{"ast": null, "code": "import { style } from '../../render/dom/style.mjs';\nimport { supportsPartialKeyframes } from '../waapi/supports/partial-keyframes.mjs';\nimport { pxValues } from '../waapi/utils/px-values.mjs';\nfunction hydrateKeyframes(element, name, keyframes, pseudoElement) {\n  if (!Array.isArray(keyframes)) {\n    keyframes = [keyframes];\n  }\n  for (let i = 0; i < keyframes.length; i++) {\n    if (keyframes[i] === null) {\n      keyframes[i] = i === 0 && !pseudoElement ? style.get(element, name) : keyframes[i - 1];\n    }\n    if (typeof keyframes[i] === \"number\" && pxValues.has(name)) {\n      keyframes[i] = keyframes[i] + \"px\";\n    }\n  }\n  if (!pseudoElement && !supportsPartialKeyframes() && keyframes.length < 2) {\n    keyframes.unshift(style.get(element, name));\n  }\n  return keyframes;\n}\nexport { hydrateKeyframes };", "map": {"version": 3, "names": ["style", "supportsPartialKeyframes", "px<PERSON><PERSON><PERSON>", "hydrateKeyframes", "element", "name", "keyframes", "pseudoElement", "Array", "isArray", "i", "length", "get", "has", "unshift"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/motion-dom/dist/es/animation/keyframes/hydrate.mjs"], "sourcesContent": ["import { style } from '../../render/dom/style.mjs';\nimport { supportsPartialKeyframes } from '../waapi/supports/partial-keyframes.mjs';\nimport { pxValues } from '../waapi/utils/px-values.mjs';\n\nfunction hydrateKeyframes(element, name, keyframes, pseudoElement) {\n    if (!Array.isArray(keyframes)) {\n        keyframes = [keyframes];\n    }\n    for (let i = 0; i < keyframes.length; i++) {\n        if (keyframes[i] === null) {\n            keyframes[i] =\n                i === 0 && !pseudoElement\n                    ? style.get(element, name)\n                    : keyframes[i - 1];\n        }\n        if (typeof keyframes[i] === \"number\" && pxValues.has(name)) {\n            keyframes[i] = keyframes[i] + \"px\";\n        }\n    }\n    if (!pseudoElement && !supportsPartialKeyframes() && keyframes.length < 2) {\n        keyframes.unshift(style.get(element, name));\n    }\n    return keyframes;\n}\n\nexport { hydrateKeyframes };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,4BAA4B;AAClD,SAASC,wBAAwB,QAAQ,yCAAyC;AAClF,SAASC,QAAQ,QAAQ,8BAA8B;AAEvD,SAASC,gBAAgBA,CAACC,OAAO,EAAEC,IAAI,EAAEC,SAAS,EAAEC,aAAa,EAAE;EAC/D,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,SAAS,CAAC,EAAE;IAC3BA,SAAS,GAAG,CAACA,SAAS,CAAC;EAC3B;EACA,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,SAAS,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;IACvC,IAAIJ,SAAS,CAACI,CAAC,CAAC,KAAK,IAAI,EAAE;MACvBJ,SAAS,CAACI,CAAC,CAAC,GACRA,CAAC,KAAK,CAAC,IAAI,CAACH,aAAa,GACnBP,KAAK,CAACY,GAAG,CAACR,OAAO,EAAEC,IAAI,CAAC,GACxBC,SAAS,CAACI,CAAC,GAAG,CAAC,CAAC;IAC9B;IACA,IAAI,OAAOJ,SAAS,CAACI,CAAC,CAAC,KAAK,QAAQ,IAAIR,QAAQ,CAACW,GAAG,CAACR,IAAI,CAAC,EAAE;MACxDC,SAAS,CAACI,CAAC,CAAC,GAAGJ,SAAS,CAACI,CAAC,CAAC,GAAG,IAAI;IACtC;EACJ;EACA,IAAI,CAACH,aAAa,IAAI,CAACN,wBAAwB,CAAC,CAAC,IAAIK,SAAS,CAACK,MAAM,GAAG,CAAC,EAAE;IACvEL,SAAS,CAACQ,OAAO,CAACd,KAAK,CAACY,GAAG,CAACR,OAAO,EAAEC,IAAI,CAAC,CAAC;EAC/C;EACA,OAAOC,SAAS;AACpB;AAEA,SAASH,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}