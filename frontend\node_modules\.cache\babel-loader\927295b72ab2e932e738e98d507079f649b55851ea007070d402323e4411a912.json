{"ast": null, "code": "import { px } from '../../../value/types/numbers/units.mjs';\nconst browserNumberValueTypes = {\n  // Border props\n  borderWidth: px,\n  borderTopWidth: px,\n  borderRightWidth: px,\n  borderBottomWidth: px,\n  borderLeftWidth: px,\n  borderRadius: px,\n  radius: px,\n  borderTopLeftRadius: px,\n  borderTopRightRadius: px,\n  borderBottomRightRadius: px,\n  borderBottomLeftRadius: px,\n  // Positioning props\n  width: px,\n  maxWidth: px,\n  height: px,\n  maxHeight: px,\n  top: px,\n  right: px,\n  bottom: px,\n  left: px,\n  // Spacing props\n  padding: px,\n  paddingTop: px,\n  paddingRight: px,\n  paddingBottom: px,\n  paddingLeft: px,\n  margin: px,\n  marginTop: px,\n  marginRight: px,\n  marginBottom: px,\n  marginLeft: px,\n  // Misc\n  backgroundPositionX: px,\n  backgroundPositionY: px\n};\nexport { browserNumberValueTypes };", "map": {"version": 3, "names": ["px", "browserNumberValueTypes", "borderWidth", "borderTopWidth", "borderRightWidth", "borderBottomWidth", "borderLeftWidth", "borderRadius", "radius", "borderTopLeftRadius", "borderTopRightRadius", "borderBottomRightRadius", "borderBottomLeftRadius", "width", "max<PERSON><PERSON><PERSON>", "height", "maxHeight", "top", "right", "bottom", "left", "padding", "paddingTop", "paddingRight", "paddingBottom", "paddingLeft", "margin", "marginTop", "marginRight", "marginBottom", "marginLeft", "backgroundPositionX", "backgroundPositionY"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/framer-motion/dist/es/render/dom/value-types/number-browser.mjs"], "sourcesContent": ["import { px } from '../../../value/types/numbers/units.mjs';\n\nconst browserNumberValueTypes = {\n    // Border props\n    borderWidth: px,\n    borderTopWidth: px,\n    borderRightWidth: px,\n    borderBottomWidth: px,\n    borderLeftWidth: px,\n    borderRadius: px,\n    radius: px,\n    borderTopLeftRadius: px,\n    borderTopRightRadius: px,\n    borderBottomRightRadius: px,\n    borderBottomLeftRadius: px,\n    // Positioning props\n    width: px,\n    maxWidth: px,\n    height: px,\n    maxHeight: px,\n    top: px,\n    right: px,\n    bottom: px,\n    left: px,\n    // Spacing props\n    padding: px,\n    paddingTop: px,\n    paddingRight: px,\n    paddingBottom: px,\n    paddingLeft: px,\n    margin: px,\n    marginTop: px,\n    marginRight: px,\n    marginBottom: px,\n    marginLeft: px,\n    // Misc\n    backgroundPositionX: px,\n    backgroundPositionY: px,\n};\n\nexport { browserNumberValueTypes };\n"], "mappings": "AAAA,SAASA,EAAE,QAAQ,wCAAwC;AAE3D,MAAMC,uBAAuB,GAAG;EAC5B;EACAC,WAAW,EAAEF,EAAE;EACfG,cAAc,EAAEH,EAAE;EAClBI,gBAAgB,EAAEJ,EAAE;EACpBK,iBAAiB,EAAEL,EAAE;EACrBM,eAAe,EAAEN,EAAE;EACnBO,YAAY,EAAEP,EAAE;EAChBQ,MAAM,EAAER,EAAE;EACVS,mBAAmB,EAAET,EAAE;EACvBU,oBAAoB,EAAEV,EAAE;EACxBW,uBAAuB,EAAEX,EAAE;EAC3BY,sBAAsB,EAAEZ,EAAE;EAC1B;EACAa,KAAK,EAAEb,EAAE;EACTc,QAAQ,EAAEd,EAAE;EACZe,MAAM,EAAEf,EAAE;EACVgB,SAAS,EAAEhB,EAAE;EACbiB,GAAG,EAAEjB,EAAE;EACPkB,KAAK,EAAElB,EAAE;EACTmB,MAAM,EAAEnB,EAAE;EACVoB,IAAI,EAAEpB,EAAE;EACR;EACAqB,OAAO,EAAErB,EAAE;EACXsB,UAAU,EAAEtB,EAAE;EACduB,YAAY,EAAEvB,EAAE;EAChBwB,aAAa,EAAExB,EAAE;EACjByB,WAAW,EAAEzB,EAAE;EACf0B,MAAM,EAAE1B,EAAE;EACV2B,SAAS,EAAE3B,EAAE;EACb4B,WAAW,EAAE5B,EAAE;EACf6B,YAAY,EAAE7B,EAAE;EAChB8B,UAAU,EAAE9B,EAAE;EACd;EACA+B,mBAAmB,EAAE/B,EAAE;EACvBgC,mBAAmB,EAAEhC;AACzB,CAAC;AAED,SAASC,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}