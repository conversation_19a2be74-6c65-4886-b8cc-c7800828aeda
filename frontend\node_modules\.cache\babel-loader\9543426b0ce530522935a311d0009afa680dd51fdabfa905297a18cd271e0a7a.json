{"ast": null, "code": "function resolveElements(elementOrSelector, scope, selectorCache) {\n  if (elementOrSelector instanceof EventTarget) {\n    return [elementOrSelector];\n  } else if (typeof elementOrSelector === \"string\") {\n    let root = document;\n    if (scope) {\n      root = scope.current;\n    }\n    const elements = selectorCache?.[elementOrSelector] ?? root.querySelectorAll(elementOrSelector);\n    return elements ? Array.from(elements) : [];\n  }\n  return Array.from(elementOrSelector);\n}\nexport { resolveElements };", "map": {"version": 3, "names": ["resolveElements", "elementOrSelector", "scope", "selectorCache", "EventTarget", "root", "document", "current", "elements", "querySelectorAll", "Array", "from"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/motion-dom/dist/es/utils/resolve-elements.mjs"], "sourcesContent": ["function resolveElements(elementOrSelector, scope, selectorCache) {\n    if (elementOrSelector instanceof EventTarget) {\n        return [elementOrSelector];\n    }\n    else if (typeof elementOrSelector === \"string\") {\n        let root = document;\n        if (scope) {\n            root = scope.current;\n        }\n        const elements = selectorCache?.[elementOrSelector] ??\n            root.querySelectorAll(elementOrSelector);\n        return elements ? Array.from(elements) : [];\n    }\n    return Array.from(elementOrSelector);\n}\n\nexport { resolveElements };\n"], "mappings": "AAAA,SAASA,eAAeA,CAACC,iBAAiB,EAAEC,KAAK,EAAEC,aAAa,EAAE;EAC9D,IAAIF,iBAAiB,YAAYG,WAAW,EAAE;IAC1C,OAAO,CAACH,iBAAiB,CAAC;EAC9B,CAAC,MACI,IAAI,OAAOA,iBAAiB,KAAK,QAAQ,EAAE;IAC5C,IAAII,IAAI,GAAGC,QAAQ;IACnB,IAAIJ,KAAK,EAAE;MACPG,IAAI,GAAGH,KAAK,CAACK,OAAO;IACxB;IACA,MAAMC,QAAQ,GAAGL,aAAa,GAAGF,iBAAiB,CAAC,IAC/CI,IAAI,CAACI,gBAAgB,CAACR,iBAAiB,CAAC;IAC5C,OAAOO,QAAQ,GAAGE,KAAK,CAACC,IAAI,CAACH,QAAQ,CAAC,GAAG,EAAE;EAC/C;EACA,OAAOE,KAAK,CAACC,IAAI,CAACV,iBAAiB,CAAC;AACxC;AAEA,SAASD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}