{"ast": null, "code": "export { default } from \"./memoize.js\";", "map": {"version": 3, "names": ["default"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/@mui/system/esm/memoize/index.js"], "sourcesContent": ["export { default } from \"./memoize.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}