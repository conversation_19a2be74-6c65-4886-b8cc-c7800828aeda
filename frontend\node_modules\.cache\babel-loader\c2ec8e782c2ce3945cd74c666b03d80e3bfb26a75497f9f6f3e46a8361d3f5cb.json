{"ast": null, "code": "import clsx from 'clsx';\n\n// Brought from [Base UI](https://github.com/mui/base-ui/blob/master/packages/react/src/merge-props/mergeProps.ts#L119)\n// Use it directly from Base UI once it's a package dependency.\nfunction isEventHandler(key, value) {\n  // This approach is more efficient than using a regex.\n  const thirdCharCode = key.charCodeAt(2);\n  return key[0] === 'o' && key[1] === 'n' && thirdCharCode >= 65 /* A */ && thirdCharCode <= 90 /* Z */ && typeof value === 'function';\n}\nexport default function mergeSlotProps(externalSlotProps, defaultSlotProps) {\n  if (!externalSlotProps) {\n    return defaultSlotProps;\n  }\n  function extractHandlers(externalSlotPropsValue, defaultSlotPropsValue) {\n    const handlers = {};\n    Object.keys(defaultSlotPropsValue).forEach(key => {\n      if (isEventHandler(key, defaultSlotPropsValue[key]) && typeof externalSlotPropsValue[key] === 'function') {\n        // only compose the handlers if both default and external slot props match the event handler\n        handlers[key] = (...args) => {\n          externalSlotPropsValue[key](...args);\n          defaultSlotPropsValue[key](...args);\n        };\n      }\n    });\n    return handlers;\n  }\n  if (typeof externalSlotProps === 'function' || typeof defaultSlotProps === 'function') {\n    return ownerState => {\n      const defaultSlotPropsValue = typeof defaultSlotProps === 'function' ? defaultSlotProps(ownerState) : defaultSlotProps;\n      const externalSlotPropsValue = typeof externalSlotProps === 'function' ? externalSlotProps({\n        ...ownerState,\n        ...defaultSlotPropsValue\n      }) : externalSlotProps;\n      const className = clsx(ownerState?.className, defaultSlotPropsValue?.className, externalSlotPropsValue?.className);\n      const handlers = extractHandlers(externalSlotPropsValue, defaultSlotPropsValue);\n      return {\n        ...defaultSlotPropsValue,\n        ...externalSlotPropsValue,\n        ...handlers,\n        ...(!!className && {\n          className\n        }),\n        ...(defaultSlotPropsValue?.style && externalSlotPropsValue?.style && {\n          style: {\n            ...defaultSlotPropsValue.style,\n            ...externalSlotPropsValue.style\n          }\n        }),\n        ...(defaultSlotPropsValue?.sx && externalSlotPropsValue?.sx && {\n          sx: [...(Array.isArray(defaultSlotPropsValue.sx) ? defaultSlotPropsValue.sx : [defaultSlotPropsValue.sx]), ...(Array.isArray(externalSlotPropsValue.sx) ? externalSlotPropsValue.sx : [externalSlotPropsValue.sx])]\n        })\n      };\n    };\n  }\n  const typedDefaultSlotProps = defaultSlotProps;\n  const handlers = extractHandlers(externalSlotProps, typedDefaultSlotProps);\n  const className = clsx(typedDefaultSlotProps?.className, externalSlotProps?.className);\n  return {\n    ...defaultSlotProps,\n    ...externalSlotProps,\n    ...handlers,\n    ...(!!className && {\n      className\n    }),\n    ...(typedDefaultSlotProps?.style && externalSlotProps?.style && {\n      style: {\n        ...typedDefaultSlotProps.style,\n        ...externalSlotProps.style\n      }\n    }),\n    ...(typedDefaultSlotProps?.sx && externalSlotProps?.sx && {\n      sx: [...(Array.isArray(typedDefaultSlotProps.sx) ? typedDefaultSlotProps.sx : [typedDefaultSlotProps.sx]), ...(Array.isArray(externalSlotProps.sx) ? externalSlotProps.sx : [externalSlotProps.sx])]\n    })\n  };\n}", "map": {"version": 3, "names": ["clsx", "isEventHandler", "key", "value", "thirdCharCode", "charCodeAt", "mergeSlotProps", "externalSlotProps", "defaultSlotProps", "extractHandlers", "externalSlotPropsValue", "defaultSlotPropsValue", "handlers", "Object", "keys", "for<PERSON>ach", "args", "ownerState", "className", "style", "sx", "Array", "isArray", "typedDefaultSlotProps"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/@mui/material/esm/utils/mergeSlotProps.js"], "sourcesContent": ["import clsx from 'clsx';\n\n// Brought from [Base UI](https://github.com/mui/base-ui/blob/master/packages/react/src/merge-props/mergeProps.ts#L119)\n// Use it directly from Base UI once it's a package dependency.\nfunction isEventHandler(key, value) {\n  // This approach is more efficient than using a regex.\n  const thirdCharCode = key.charCodeAt(2);\n  return key[0] === 'o' && key[1] === 'n' && thirdCharCode >= 65 /* A */ && thirdCharCode <= 90 /* Z */ && typeof value === 'function';\n}\nexport default function mergeSlotProps(externalSlotProps, defaultSlotProps) {\n  if (!externalSlotProps) {\n    return defaultSlotProps;\n  }\n  function extractHandlers(externalSlotPropsValue, defaultSlotPropsValue) {\n    const handlers = {};\n    Object.keys(defaultSlotPropsValue).forEach(key => {\n      if (isEventHandler(key, defaultSlotPropsValue[key]) && typeof externalSlotPropsValue[key] === 'function') {\n        // only compose the handlers if both default and external slot props match the event handler\n        handlers[key] = (...args) => {\n          externalSlotPropsValue[key](...args);\n          defaultSlotPropsValue[key](...args);\n        };\n      }\n    });\n    return handlers;\n  }\n  if (typeof externalSlotProps === 'function' || typeof defaultSlotProps === 'function') {\n    return ownerState => {\n      const defaultSlotPropsValue = typeof defaultSlotProps === 'function' ? defaultSlotProps(ownerState) : defaultSlotProps;\n      const externalSlotPropsValue = typeof externalSlotProps === 'function' ? externalSlotProps({\n        ...ownerState,\n        ...defaultSlotPropsValue\n      }) : externalSlotProps;\n      const className = clsx(ownerState?.className, defaultSlotPropsValue?.className, externalSlotPropsValue?.className);\n      const handlers = extractHandlers(externalSlotPropsValue, defaultSlotPropsValue);\n      return {\n        ...defaultSlotPropsValue,\n        ...externalSlotPropsValue,\n        ...handlers,\n        ...(!!className && {\n          className\n        }),\n        ...(defaultSlotPropsValue?.style && externalSlotPropsValue?.style && {\n          style: {\n            ...defaultSlotPropsValue.style,\n            ...externalSlotPropsValue.style\n          }\n        }),\n        ...(defaultSlotPropsValue?.sx && externalSlotPropsValue?.sx && {\n          sx: [...(Array.isArray(defaultSlotPropsValue.sx) ? defaultSlotPropsValue.sx : [defaultSlotPropsValue.sx]), ...(Array.isArray(externalSlotPropsValue.sx) ? externalSlotPropsValue.sx : [externalSlotPropsValue.sx])]\n        })\n      };\n    };\n  }\n  const typedDefaultSlotProps = defaultSlotProps;\n  const handlers = extractHandlers(externalSlotProps, typedDefaultSlotProps);\n  const className = clsx(typedDefaultSlotProps?.className, externalSlotProps?.className);\n  return {\n    ...defaultSlotProps,\n    ...externalSlotProps,\n    ...handlers,\n    ...(!!className && {\n      className\n    }),\n    ...(typedDefaultSlotProps?.style && externalSlotProps?.style && {\n      style: {\n        ...typedDefaultSlotProps.style,\n        ...externalSlotProps.style\n      }\n    }),\n    ...(typedDefaultSlotProps?.sx && externalSlotProps?.sx && {\n      sx: [...(Array.isArray(typedDefaultSlotProps.sx) ? typedDefaultSlotProps.sx : [typedDefaultSlotProps.sx]), ...(Array.isArray(externalSlotProps.sx) ? externalSlotProps.sx : [externalSlotProps.sx])]\n    })\n  };\n}"], "mappings": "AAAA,OAAOA,IAAI,MAAM,MAAM;;AAEvB;AACA;AACA,SAASC,cAAcA,CAACC,GAAG,EAAEC,KAAK,EAAE;EAClC;EACA,MAAMC,aAAa,GAAGF,GAAG,CAACG,UAAU,CAAC,CAAC,CAAC;EACvC,OAAOH,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIE,aAAa,IAAI,EAAE,CAAC,WAAWA,aAAa,IAAI,EAAE,CAAC,WAAW,OAAOD,KAAK,KAAK,UAAU;AACtI;AACA,eAAe,SAASG,cAAcA,CAACC,iBAAiB,EAAEC,gBAAgB,EAAE;EAC1E,IAAI,CAACD,iBAAiB,EAAE;IACtB,OAAOC,gBAAgB;EACzB;EACA,SAASC,eAAeA,CAACC,sBAAsB,EAAEC,qBAAqB,EAAE;IACtE,MAAMC,QAAQ,GAAG,CAAC,CAAC;IACnBC,MAAM,CAACC,IAAI,CAACH,qBAAqB,CAAC,CAACI,OAAO,CAACb,GAAG,IAAI;MAChD,IAAID,cAAc,CAACC,GAAG,EAAES,qBAAqB,CAACT,GAAG,CAAC,CAAC,IAAI,OAAOQ,sBAAsB,CAACR,GAAG,CAAC,KAAK,UAAU,EAAE;QACxG;QACAU,QAAQ,CAACV,GAAG,CAAC,GAAG,CAAC,GAAGc,IAAI,KAAK;UAC3BN,sBAAsB,CAACR,GAAG,CAAC,CAAC,GAAGc,IAAI,CAAC;UACpCL,qBAAqB,CAACT,GAAG,CAAC,CAAC,GAAGc,IAAI,CAAC;QACrC,CAAC;MACH;IACF,CAAC,CAAC;IACF,OAAOJ,QAAQ;EACjB;EACA,IAAI,OAAOL,iBAAiB,KAAK,UAAU,IAAI,OAAOC,gBAAgB,KAAK,UAAU,EAAE;IACrF,OAAOS,UAAU,IAAI;MACnB,MAAMN,qBAAqB,GAAG,OAAOH,gBAAgB,KAAK,UAAU,GAAGA,gBAAgB,CAACS,UAAU,CAAC,GAAGT,gBAAgB;MACtH,MAAME,sBAAsB,GAAG,OAAOH,iBAAiB,KAAK,UAAU,GAAGA,iBAAiB,CAAC;QACzF,GAAGU,UAAU;QACb,GAAGN;MACL,CAAC,CAAC,GAAGJ,iBAAiB;MACtB,MAAMW,SAAS,GAAGlB,IAAI,CAACiB,UAAU,EAAEC,SAAS,EAAEP,qBAAqB,EAAEO,SAAS,EAAER,sBAAsB,EAAEQ,SAAS,CAAC;MAClH,MAAMN,QAAQ,GAAGH,eAAe,CAACC,sBAAsB,EAAEC,qBAAqB,CAAC;MAC/E,OAAO;QACL,GAAGA,qBAAqB;QACxB,GAAGD,sBAAsB;QACzB,GAAGE,QAAQ;QACX,IAAI,CAAC,CAACM,SAAS,IAAI;UACjBA;QACF,CAAC,CAAC;QACF,IAAIP,qBAAqB,EAAEQ,KAAK,IAAIT,sBAAsB,EAAES,KAAK,IAAI;UACnEA,KAAK,EAAE;YACL,GAAGR,qBAAqB,CAACQ,KAAK;YAC9B,GAAGT,sBAAsB,CAACS;UAC5B;QACF,CAAC,CAAC;QACF,IAAIR,qBAAqB,EAAES,EAAE,IAAIV,sBAAsB,EAAEU,EAAE,IAAI;UAC7DA,EAAE,EAAE,CAAC,IAAIC,KAAK,CAACC,OAAO,CAACX,qBAAqB,CAACS,EAAE,CAAC,GAAGT,qBAAqB,CAACS,EAAE,GAAG,CAACT,qBAAqB,CAACS,EAAE,CAAC,CAAC,EAAE,IAAIC,KAAK,CAACC,OAAO,CAACZ,sBAAsB,CAACU,EAAE,CAAC,GAAGV,sBAAsB,CAACU,EAAE,GAAG,CAACV,sBAAsB,CAACU,EAAE,CAAC,CAAC;QACpN,CAAC;MACH,CAAC;IACH,CAAC;EACH;EACA,MAAMG,qBAAqB,GAAGf,gBAAgB;EAC9C,MAAMI,QAAQ,GAAGH,eAAe,CAACF,iBAAiB,EAAEgB,qBAAqB,CAAC;EAC1E,MAAML,SAAS,GAAGlB,IAAI,CAACuB,qBAAqB,EAAEL,SAAS,EAAEX,iBAAiB,EAAEW,SAAS,CAAC;EACtF,OAAO;IACL,GAAGV,gBAAgB;IACnB,GAAGD,iBAAiB;IACpB,GAAGK,QAAQ;IACX,IAAI,CAAC,CAACM,SAAS,IAAI;MACjBA;IACF,CAAC,CAAC;IACF,IAAIK,qBAAqB,EAAEJ,KAAK,IAAIZ,iBAAiB,EAAEY,KAAK,IAAI;MAC9DA,KAAK,EAAE;QACL,GAAGI,qBAAqB,CAACJ,KAAK;QAC9B,GAAGZ,iBAAiB,CAACY;MACvB;IACF,CAAC,CAAC;IACF,IAAII,qBAAqB,EAAEH,EAAE,IAAIb,iBAAiB,EAAEa,EAAE,IAAI;MACxDA,EAAE,EAAE,CAAC,IAAIC,KAAK,CAACC,OAAO,CAACC,qBAAqB,CAACH,EAAE,CAAC,GAAGG,qBAAqB,CAACH,EAAE,GAAG,CAACG,qBAAqB,CAACH,EAAE,CAAC,CAAC,EAAE,IAAIC,KAAK,CAACC,OAAO,CAACf,iBAAiB,CAACa,EAAE,CAAC,GAAGb,iBAAiB,CAACa,EAAE,GAAG,CAACb,iBAAiB,CAACa,EAAE,CAAC,CAAC;IACrM,CAAC;EACH,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}