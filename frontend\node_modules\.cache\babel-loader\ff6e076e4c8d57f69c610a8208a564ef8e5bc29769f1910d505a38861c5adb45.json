{"ast": null, "code": "import * as React from 'react';\nvar syncFallback = function syncFallback(create) {\n  return create();\n};\nvar useInsertionEffect = React['useInsertion' + 'Effect'] ? React['useInsertion' + 'Effect'] : false;\nvar useInsertionEffectAlwaysWithSyncFallback = useInsertionEffect || syncFallback;\nvar useInsertionEffectWithLayoutFallback = useInsertionEffect || React.useLayoutEffect;\nexport { useInsertionEffectAlwaysWithSyncFallback, useInsertionEffectWithLayoutFallback };", "map": {"version": 3, "names": ["React", "syncFallback", "create", "useInsertionEffect", "useInsertionEffectAlwaysWithSyncFallback", "useInsertionEffectWithLayoutFallback", "useLayoutEffect"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/@emotion/use-insertion-effect-with-fallbacks/dist/emotion-use-insertion-effect-with-fallbacks.browser.esm.js"], "sourcesContent": ["import * as React from 'react';\n\nvar syncFallback = function syncFallback(create) {\n  return create();\n};\n\nvar useInsertionEffect = React['useInsertion' + 'Effect'] ? React['useInsertion' + 'Effect'] : false;\nvar useInsertionEffectAlwaysWithSyncFallback = useInsertionEffect || syncFallback;\nvar useInsertionEffectWithLayoutFallback = useInsertionEffect || React.useLayoutEffect;\n\nexport { useInsertionEffectAlwaysWithSyncFallback, useInsertionEffectWithLayoutFallback };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,MAAM,EAAE;EAC/C,OAAOA,MAAM,CAAC,CAAC;AACjB,CAAC;AAED,IAAIC,kBAAkB,GAAGH,KAAK,CAAC,cAAc,GAAG,QAAQ,CAAC,GAAGA,KAAK,CAAC,cAAc,GAAG,QAAQ,CAAC,GAAG,KAAK;AACpG,IAAII,wCAAwC,GAAGD,kBAAkB,IAAIF,YAAY;AACjF,IAAII,oCAAoC,GAAGF,kBAAkB,IAAIH,KAAK,CAACM,eAAe;AAEtF,SAASF,wCAAwC,EAAEC,oCAAoC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}