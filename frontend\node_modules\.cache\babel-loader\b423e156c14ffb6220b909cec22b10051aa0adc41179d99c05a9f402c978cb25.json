{"ast": null, "code": "'use client';\n\n// @inheritedComponent Tooltip\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { emphasize } from '@mui/system/colorManipulator';\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport Fab from \"../Fab/index.js\";\nimport Tooltip from \"../Tooltip/index.js\";\nimport capitalize from \"../utils/capitalize.js\";\nimport speedDialActionClasses, { getSpeedDialActionUtilityClass } from \"./speedDialActionClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { mergeSlotProps } from \"../utils/index.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    open,\n    tooltipPlacement,\n    classes\n  } = ownerState;\n  const slots = {\n    fab: ['fab', !open && 'fabClosed'],\n    staticTooltip: ['staticTooltip', `tooltipPlacement${capitalize(tooltipPlacement)}`, !open && 'staticTooltipClosed'],\n    staticTooltipLabel: ['staticTooltipLabel']\n  };\n  return composeClasses(slots, getSpeedDialActionUtilityClass, classes);\n};\nconst SpeedDialActionFab = styled(Fab, {\n  name: 'MuiSpeedDialAction',\n  slot: 'Fab',\n  skipVariantsResolver: false,\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.fab, !ownerState.open && styles.fabClosed];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  margin: 8,\n  color: (theme.vars || theme).palette.text.secondary,\n  backgroundColor: (theme.vars || theme).palette.background.paper,\n  '&:hover': {\n    backgroundColor: theme.vars ? theme.vars.palette.SpeedDialAction.fabHoverBg : emphasize(theme.palette.background.paper, 0.15)\n  },\n  transition: `${theme.transitions.create('transform', {\n    duration: theme.transitions.duration.shorter\n  })}, opacity 0.8s`,\n  opacity: 1,\n  variants: [{\n    props: ({\n      ownerState\n    }) => !ownerState.open,\n    style: {\n      opacity: 0,\n      transform: 'scale(0)'\n    }\n  }]\n})));\nconst SpeedDialActionStaticTooltip = styled('span', {\n  name: 'MuiSpeedDialAction',\n  slot: 'StaticTooltip',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.staticTooltip, !ownerState.open && styles.staticTooltipClosed, styles[`tooltipPlacement${capitalize(ownerState.tooltipPlacement)}`]];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  position: 'relative',\n  display: 'flex',\n  alignItems: 'center',\n  [`& .${speedDialActionClasses.staticTooltipLabel}`]: {\n    transition: theme.transitions.create(['transform', 'opacity'], {\n      duration: theme.transitions.duration.shorter\n    }),\n    opacity: 1\n  },\n  variants: [{\n    props: ({\n      ownerState\n    }) => !ownerState.open,\n    style: {\n      [`& .${speedDialActionClasses.staticTooltipLabel}`]: {\n        opacity: 0,\n        transform: 'scale(0.5)'\n      }\n    }\n  }, {\n    props: {\n      tooltipPlacement: 'left'\n    },\n    style: {\n      [`& .${speedDialActionClasses.staticTooltipLabel}`]: {\n        transformOrigin: '100% 50%',\n        right: '100%',\n        marginRight: 8\n      }\n    }\n  }, {\n    props: {\n      tooltipPlacement: 'right'\n    },\n    style: {\n      [`& .${speedDialActionClasses.staticTooltipLabel}`]: {\n        transformOrigin: '0% 50%',\n        left: '100%',\n        marginLeft: 8\n      }\n    }\n  }]\n})));\nconst SpeedDialActionStaticTooltipLabel = styled('span', {\n  name: 'MuiSpeedDialAction',\n  slot: 'StaticTooltipLabel',\n  overridesResolver: (props, styles) => styles.staticTooltipLabel\n})(memoTheme(({\n  theme\n}) => ({\n  position: 'absolute',\n  ...theme.typography.body1,\n  backgroundColor: (theme.vars || theme).palette.background.paper,\n  borderRadius: (theme.vars || theme).shape.borderRadius,\n  boxShadow: (theme.vars || theme).shadows[1],\n  color: (theme.vars || theme).palette.text.secondary,\n  padding: '4px 16px',\n  wordBreak: 'keep-all'\n})));\nconst SpeedDialAction = /*#__PURE__*/React.forwardRef(function SpeedDialAction(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiSpeedDialAction'\n  });\n  const {\n    className,\n    delay = 0,\n    FabProps = {},\n    icon,\n    id,\n    open,\n    TooltipClasses,\n    tooltipOpen: tooltipOpenProp = false,\n    tooltipPlacement = 'left',\n    tooltipTitle,\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    tooltipPlacement\n  };\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = {\n    slots,\n    slotProps: {\n      fab: FabProps,\n      ...slotProps,\n      tooltip: mergeSlotProps(typeof slotProps.tooltip === 'function' ? slotProps.tooltip(ownerState) : slotProps.tooltip, {\n        title: tooltipTitle,\n        open: tooltipOpenProp,\n        placement: tooltipPlacement,\n        classes: TooltipClasses\n      })\n    }\n  };\n  const [tooltipOpen, setTooltipOpen] = React.useState(externalForwardedProps.slotProps.tooltip?.open);\n  const handleTooltipClose = () => {\n    setTooltipOpen(false);\n  };\n  const handleTooltipOpen = () => {\n    setTooltipOpen(true);\n  };\n  const transitionStyle = {\n    transitionDelay: `${delay}ms`\n  };\n  const [FabSlot, fabSlotProps] = useSlot('fab', {\n    elementType: SpeedDialActionFab,\n    externalForwardedProps,\n    ownerState,\n    shouldForwardComponentProp: true,\n    className: clsx(classes.fab, className),\n    additionalProps: {\n      style: transitionStyle,\n      tabIndex: -1,\n      role: 'menuitem',\n      size: 'small'\n    }\n  });\n  const [TooltipSlot, tooltipSlotProps] = useSlot('tooltip', {\n    elementType: Tooltip,\n    externalForwardedProps,\n    shouldForwardComponentProp: true,\n    ref,\n    additionalProps: {\n      id\n    },\n    ownerState,\n    getSlotProps: handlers => ({\n      ...handlers,\n      onClose: event => {\n        handlers.onClose?.(event);\n        handleTooltipClose();\n      },\n      onOpen: event => {\n        handlers.onOpen?.(event);\n        handleTooltipOpen();\n      }\n    })\n  });\n  const [StaticTooltipSlot, staticTooltipSlotProps] = useSlot('staticTooltip', {\n    elementType: SpeedDialActionStaticTooltip,\n    externalForwardedProps,\n    ownerState,\n    ref,\n    className: classes.staticTooltip,\n    additionalProps: {\n      id\n    }\n  });\n  const [StaticTooltipLabelSlot, staticTooltipLabelSlotProps] = useSlot('staticTooltipLabel', {\n    elementType: SpeedDialActionStaticTooltipLabel,\n    externalForwardedProps,\n    ownerState,\n    className: classes.staticTooltipLabel,\n    additionalProps: {\n      style: transitionStyle,\n      id: `${id}-label`\n    }\n  });\n  const fab = /*#__PURE__*/_jsx(FabSlot, {\n    ...fabSlotProps,\n    children: icon\n  });\n  if (tooltipSlotProps.open) {\n    return /*#__PURE__*/_jsxs(StaticTooltipSlot, {\n      ...staticTooltipSlotProps,\n      ...other,\n      children: [/*#__PURE__*/_jsx(StaticTooltipLabelSlot, {\n        ...staticTooltipLabelSlotProps,\n        children: tooltipSlotProps.title\n      }), /*#__PURE__*/React.cloneElement(fab, {\n        'aria-labelledby': `${id}-label`\n      })]\n    });\n  }\n  if (!open && tooltipOpen) {\n    setTooltipOpen(false);\n  }\n  return /*#__PURE__*/_jsx(TooltipSlot, {\n    ...tooltipSlotProps,\n    title: tooltipSlotProps.title,\n    open: open && tooltipOpen,\n    placement: tooltipSlotProps.placement,\n    classes: tooltipSlotProps.classes,\n    ...other,\n    children: fab\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? SpeedDialAction.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Adds a transition delay, to allow a series of SpeedDialActions to be animated.\n   * @default 0\n   */\n  delay: PropTypes.number,\n  /**\n   * Props applied to the [`Fab`](https://mui.com/material-ui/api/fab/) component.\n   * @default {}\n   * @deprecated Use `slotProps.fab` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  FabProps: PropTypes.object,\n  /**\n   * The icon to display in the SpeedDial Fab.\n   */\n  icon: PropTypes.node,\n  /**\n   * This prop is used to help implement the accessibility logic.\n   * If you don't provide this prop. It falls back to a randomly generated id.\n   */\n  id: PropTypes.string,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    fab: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    staticTooltip: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    staticTooltipLabel: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    tooltip: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    fab: PropTypes.elementType,\n    staticTooltip: PropTypes.elementType,\n    staticTooltipLabel: PropTypes.elementType,\n    tooltip: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * `classes` prop applied to the [`Tooltip`](https://mui.com/material-ui/api/tooltip/) element.\n   * @deprecated Use `slotProps.tooltip.classes` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  TooltipClasses: PropTypes.object,\n  /**\n   * Make the tooltip always visible when the SpeedDial is open.\n   * @default false\n   * @deprecated Use `slotProps.tooltip.open` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  tooltipOpen: PropTypes.bool,\n  /**\n   * Placement of the tooltip.\n   * @default 'left'\n   * @deprecated Use `slotProps.tooltip.placement` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  tooltipPlacement: PropTypes.oneOf(['auto-end', 'auto-start', 'auto', 'bottom-end', 'bottom-start', 'bottom', 'left-end', 'left-start', 'left', 'right-end', 'right-start', 'right', 'top-end', 'top-start', 'top']),\n  /**\n   * Label to display in the tooltip.\n   * @deprecated Use `slotProps.tooltip.title` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  tooltipTitle: PropTypes.node\n} : void 0;\nexport default SpeedDialAction;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "composeClasses", "emphasize", "styled", "memoTheme", "useDefaultProps", "Fab", "<PERSON><PERSON><PERSON>", "capitalize", "speedDialActionClasses", "getSpeedDialActionUtilityClass", "useSlot", "mergeSlotProps", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "open", "tooltipPlacement", "classes", "slots", "fab", "staticTooltip", "staticTooltipLabel", "SpeedDialActionFab", "name", "slot", "skipVariantsResolver", "overridesResolver", "props", "styles", "fabClosed", "theme", "margin", "color", "vars", "palette", "text", "secondary", "backgroundColor", "background", "paper", "SpeedDialAction", "fabHoverBg", "transition", "transitions", "create", "duration", "shorter", "opacity", "variants", "style", "transform", "SpeedDialActionStaticTooltip", "staticTooltipClosed", "position", "display", "alignItems", "transform<PERSON><PERSON>in", "right", "marginRight", "left", "marginLeft", "SpeedDialActionStaticTooltipLabel", "typography", "body1", "borderRadius", "shape", "boxShadow", "shadows", "padding", "wordBreak", "forwardRef", "inProps", "ref", "className", "delay", "FabProps", "icon", "id", "TooltipClasses", "tooltipOpen", "tooltipOpenProp", "tooltipTitle", "slotProps", "other", "externalForwardedProps", "tooltip", "title", "placement", "setTooltipOpen", "useState", "handleTooltipClose", "handleTooltipOpen", "transitionStyle", "transitionDelay", "FabSlot", "fabSlotProps", "elementType", "shouldForwardComponentProp", "additionalProps", "tabIndex", "role", "size", "TooltipSlot", "tooltipSlotProps", "getSlotProps", "handlers", "onClose", "event", "onOpen", "StaticTooltipSlot", "staticTooltipSlotProps", "StaticTooltipLabelSlot", "staticTooltipLabelSlotProps", "children", "cloneElement", "process", "env", "NODE_ENV", "propTypes", "object", "string", "number", "node", "bool", "oneOfType", "func", "sx", "arrayOf", "oneOf"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/@mui/material/esm/SpeedDialAction/SpeedDialAction.js"], "sourcesContent": ["'use client';\n\n// @inheritedComponent Tooltip\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { emphasize } from '@mui/system/colorManipulator';\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport Fab from \"../Fab/index.js\";\nimport Tooltip from \"../Tooltip/index.js\";\nimport capitalize from \"../utils/capitalize.js\";\nimport speedDialActionClasses, { getSpeedDialActionUtilityClass } from \"./speedDialActionClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { mergeSlotProps } from \"../utils/index.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    open,\n    tooltipPlacement,\n    classes\n  } = ownerState;\n  const slots = {\n    fab: ['fab', !open && 'fabClosed'],\n    staticTooltip: ['staticTooltip', `tooltipPlacement${capitalize(tooltipPlacement)}`, !open && 'staticTooltipClosed'],\n    staticTooltipLabel: ['staticTooltipLabel']\n  };\n  return composeClasses(slots, getSpeedDialActionUtilityClass, classes);\n};\nconst SpeedDialActionFab = styled(Fab, {\n  name: 'MuiSpeedDialAction',\n  slot: 'Fab',\n  skipVariantsResolver: false,\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.fab, !ownerState.open && styles.fabClosed];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  margin: 8,\n  color: (theme.vars || theme).palette.text.secondary,\n  backgroundColor: (theme.vars || theme).palette.background.paper,\n  '&:hover': {\n    backgroundColor: theme.vars ? theme.vars.palette.SpeedDialAction.fabHoverBg : emphasize(theme.palette.background.paper, 0.15)\n  },\n  transition: `${theme.transitions.create('transform', {\n    duration: theme.transitions.duration.shorter\n  })}, opacity 0.8s`,\n  opacity: 1,\n  variants: [{\n    props: ({\n      ownerState\n    }) => !ownerState.open,\n    style: {\n      opacity: 0,\n      transform: 'scale(0)'\n    }\n  }]\n})));\nconst SpeedDialActionStaticTooltip = styled('span', {\n  name: 'MuiSpeedDialAction',\n  slot: 'StaticTooltip',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.staticTooltip, !ownerState.open && styles.staticTooltipClosed, styles[`tooltipPlacement${capitalize(ownerState.tooltipPlacement)}`]];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  position: 'relative',\n  display: 'flex',\n  alignItems: 'center',\n  [`& .${speedDialActionClasses.staticTooltipLabel}`]: {\n    transition: theme.transitions.create(['transform', 'opacity'], {\n      duration: theme.transitions.duration.shorter\n    }),\n    opacity: 1\n  },\n  variants: [{\n    props: ({\n      ownerState\n    }) => !ownerState.open,\n    style: {\n      [`& .${speedDialActionClasses.staticTooltipLabel}`]: {\n        opacity: 0,\n        transform: 'scale(0.5)'\n      }\n    }\n  }, {\n    props: {\n      tooltipPlacement: 'left'\n    },\n    style: {\n      [`& .${speedDialActionClasses.staticTooltipLabel}`]: {\n        transformOrigin: '100% 50%',\n        right: '100%',\n        marginRight: 8\n      }\n    }\n  }, {\n    props: {\n      tooltipPlacement: 'right'\n    },\n    style: {\n      [`& .${speedDialActionClasses.staticTooltipLabel}`]: {\n        transformOrigin: '0% 50%',\n        left: '100%',\n        marginLeft: 8\n      }\n    }\n  }]\n})));\nconst SpeedDialActionStaticTooltipLabel = styled('span', {\n  name: 'MuiSpeedDialAction',\n  slot: 'StaticTooltipLabel',\n  overridesResolver: (props, styles) => styles.staticTooltipLabel\n})(memoTheme(({\n  theme\n}) => ({\n  position: 'absolute',\n  ...theme.typography.body1,\n  backgroundColor: (theme.vars || theme).palette.background.paper,\n  borderRadius: (theme.vars || theme).shape.borderRadius,\n  boxShadow: (theme.vars || theme).shadows[1],\n  color: (theme.vars || theme).palette.text.secondary,\n  padding: '4px 16px',\n  wordBreak: 'keep-all'\n})));\nconst SpeedDialAction = /*#__PURE__*/React.forwardRef(function SpeedDialAction(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiSpeedDialAction'\n  });\n  const {\n    className,\n    delay = 0,\n    FabProps = {},\n    icon,\n    id,\n    open,\n    TooltipClasses,\n    tooltipOpen: tooltipOpenProp = false,\n    tooltipPlacement = 'left',\n    tooltipTitle,\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    tooltipPlacement\n  };\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = {\n    slots,\n    slotProps: {\n      fab: FabProps,\n      ...slotProps,\n      tooltip: mergeSlotProps(typeof slotProps.tooltip === 'function' ? slotProps.tooltip(ownerState) : slotProps.tooltip, {\n        title: tooltipTitle,\n        open: tooltipOpenProp,\n        placement: tooltipPlacement,\n        classes: TooltipClasses\n      })\n    }\n  };\n  const [tooltipOpen, setTooltipOpen] = React.useState(externalForwardedProps.slotProps.tooltip?.open);\n  const handleTooltipClose = () => {\n    setTooltipOpen(false);\n  };\n  const handleTooltipOpen = () => {\n    setTooltipOpen(true);\n  };\n  const transitionStyle = {\n    transitionDelay: `${delay}ms`\n  };\n  const [FabSlot, fabSlotProps] = useSlot('fab', {\n    elementType: SpeedDialActionFab,\n    externalForwardedProps,\n    ownerState,\n    shouldForwardComponentProp: true,\n    className: clsx(classes.fab, className),\n    additionalProps: {\n      style: transitionStyle,\n      tabIndex: -1,\n      role: 'menuitem',\n      size: 'small'\n    }\n  });\n  const [TooltipSlot, tooltipSlotProps] = useSlot('tooltip', {\n    elementType: Tooltip,\n    externalForwardedProps,\n    shouldForwardComponentProp: true,\n    ref,\n    additionalProps: {\n      id\n    },\n    ownerState,\n    getSlotProps: handlers => ({\n      ...handlers,\n      onClose: event => {\n        handlers.onClose?.(event);\n        handleTooltipClose();\n      },\n      onOpen: event => {\n        handlers.onOpen?.(event);\n        handleTooltipOpen();\n      }\n    })\n  });\n  const [StaticTooltipSlot, staticTooltipSlotProps] = useSlot('staticTooltip', {\n    elementType: SpeedDialActionStaticTooltip,\n    externalForwardedProps,\n    ownerState,\n    ref,\n    className: classes.staticTooltip,\n    additionalProps: {\n      id\n    }\n  });\n  const [StaticTooltipLabelSlot, staticTooltipLabelSlotProps] = useSlot('staticTooltipLabel', {\n    elementType: SpeedDialActionStaticTooltipLabel,\n    externalForwardedProps,\n    ownerState,\n    className: classes.staticTooltipLabel,\n    additionalProps: {\n      style: transitionStyle,\n      id: `${id}-label`\n    }\n  });\n  const fab = /*#__PURE__*/_jsx(FabSlot, {\n    ...fabSlotProps,\n    children: icon\n  });\n  if (tooltipSlotProps.open) {\n    return /*#__PURE__*/_jsxs(StaticTooltipSlot, {\n      ...staticTooltipSlotProps,\n      ...other,\n      children: [/*#__PURE__*/_jsx(StaticTooltipLabelSlot, {\n        ...staticTooltipLabelSlotProps,\n        children: tooltipSlotProps.title\n      }), /*#__PURE__*/React.cloneElement(fab, {\n        'aria-labelledby': `${id}-label`\n      })]\n    });\n  }\n  if (!open && tooltipOpen) {\n    setTooltipOpen(false);\n  }\n  return /*#__PURE__*/_jsx(TooltipSlot, {\n    ...tooltipSlotProps,\n    title: tooltipSlotProps.title,\n    open: open && tooltipOpen,\n    placement: tooltipSlotProps.placement,\n    classes: tooltipSlotProps.classes,\n    ...other,\n    children: fab\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? SpeedDialAction.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Adds a transition delay, to allow a series of SpeedDialActions to be animated.\n   * @default 0\n   */\n  delay: PropTypes.number,\n  /**\n   * Props applied to the [`Fab`](https://mui.com/material-ui/api/fab/) component.\n   * @default {}\n   * @deprecated Use `slotProps.fab` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  FabProps: PropTypes.object,\n  /**\n   * The icon to display in the SpeedDial Fab.\n   */\n  icon: PropTypes.node,\n  /**\n   * This prop is used to help implement the accessibility logic.\n   * If you don't provide this prop. It falls back to a randomly generated id.\n   */\n  id: PropTypes.string,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    fab: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    staticTooltip: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    staticTooltipLabel: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    tooltip: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    fab: PropTypes.elementType,\n    staticTooltip: PropTypes.elementType,\n    staticTooltipLabel: PropTypes.elementType,\n    tooltip: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * `classes` prop applied to the [`Tooltip`](https://mui.com/material-ui/api/tooltip/) element.\n   * @deprecated Use `slotProps.tooltip.classes` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  TooltipClasses: PropTypes.object,\n  /**\n   * Make the tooltip always visible when the SpeedDial is open.\n   * @default false\n   * @deprecated Use `slotProps.tooltip.open` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  tooltipOpen: PropTypes.bool,\n  /**\n   * Placement of the tooltip.\n   * @default 'left'\n   * @deprecated Use `slotProps.tooltip.placement` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  tooltipPlacement: PropTypes.oneOf(['auto-end', 'auto-start', 'auto', 'bottom-end', 'bottom-start', 'bottom', 'left-end', 'left-start', 'left', 'right-end', 'right-start', 'right', 'top-end', 'top-start', 'top']),\n  /**\n   * Label to display in the tooltip.\n   * @deprecated Use `slotProps.tooltip.title` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  tooltipTitle: PropTypes.node\n} : void 0;\nexport default SpeedDialAction;"], "mappings": "AAAA,YAAY;;AAEZ;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,SAAS,QAAQ,8BAA8B;AACxD,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,SAASC,eAAe,QAAQ,kCAAkC;AAClE,OAAOC,GAAG,MAAM,iBAAiB;AACjC,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,sBAAsB,IAAIC,8BAA8B,QAAQ,6BAA6B;AACpG,OAAOC,OAAO,MAAM,qBAAqB;AACzC,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,IAAI;IACJC,gBAAgB;IAChBC;EACF,CAAC,GAAGH,UAAU;EACd,MAAMI,KAAK,GAAG;IACZC,GAAG,EAAE,CAAC,KAAK,EAAE,CAACJ,IAAI,IAAI,WAAW,CAAC;IAClCK,aAAa,EAAE,CAAC,eAAe,EAAE,mBAAmBhB,UAAU,CAACY,gBAAgB,CAAC,EAAE,EAAE,CAACD,IAAI,IAAI,qBAAqB,CAAC;IACnHM,kBAAkB,EAAE,CAAC,oBAAoB;EAC3C,CAAC;EACD,OAAOxB,cAAc,CAACqB,KAAK,EAAEZ,8BAA8B,EAAEW,OAAO,CAAC;AACvE,CAAC;AACD,MAAMK,kBAAkB,GAAGvB,MAAM,CAACG,GAAG,EAAE;EACrCqB,IAAI,EAAE,oBAAoB;EAC1BC,IAAI,EAAE,KAAK;EACXC,oBAAoB,EAAE,KAAK;EAC3BC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJd;IACF,CAAC,GAAGa,KAAK;IACT,OAAO,CAACC,MAAM,CAACT,GAAG,EAAE,CAACL,UAAU,CAACC,IAAI,IAAIa,MAAM,CAACC,SAAS,CAAC;EAC3D;AACF,CAAC,CAAC,CAAC7B,SAAS,CAAC,CAAC;EACZ8B;AACF,CAAC,MAAM;EACLC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE,CAACF,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEI,OAAO,CAACC,IAAI,CAACC,SAAS;EACnDC,eAAe,EAAE,CAACP,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEI,OAAO,CAACI,UAAU,CAACC,KAAK;EAC/D,SAAS,EAAE;IACTF,eAAe,EAAEP,KAAK,CAACG,IAAI,GAAGH,KAAK,CAACG,IAAI,CAACC,OAAO,CAACM,eAAe,CAACC,UAAU,GAAG3C,SAAS,CAACgC,KAAK,CAACI,OAAO,CAACI,UAAU,CAACC,KAAK,EAAE,IAAI;EAC9H,CAAC;EACDG,UAAU,EAAE,GAAGZ,KAAK,CAACa,WAAW,CAACC,MAAM,CAAC,WAAW,EAAE;IACnDC,QAAQ,EAAEf,KAAK,CAACa,WAAW,CAACE,QAAQ,CAACC;EACvC,CAAC,CAAC,gBAAgB;EAClBC,OAAO,EAAE,CAAC;EACVC,QAAQ,EAAE,CAAC;IACTrB,KAAK,EAAEA,CAAC;MACNb;IACF,CAAC,KAAK,CAACA,UAAU,CAACC,IAAI;IACtBkC,KAAK,EAAE;MACLF,OAAO,EAAE,CAAC;MACVG,SAAS,EAAE;IACb;EACF,CAAC;AACH,CAAC,CAAC,CAAC,CAAC;AACJ,MAAMC,4BAA4B,GAAGpD,MAAM,CAAC,MAAM,EAAE;EAClDwB,IAAI,EAAE,oBAAoB;EAC1BC,IAAI,EAAE,eAAe;EACrBE,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJd;IACF,CAAC,GAAGa,KAAK;IACT,OAAO,CAACC,MAAM,CAACR,aAAa,EAAE,CAACN,UAAU,CAACC,IAAI,IAAIa,MAAM,CAACwB,mBAAmB,EAAExB,MAAM,CAAC,mBAAmBxB,UAAU,CAACU,UAAU,CAACE,gBAAgB,CAAC,EAAE,CAAC,CAAC;EACrJ;AACF,CAAC,CAAC,CAAChB,SAAS,CAAC,CAAC;EACZ8B;AACF,CAAC,MAAM;EACLuB,QAAQ,EAAE,UAAU;EACpBC,OAAO,EAAE,MAAM;EACfC,UAAU,EAAE,QAAQ;EACpB,CAAC,MAAMlD,sBAAsB,CAACgB,kBAAkB,EAAE,GAAG;IACnDqB,UAAU,EAAEZ,KAAK,CAACa,WAAW,CAACC,MAAM,CAAC,CAAC,WAAW,EAAE,SAAS,CAAC,EAAE;MAC7DC,QAAQ,EAAEf,KAAK,CAACa,WAAW,CAACE,QAAQ,CAACC;IACvC,CAAC,CAAC;IACFC,OAAO,EAAE;EACX,CAAC;EACDC,QAAQ,EAAE,CAAC;IACTrB,KAAK,EAAEA,CAAC;MACNb;IACF,CAAC,KAAK,CAACA,UAAU,CAACC,IAAI;IACtBkC,KAAK,EAAE;MACL,CAAC,MAAM5C,sBAAsB,CAACgB,kBAAkB,EAAE,GAAG;QACnD0B,OAAO,EAAE,CAAC;QACVG,SAAS,EAAE;MACb;IACF;EACF,CAAC,EAAE;IACDvB,KAAK,EAAE;MACLX,gBAAgB,EAAE;IACpB,CAAC;IACDiC,KAAK,EAAE;MACL,CAAC,MAAM5C,sBAAsB,CAACgB,kBAAkB,EAAE,GAAG;QACnDmC,eAAe,EAAE,UAAU;QAC3BC,KAAK,EAAE,MAAM;QACbC,WAAW,EAAE;MACf;IACF;EACF,CAAC,EAAE;IACD/B,KAAK,EAAE;MACLX,gBAAgB,EAAE;IACpB,CAAC;IACDiC,KAAK,EAAE;MACL,CAAC,MAAM5C,sBAAsB,CAACgB,kBAAkB,EAAE,GAAG;QACnDmC,eAAe,EAAE,QAAQ;QACzBG,IAAI,EAAE,MAAM;QACZC,UAAU,EAAE;MACd;IACF;EACF,CAAC;AACH,CAAC,CAAC,CAAC,CAAC;AACJ,MAAMC,iCAAiC,GAAG9D,MAAM,CAAC,MAAM,EAAE;EACvDwB,IAAI,EAAE,oBAAoB;EAC1BC,IAAI,EAAE,oBAAoB;EAC1BE,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACP;AAC/C,CAAC,CAAC,CAACrB,SAAS,CAAC,CAAC;EACZ8B;AACF,CAAC,MAAM;EACLuB,QAAQ,EAAE,UAAU;EACpB,GAAGvB,KAAK,CAACgC,UAAU,CAACC,KAAK;EACzB1B,eAAe,EAAE,CAACP,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEI,OAAO,CAACI,UAAU,CAACC,KAAK;EAC/DyB,YAAY,EAAE,CAAClC,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEmC,KAAK,CAACD,YAAY;EACtDE,SAAS,EAAE,CAACpC,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEqC,OAAO,CAAC,CAAC,CAAC;EAC3CnC,KAAK,EAAE,CAACF,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEI,OAAO,CAACC,IAAI,CAACC,SAAS;EACnDgC,OAAO,EAAE,UAAU;EACnBC,SAAS,EAAE;AACb,CAAC,CAAC,CAAC,CAAC;AACJ,MAAM7B,eAAe,GAAG,aAAa9C,KAAK,CAAC4E,UAAU,CAAC,SAAS9B,eAAeA,CAAC+B,OAAO,EAAEC,GAAG,EAAE;EAC3F,MAAM7C,KAAK,GAAG1B,eAAe,CAAC;IAC5B0B,KAAK,EAAE4C,OAAO;IACdhD,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJkD,SAAS;IACTC,KAAK,GAAG,CAAC;IACTC,QAAQ,GAAG,CAAC,CAAC;IACbC,IAAI;IACJC,EAAE;IACF9D,IAAI;IACJ+D,cAAc;IACdC,WAAW,EAAEC,eAAe,GAAG,KAAK;IACpChE,gBAAgB,GAAG,MAAM;IACzBiE,YAAY;IACZ/D,KAAK,GAAG,CAAC,CAAC;IACVgE,SAAS,GAAG,CAAC,CAAC;IACd,GAAGC;EACL,CAAC,GAAGxD,KAAK;EACT,MAAMb,UAAU,GAAG;IACjB,GAAGa,KAAK;IACRX;EACF,CAAC;EACD,MAAMC,OAAO,GAAGJ,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMsE,sBAAsB,GAAG;IAC7BlE,KAAK;IACLgE,SAAS,EAAE;MACT/D,GAAG,EAAEwD,QAAQ;MACb,GAAGO,SAAS;MACZG,OAAO,EAAE7E,cAAc,CAAC,OAAO0E,SAAS,CAACG,OAAO,KAAK,UAAU,GAAGH,SAAS,CAACG,OAAO,CAACvE,UAAU,CAAC,GAAGoE,SAAS,CAACG,OAAO,EAAE;QACnHC,KAAK,EAAEL,YAAY;QACnBlE,IAAI,EAAEiE,eAAe;QACrBO,SAAS,EAAEvE,gBAAgB;QAC3BC,OAAO,EAAE6D;MACX,CAAC;IACH;EACF,CAAC;EACD,MAAM,CAACC,WAAW,EAAES,cAAc,CAAC,GAAG9F,KAAK,CAAC+F,QAAQ,CAACL,sBAAsB,CAACF,SAAS,CAACG,OAAO,EAAEtE,IAAI,CAAC;EACpG,MAAM2E,kBAAkB,GAAGA,CAAA,KAAM;IAC/BF,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;EACD,MAAMG,iBAAiB,GAAGA,CAAA,KAAM;IAC9BH,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EACD,MAAMI,eAAe,GAAG;IACtBC,eAAe,EAAE,GAAGnB,KAAK;EAC3B,CAAC;EACD,MAAM,CAACoB,OAAO,EAAEC,YAAY,CAAC,GAAGxF,OAAO,CAAC,KAAK,EAAE;IAC7CyF,WAAW,EAAE1E,kBAAkB;IAC/B8D,sBAAsB;IACtBtE,UAAU;IACVmF,0BAA0B,EAAE,IAAI;IAChCxB,SAAS,EAAE7E,IAAI,CAACqB,OAAO,CAACE,GAAG,EAAEsD,SAAS,CAAC;IACvCyB,eAAe,EAAE;MACfjD,KAAK,EAAE2C,eAAe;MACtBO,QAAQ,EAAE,CAAC,CAAC;MACZC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE;IACR;EACF,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,gBAAgB,CAAC,GAAGhG,OAAO,CAAC,SAAS,EAAE;IACzDyF,WAAW,EAAE7F,OAAO;IACpBiF,sBAAsB;IACtBa,0BAA0B,EAAE,IAAI;IAChCzB,GAAG;IACH0B,eAAe,EAAE;MACfrB;IACF,CAAC;IACD/D,UAAU;IACV0F,YAAY,EAAEC,QAAQ,KAAK;MACzB,GAAGA,QAAQ;MACXC,OAAO,EAAEC,KAAK,IAAI;QAChBF,QAAQ,CAACC,OAAO,GAAGC,KAAK,CAAC;QACzBjB,kBAAkB,CAAC,CAAC;MACtB,CAAC;MACDkB,MAAM,EAAED,KAAK,IAAI;QACfF,QAAQ,CAACG,MAAM,GAAGD,KAAK,CAAC;QACxBhB,iBAAiB,CAAC,CAAC;MACrB;IACF,CAAC;EACH,CAAC,CAAC;EACF,MAAM,CAACkB,iBAAiB,EAAEC,sBAAsB,CAAC,GAAGvG,OAAO,CAAC,eAAe,EAAE;IAC3EyF,WAAW,EAAE7C,4BAA4B;IACzCiC,sBAAsB;IACtBtE,UAAU;IACV0D,GAAG;IACHC,SAAS,EAAExD,OAAO,CAACG,aAAa;IAChC8E,eAAe,EAAE;MACfrB;IACF;EACF,CAAC,CAAC;EACF,MAAM,CAACkC,sBAAsB,EAAEC,2BAA2B,CAAC,GAAGzG,OAAO,CAAC,oBAAoB,EAAE;IAC1FyF,WAAW,EAAEnC,iCAAiC;IAC9CuB,sBAAsB;IACtBtE,UAAU;IACV2D,SAAS,EAAExD,OAAO,CAACI,kBAAkB;IACrC6E,eAAe,EAAE;MACfjD,KAAK,EAAE2C,eAAe;MACtBf,EAAE,EAAE,GAAGA,EAAE;IACX;EACF,CAAC,CAAC;EACF,MAAM1D,GAAG,GAAG,aAAaT,IAAI,CAACoF,OAAO,EAAE;IACrC,GAAGC,YAAY;IACfkB,QAAQ,EAAErC;EACZ,CAAC,CAAC;EACF,IAAI2B,gBAAgB,CAACxF,IAAI,EAAE;IACzB,OAAO,aAAaH,KAAK,CAACiG,iBAAiB,EAAE;MAC3C,GAAGC,sBAAsB;MACzB,GAAG3B,KAAK;MACR8B,QAAQ,EAAE,CAAC,aAAavG,IAAI,CAACqG,sBAAsB,EAAE;QACnD,GAAGC,2BAA2B;QAC9BC,QAAQ,EAAEV,gBAAgB,CAACjB;MAC7B,CAAC,CAAC,EAAE,aAAa5F,KAAK,CAACwH,YAAY,CAAC/F,GAAG,EAAE;QACvC,iBAAiB,EAAE,GAAG0D,EAAE;MAC1B,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EACA,IAAI,CAAC9D,IAAI,IAAIgE,WAAW,EAAE;IACxBS,cAAc,CAAC,KAAK,CAAC;EACvB;EACA,OAAO,aAAa9E,IAAI,CAAC4F,WAAW,EAAE;IACpC,GAAGC,gBAAgB;IACnBjB,KAAK,EAAEiB,gBAAgB,CAACjB,KAAK;IAC7BvE,IAAI,EAAEA,IAAI,IAAIgE,WAAW;IACzBQ,SAAS,EAAEgB,gBAAgB,CAAChB,SAAS;IACrCtE,OAAO,EAAEsF,gBAAgB,CAACtF,OAAO;IACjC,GAAGkE,KAAK;IACR8B,QAAQ,EAAE9F;EACZ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFgG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG7E,eAAe,CAAC8E,SAAS,CAAC,yBAAyB;EACzF;EACA;EACA;EACA;EACA;AACF;AACA;EACErG,OAAO,EAAEtB,SAAS,CAAC4H,MAAM;EACzB;AACF;AACA;EACE9C,SAAS,EAAE9E,SAAS,CAAC6H,MAAM;EAC3B;AACF;AACA;AACA;EACE9C,KAAK,EAAE/E,SAAS,CAAC8H,MAAM;EACvB;AACF;AACA;AACA;AACA;EACE9C,QAAQ,EAAEhF,SAAS,CAAC4H,MAAM;EAC1B;AACF;AACA;EACE3C,IAAI,EAAEjF,SAAS,CAAC+H,IAAI;EACpB;AACF;AACA;AACA;EACE7C,EAAE,EAAElF,SAAS,CAAC6H,MAAM;EACpB;AACF;AACA;EACEzG,IAAI,EAAEpB,SAAS,CAACgI,IAAI;EACpB;AACF;AACA;AACA;EACEzC,SAAS,EAAEvF,SAAS,CAACsE,KAAK,CAAC;IACzB9C,GAAG,EAAExB,SAAS,CAACiI,SAAS,CAAC,CAACjI,SAAS,CAACkI,IAAI,EAAElI,SAAS,CAAC4H,MAAM,CAAC,CAAC;IAC5DnG,aAAa,EAAEzB,SAAS,CAACiI,SAAS,CAAC,CAACjI,SAAS,CAACkI,IAAI,EAAElI,SAAS,CAAC4H,MAAM,CAAC,CAAC;IACtElG,kBAAkB,EAAE1B,SAAS,CAACiI,SAAS,CAAC,CAACjI,SAAS,CAACkI,IAAI,EAAElI,SAAS,CAAC4H,MAAM,CAAC,CAAC;IAC3ElC,OAAO,EAAE1F,SAAS,CAACiI,SAAS,CAAC,CAACjI,SAAS,CAACkI,IAAI,EAAElI,SAAS,CAAC4H,MAAM,CAAC;EACjE,CAAC,CAAC;EACF;AACF;AACA;AACA;EACErG,KAAK,EAAEvB,SAAS,CAACsE,KAAK,CAAC;IACrB9C,GAAG,EAAExB,SAAS,CAACqG,WAAW;IAC1B5E,aAAa,EAAEzB,SAAS,CAACqG,WAAW;IACpC3E,kBAAkB,EAAE1B,SAAS,CAACqG,WAAW;IACzCX,OAAO,EAAE1F,SAAS,CAACqG;EACrB,CAAC,CAAC;EACF;AACF;AACA;EACE8B,EAAE,EAAEnI,SAAS,CAACiI,SAAS,CAAC,CAACjI,SAAS,CAACoI,OAAO,CAACpI,SAAS,CAACiI,SAAS,CAAC,CAACjI,SAAS,CAACkI,IAAI,EAAElI,SAAS,CAAC4H,MAAM,EAAE5H,SAAS,CAACgI,IAAI,CAAC,CAAC,CAAC,EAAEhI,SAAS,CAACkI,IAAI,EAAElI,SAAS,CAAC4H,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;EACEzC,cAAc,EAAEnF,SAAS,CAAC4H,MAAM;EAChC;AACF;AACA;AACA;AACA;EACExC,WAAW,EAAEpF,SAAS,CAACgI,IAAI;EAC3B;AACF;AACA;AACA;AACA;EACE3G,gBAAgB,EAAErB,SAAS,CAACqI,KAAK,CAAC,CAAC,UAAU,EAAE,YAAY,EAAE,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;EACnN;AACF;AACA;AACA;EACE/C,YAAY,EAAEtF,SAAS,CAAC+H;AAC1B,CAAC,GAAG,KAAK,CAAC;AACV,eAAelF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}