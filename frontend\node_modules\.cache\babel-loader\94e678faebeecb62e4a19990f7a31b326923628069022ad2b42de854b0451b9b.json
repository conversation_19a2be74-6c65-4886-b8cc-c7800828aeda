{"ast": null, "code": "const pink = {\n  50: '#fce4ec',\n  100: '#f8bbd0',\n  200: '#f48fb1',\n  300: '#f06292',\n  400: '#ec407a',\n  500: '#e91e63',\n  600: '#d81b60',\n  700: '#c2185b',\n  800: '#ad1457',\n  900: '#880e4f',\n  A100: '#ff80ab',\n  A200: '#ff4081',\n  A400: '#f50057',\n  A700: '#c51162'\n};\nexport default pink;", "map": {"version": 3, "names": ["pink", "A100", "A200", "A400", "A700"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/@mui/material/esm/colors/pink.js"], "sourcesContent": ["const pink = {\n  50: '#fce4ec',\n  100: '#f8bbd0',\n  200: '#f48fb1',\n  300: '#f06292',\n  400: '#ec407a',\n  500: '#e91e63',\n  600: '#d81b60',\n  700: '#c2185b',\n  800: '#ad1457',\n  900: '#880e4f',\n  A100: '#ff80ab',\n  A200: '#ff4081',\n  A400: '#f50057',\n  A700: '#c51162'\n};\nexport default pink;"], "mappings": "AAAA,MAAMA,IAAI,GAAG;EACX,EAAE,EAAE,SAAS;EACb,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACdC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE;AACR,CAAC;AACD,eAAeJ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}