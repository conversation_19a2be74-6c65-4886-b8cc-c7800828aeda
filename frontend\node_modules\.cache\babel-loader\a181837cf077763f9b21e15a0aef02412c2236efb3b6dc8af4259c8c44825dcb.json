{"ast": null, "code": "export default function getMainAxisFromPlacement(placement) {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}", "map": {"version": 3, "names": ["getMainAxisFromPlacement", "placement", "indexOf"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js"], "sourcesContent": ["export default function getMainAxisFromPlacement(placement) {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}"], "mappings": "AAAA,eAAe,SAASA,wBAAwBA,CAACC,SAAS,EAAE;EAC1D,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC,CAACC,OAAO,CAACD,SAAS,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG;AAC9D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}