{"ast": null, "code": "import PropTypes from 'prop-types';\nconst responsivePropType = process.env.NODE_ENV !== 'production' ? PropTypes.oneOfType([PropTypes.number, PropTypes.string, PropTypes.object, PropTypes.array]) : {};\nexport default responsivePropType;", "map": {"version": 3, "names": ["PropTypes", "responsivePropType", "process", "env", "NODE_ENV", "oneOfType", "number", "string", "object", "array"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/@mui/system/esm/responsivePropType/responsivePropType.js"], "sourcesContent": ["import PropTypes from 'prop-types';\nconst responsivePropType = process.env.NODE_ENV !== 'production' ? PropTypes.oneOfType([PropTypes.number, PropTypes.string, PropTypes.object, PropTypes.array]) : {};\nexport default responsivePropType;"], "mappings": "AAAA,OAAOA,SAAS,MAAM,YAAY;AAClC,MAAMC,kBAAkB,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGJ,SAAS,CAACK,SAAS,CAAC,CAACL,SAAS,CAACM,MAAM,EAAEN,SAAS,CAACO,MAAM,EAAEP,SAAS,CAACQ,MAAM,EAAER,SAAS,CAACS,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;AACpK,eAAeR,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}