{"ast": null, "code": "'use client';\n\nimport { createBox } from '@mui/system';\nimport PropTypes from 'prop-types';\nimport { unstable_ClassNameGenerator as ClassNameGenerator } from \"../className/index.js\";\nimport { createTheme } from \"../styles/index.js\";\nimport THEME_ID from \"../styles/identifier.js\";\nimport boxClasses from \"./boxClasses.js\";\nconst defaultTheme = createTheme();\nconst Box = createBox({\n  themeId: THEME_ID,\n  defaultTheme,\n  defaultClassName: boxClasses.root,\n  generateClassName: ClassNameGenerator.generate\n});\nprocess.env.NODE_ENV !== \"production\" ? Box.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Box;", "map": {"version": 3, "names": ["createBox", "PropTypes", "unstable_ClassNameGenerator", "ClassNameGenerator", "createTheme", "THEME_ID", "boxClasses", "defaultTheme", "Box", "themeId", "defaultClassName", "root", "generateClassName", "generate", "process", "env", "NODE_ENV", "propTypes", "children", "node", "component", "elementType", "sx", "oneOfType", "arrayOf", "func", "object", "bool"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/@mui/material/esm/Box/Box.js"], "sourcesContent": ["'use client';\n\nimport { createBox } from '@mui/system';\nimport PropTypes from 'prop-types';\nimport { unstable_ClassNameGenerator as ClassNameGenerator } from \"../className/index.js\";\nimport { createTheme } from \"../styles/index.js\";\nimport THEME_ID from \"../styles/identifier.js\";\nimport boxClasses from \"./boxClasses.js\";\nconst defaultTheme = createTheme();\nconst Box = createBox({\n  themeId: THEME_ID,\n  defaultTheme,\n  defaultClassName: boxClasses.root,\n  generateClassName: ClassNameGenerator.generate\n});\nprocess.env.NODE_ENV !== \"production\" ? Box.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Box;"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,SAAS,QAAQ,aAAa;AACvC,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,2BAA2B,IAAIC,kBAAkB,QAAQ,uBAAuB;AACzF,SAASC,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,QAAQ,MAAM,yBAAyB;AAC9C,OAAOC,UAAU,MAAM,iBAAiB;AACxC,MAAMC,YAAY,GAAGH,WAAW,CAAC,CAAC;AAClC,MAAMI,GAAG,GAAGR,SAAS,CAAC;EACpBS,OAAO,EAAEJ,QAAQ;EACjBE,YAAY;EACZG,gBAAgB,EAAEJ,UAAU,CAACK,IAAI;EACjCC,iBAAiB,EAAET,kBAAkB,CAACU;AACxC,CAAC,CAAC;AACFC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGR,GAAG,CAACS,SAAS,CAAC,yBAAyB;EAC7E;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAEjB,SAAS,CAACkB,IAAI;EACxB;AACF;AACA;AACA;EACEC,SAAS,EAAEnB,SAAS,CAACoB,WAAW;EAChC;AACF;AACA;EACEC,EAAE,EAAErB,SAAS,CAACsB,SAAS,CAAC,CAACtB,SAAS,CAACuB,OAAO,CAACvB,SAAS,CAACsB,SAAS,CAAC,CAACtB,SAAS,CAACwB,IAAI,EAAExB,SAAS,CAACyB,MAAM,EAAEzB,SAAS,CAAC0B,IAAI,CAAC,CAAC,CAAC,EAAE1B,SAAS,CAACwB,IAAI,EAAExB,SAAS,CAACyB,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAelB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}