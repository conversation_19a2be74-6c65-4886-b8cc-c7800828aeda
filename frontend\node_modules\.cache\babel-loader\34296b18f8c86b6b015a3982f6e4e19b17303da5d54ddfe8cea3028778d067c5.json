{"ast": null, "code": "function updateSVGDimensions(instance, renderState) {\n  try {\n    renderState.dimensions = typeof instance.getBBox === \"function\" ? instance.getBBox() : instance.getBoundingClientRect();\n  } catch (e) {\n    // Most likely trying to measure an unrendered element under Firefox\n    renderState.dimensions = {\n      x: 0,\n      y: 0,\n      width: 0,\n      height: 0\n    };\n  }\n}\nexport { updateSVGDimensions };", "map": {"version": 3, "names": ["updateSVGDimensions", "instance", "renderState", "dimensions", "getBBox", "getBoundingClientRect", "e", "x", "y", "width", "height"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/framer-motion/dist/es/render/svg/utils/measure.mjs"], "sourcesContent": ["function updateSVGDimensions(instance, renderState) {\n    try {\n        renderState.dimensions =\n            typeof instance.getBBox === \"function\"\n                ? instance.getBBox()\n                : instance.getBoundingClientRect();\n    }\n    catch (e) {\n        // Most likely trying to measure an unrendered element under Firefox\n        renderState.dimensions = {\n            x: 0,\n            y: 0,\n            width: 0,\n            height: 0,\n        };\n    }\n}\n\nexport { updateSVGDimensions };\n"], "mappings": "AAAA,SAASA,mBAAmBA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAChD,IAAI;IACAA,WAAW,CAACC,UAAU,GAClB,OAAOF,QAAQ,CAACG,OAAO,KAAK,UAAU,GAChCH,QAAQ,CAACG,OAAO,CAAC,CAAC,GAClBH,QAAQ,CAACI,qBAAqB,CAAC,CAAC;EAC9C,CAAC,CACD,OAAOC,CAAC,EAAE;IACN;IACAJ,WAAW,CAACC,UAAU,GAAG;MACrBI,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE;IACZ,CAAC;EACL;AACJ;AAEA,SAASV,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}