{"ast": null, "code": "export { default } from \"./useControlled.js\";", "map": {"version": 3, "names": ["default"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/@mui/utils/esm/useControlled/index.js"], "sourcesContent": ["export { default } from \"./useControlled.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}