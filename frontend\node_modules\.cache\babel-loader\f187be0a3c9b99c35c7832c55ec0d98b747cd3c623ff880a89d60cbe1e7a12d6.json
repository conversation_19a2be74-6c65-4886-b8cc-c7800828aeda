{"ast": null, "code": "import { frame, cancelFrame, frameData } from '../frameloop/frame.mjs';\nimport { activeAnimations } from './animation-count.mjs';\nimport { statsBuffer } from './buffer.mjs';\nfunction record() {\n  const {\n    value\n  } = statsBuffer;\n  if (value === null) {\n    cancelFrame(record);\n    return;\n  }\n  value.frameloop.rate.push(frameData.delta);\n  value.animations.mainThread.push(activeAnimations.mainThread);\n  value.animations.waapi.push(activeAnimations.waapi);\n  value.animations.layout.push(activeAnimations.layout);\n}\nfunction mean(values) {\n  return values.reduce((acc, value) => acc + value, 0) / values.length;\n}\nfunction summarise(values, calcAverage = mean) {\n  if (values.length === 0) {\n    return {\n      min: 0,\n      max: 0,\n      avg: 0\n    };\n  }\n  return {\n    min: Math.min(...values),\n    max: Math.max(...values),\n    avg: calcAverage(values)\n  };\n}\nconst msToFps = ms => Math.round(1000 / ms);\nfunction clearStatsBuffer() {\n  statsBuffer.value = null;\n  statsBuffer.addProjectionMetrics = null;\n}\nfunction reportStats() {\n  const {\n    value\n  } = statsBuffer;\n  if (!value) {\n    throw new Error(\"Stats are not being measured\");\n  }\n  clearStatsBuffer();\n  cancelFrame(record);\n  const summary = {\n    frameloop: {\n      rate: summarise(value.frameloop.rate),\n      read: summarise(value.frameloop.read),\n      resolveKeyframes: summarise(value.frameloop.resolveKeyframes),\n      update: summarise(value.frameloop.update),\n      preRender: summarise(value.frameloop.preRender),\n      render: summarise(value.frameloop.render),\n      postRender: summarise(value.frameloop.postRender)\n    },\n    animations: {\n      mainThread: summarise(value.animations.mainThread),\n      waapi: summarise(value.animations.waapi),\n      layout: summarise(value.animations.layout)\n    },\n    layoutProjection: {\n      nodes: summarise(value.layoutProjection.nodes),\n      calculatedTargetDeltas: summarise(value.layoutProjection.calculatedTargetDeltas),\n      calculatedProjections: summarise(value.layoutProjection.calculatedProjections)\n    }\n  };\n  /**\n   * Convert the rate to FPS\n   */\n  const {\n    rate\n  } = summary.frameloop;\n  rate.min = msToFps(rate.min);\n  rate.max = msToFps(rate.max);\n  rate.avg = msToFps(rate.avg);\n  [rate.min, rate.max] = [rate.max, rate.min];\n  return summary;\n}\nfunction recordStats() {\n  if (statsBuffer.value) {\n    clearStatsBuffer();\n    throw new Error(\"Stats are already being measured\");\n  }\n  const newStatsBuffer = statsBuffer;\n  newStatsBuffer.value = {\n    frameloop: {\n      rate: [],\n      read: [],\n      resolveKeyframes: [],\n      update: [],\n      preRender: [],\n      render: [],\n      postRender: []\n    },\n    animations: {\n      mainThread: [],\n      waapi: [],\n      layout: []\n    },\n    layoutProjection: {\n      nodes: [],\n      calculatedTargetDeltas: [],\n      calculatedProjections: []\n    }\n  };\n  newStatsBuffer.addProjectionMetrics = metrics => {\n    const {\n      layoutProjection\n    } = newStatsBuffer.value;\n    layoutProjection.nodes.push(metrics.nodes);\n    layoutProjection.calculatedTargetDeltas.push(metrics.calculatedTargetDeltas);\n    layoutProjection.calculatedProjections.push(metrics.calculatedProjections);\n  };\n  frame.postRender(record, true);\n  return reportStats;\n}\nexport { recordStats };", "map": {"version": 3, "names": ["frame", "cancelFrame", "frameData", "activeAnimations", "statsBuffer", "record", "value", "frameloop", "rate", "push", "delta", "animations", "mainThread", "waapi", "layout", "mean", "values", "reduce", "acc", "length", "summarise", "calcAverage", "min", "max", "avg", "Math", "msToFps", "ms", "round", "clearStatsBuffer", "addProjectionMetrics", "reportStats", "Error", "summary", "read", "resolveKeyframes", "update", "preRender", "render", "postRender", "layoutProjection", "nodes", "calculatedTargetDeltas", "calculatedProjections", "recordStats", "newStatsBuffer", "metrics"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/motion-dom/dist/es/stats/index.mjs"], "sourcesContent": ["import { frame, cancelFrame, frameData } from '../frameloop/frame.mjs';\nimport { activeAnimations } from './animation-count.mjs';\nimport { statsBuffer } from './buffer.mjs';\n\nfunction record() {\n    const { value } = statsBuffer;\n    if (value === null) {\n        cancelFrame(record);\n        return;\n    }\n    value.frameloop.rate.push(frameData.delta);\n    value.animations.mainThread.push(activeAnimations.mainThread);\n    value.animations.waapi.push(activeAnimations.waapi);\n    value.animations.layout.push(activeAnimations.layout);\n}\nfunction mean(values) {\n    return values.reduce((acc, value) => acc + value, 0) / values.length;\n}\nfunction summarise(values, calcAverage = mean) {\n    if (values.length === 0) {\n        return {\n            min: 0,\n            max: 0,\n            avg: 0,\n        };\n    }\n    return {\n        min: Math.min(...values),\n        max: Math.max(...values),\n        avg: calcAverage(values),\n    };\n}\nconst msToFps = (ms) => Math.round(1000 / ms);\nfunction clearStatsBuffer() {\n    statsBuffer.value = null;\n    statsBuffer.addProjectionMetrics = null;\n}\nfunction reportStats() {\n    const { value } = statsBuffer;\n    if (!value) {\n        throw new Error(\"Stats are not being measured\");\n    }\n    clearStatsBuffer();\n    cancelFrame(record);\n    const summary = {\n        frameloop: {\n            rate: summarise(value.frameloop.rate),\n            read: summarise(value.frameloop.read),\n            resolveKeyframes: summarise(value.frameloop.resolveKeyframes),\n            update: summarise(value.frameloop.update),\n            preRender: summarise(value.frameloop.preRender),\n            render: summarise(value.frameloop.render),\n            postRender: summarise(value.frameloop.postRender),\n        },\n        animations: {\n            mainThread: summarise(value.animations.mainThread),\n            waapi: summarise(value.animations.waapi),\n            layout: summarise(value.animations.layout),\n        },\n        layoutProjection: {\n            nodes: summarise(value.layoutProjection.nodes),\n            calculatedTargetDeltas: summarise(value.layoutProjection.calculatedTargetDeltas),\n            calculatedProjections: summarise(value.layoutProjection.calculatedProjections),\n        },\n    };\n    /**\n     * Convert the rate to FPS\n     */\n    const { rate } = summary.frameloop;\n    rate.min = msToFps(rate.min);\n    rate.max = msToFps(rate.max);\n    rate.avg = msToFps(rate.avg);\n    [rate.min, rate.max] = [rate.max, rate.min];\n    return summary;\n}\nfunction recordStats() {\n    if (statsBuffer.value) {\n        clearStatsBuffer();\n        throw new Error(\"Stats are already being measured\");\n    }\n    const newStatsBuffer = statsBuffer;\n    newStatsBuffer.value = {\n        frameloop: {\n            rate: [],\n            read: [],\n            resolveKeyframes: [],\n            update: [],\n            preRender: [],\n            render: [],\n            postRender: [],\n        },\n        animations: {\n            mainThread: [],\n            waapi: [],\n            layout: [],\n        },\n        layoutProjection: {\n            nodes: [],\n            calculatedTargetDeltas: [],\n            calculatedProjections: [],\n        },\n    };\n    newStatsBuffer.addProjectionMetrics = (metrics) => {\n        const { layoutProjection } = newStatsBuffer.value;\n        layoutProjection.nodes.push(metrics.nodes);\n        layoutProjection.calculatedTargetDeltas.push(metrics.calculatedTargetDeltas);\n        layoutProjection.calculatedProjections.push(metrics.calculatedProjections);\n    };\n    frame.postRender(record, true);\n    return reportStats;\n}\n\nexport { recordStats };\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,WAAW,EAAEC,SAAS,QAAQ,wBAAwB;AACtE,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,WAAW,QAAQ,cAAc;AAE1C,SAASC,MAAMA,CAAA,EAAG;EACd,MAAM;IAAEC;EAAM,CAAC,GAAGF,WAAW;EAC7B,IAAIE,KAAK,KAAK,IAAI,EAAE;IAChBL,WAAW,CAACI,MAAM,CAAC;IACnB;EACJ;EACAC,KAAK,CAACC,SAAS,CAACC,IAAI,CAACC,IAAI,CAACP,SAAS,CAACQ,KAAK,CAAC;EAC1CJ,KAAK,CAACK,UAAU,CAACC,UAAU,CAACH,IAAI,CAACN,gBAAgB,CAACS,UAAU,CAAC;EAC7DN,KAAK,CAACK,UAAU,CAACE,KAAK,CAACJ,IAAI,CAACN,gBAAgB,CAACU,KAAK,CAAC;EACnDP,KAAK,CAACK,UAAU,CAACG,MAAM,CAACL,IAAI,CAACN,gBAAgB,CAACW,MAAM,CAAC;AACzD;AACA,SAASC,IAAIA,CAACC,MAAM,EAAE;EAClB,OAAOA,MAAM,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEZ,KAAK,KAAKY,GAAG,GAAGZ,KAAK,EAAE,CAAC,CAAC,GAAGU,MAAM,CAACG,MAAM;AACxE;AACA,SAASC,SAASA,CAACJ,MAAM,EAAEK,WAAW,GAAGN,IAAI,EAAE;EAC3C,IAAIC,MAAM,CAACG,MAAM,KAAK,CAAC,EAAE;IACrB,OAAO;MACHG,GAAG,EAAE,CAAC;MACNC,GAAG,EAAE,CAAC;MACNC,GAAG,EAAE;IACT,CAAC;EACL;EACA,OAAO;IACHF,GAAG,EAAEG,IAAI,CAACH,GAAG,CAAC,GAAGN,MAAM,CAAC;IACxBO,GAAG,EAAEE,IAAI,CAACF,GAAG,CAAC,GAAGP,MAAM,CAAC;IACxBQ,GAAG,EAAEH,WAAW,CAACL,MAAM;EAC3B,CAAC;AACL;AACA,MAAMU,OAAO,GAAIC,EAAE,IAAKF,IAAI,CAACG,KAAK,CAAC,IAAI,GAAGD,EAAE,CAAC;AAC7C,SAASE,gBAAgBA,CAAA,EAAG;EACxBzB,WAAW,CAACE,KAAK,GAAG,IAAI;EACxBF,WAAW,CAAC0B,oBAAoB,GAAG,IAAI;AAC3C;AACA,SAASC,WAAWA,CAAA,EAAG;EACnB,MAAM;IAAEzB;EAAM,CAAC,GAAGF,WAAW;EAC7B,IAAI,CAACE,KAAK,EAAE;IACR,MAAM,IAAI0B,KAAK,CAAC,8BAA8B,CAAC;EACnD;EACAH,gBAAgB,CAAC,CAAC;EAClB5B,WAAW,CAACI,MAAM,CAAC;EACnB,MAAM4B,OAAO,GAAG;IACZ1B,SAAS,EAAE;MACPC,IAAI,EAAEY,SAAS,CAACd,KAAK,CAACC,SAAS,CAACC,IAAI,CAAC;MACrC0B,IAAI,EAAEd,SAAS,CAACd,KAAK,CAACC,SAAS,CAAC2B,IAAI,CAAC;MACrCC,gBAAgB,EAAEf,SAAS,CAACd,KAAK,CAACC,SAAS,CAAC4B,gBAAgB,CAAC;MAC7DC,MAAM,EAAEhB,SAAS,CAACd,KAAK,CAACC,SAAS,CAAC6B,MAAM,CAAC;MACzCC,SAAS,EAAEjB,SAAS,CAACd,KAAK,CAACC,SAAS,CAAC8B,SAAS,CAAC;MAC/CC,MAAM,EAAElB,SAAS,CAACd,KAAK,CAACC,SAAS,CAAC+B,MAAM,CAAC;MACzCC,UAAU,EAAEnB,SAAS,CAACd,KAAK,CAACC,SAAS,CAACgC,UAAU;IACpD,CAAC;IACD5B,UAAU,EAAE;MACRC,UAAU,EAAEQ,SAAS,CAACd,KAAK,CAACK,UAAU,CAACC,UAAU,CAAC;MAClDC,KAAK,EAAEO,SAAS,CAACd,KAAK,CAACK,UAAU,CAACE,KAAK,CAAC;MACxCC,MAAM,EAAEM,SAAS,CAACd,KAAK,CAACK,UAAU,CAACG,MAAM;IAC7C,CAAC;IACD0B,gBAAgB,EAAE;MACdC,KAAK,EAAErB,SAAS,CAACd,KAAK,CAACkC,gBAAgB,CAACC,KAAK,CAAC;MAC9CC,sBAAsB,EAAEtB,SAAS,CAACd,KAAK,CAACkC,gBAAgB,CAACE,sBAAsB,CAAC;MAChFC,qBAAqB,EAAEvB,SAAS,CAACd,KAAK,CAACkC,gBAAgB,CAACG,qBAAqB;IACjF;EACJ,CAAC;EACD;AACJ;AACA;EACI,MAAM;IAAEnC;EAAK,CAAC,GAAGyB,OAAO,CAAC1B,SAAS;EAClCC,IAAI,CAACc,GAAG,GAAGI,OAAO,CAAClB,IAAI,CAACc,GAAG,CAAC;EAC5Bd,IAAI,CAACe,GAAG,GAAGG,OAAO,CAAClB,IAAI,CAACe,GAAG,CAAC;EAC5Bf,IAAI,CAACgB,GAAG,GAAGE,OAAO,CAAClB,IAAI,CAACgB,GAAG,CAAC;EAC5B,CAAChB,IAAI,CAACc,GAAG,EAAEd,IAAI,CAACe,GAAG,CAAC,GAAG,CAACf,IAAI,CAACe,GAAG,EAAEf,IAAI,CAACc,GAAG,CAAC;EAC3C,OAAOW,OAAO;AAClB;AACA,SAASW,WAAWA,CAAA,EAAG;EACnB,IAAIxC,WAAW,CAACE,KAAK,EAAE;IACnBuB,gBAAgB,CAAC,CAAC;IAClB,MAAM,IAAIG,KAAK,CAAC,kCAAkC,CAAC;EACvD;EACA,MAAMa,cAAc,GAAGzC,WAAW;EAClCyC,cAAc,CAACvC,KAAK,GAAG;IACnBC,SAAS,EAAE;MACPC,IAAI,EAAE,EAAE;MACR0B,IAAI,EAAE,EAAE;MACRC,gBAAgB,EAAE,EAAE;MACpBC,MAAM,EAAE,EAAE;MACVC,SAAS,EAAE,EAAE;MACbC,MAAM,EAAE,EAAE;MACVC,UAAU,EAAE;IAChB,CAAC;IACD5B,UAAU,EAAE;MACRC,UAAU,EAAE,EAAE;MACdC,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE;IACZ,CAAC;IACD0B,gBAAgB,EAAE;MACdC,KAAK,EAAE,EAAE;MACTC,sBAAsB,EAAE,EAAE;MAC1BC,qBAAqB,EAAE;IAC3B;EACJ,CAAC;EACDE,cAAc,CAACf,oBAAoB,GAAIgB,OAAO,IAAK;IAC/C,MAAM;MAAEN;IAAiB,CAAC,GAAGK,cAAc,CAACvC,KAAK;IACjDkC,gBAAgB,CAACC,KAAK,CAAChC,IAAI,CAACqC,OAAO,CAACL,KAAK,CAAC;IAC1CD,gBAAgB,CAACE,sBAAsB,CAACjC,IAAI,CAACqC,OAAO,CAACJ,sBAAsB,CAAC;IAC5EF,gBAAgB,CAACG,qBAAqB,CAAClC,IAAI,CAACqC,OAAO,CAACH,qBAAqB,CAAC;EAC9E,CAAC;EACD3C,KAAK,CAACuC,UAAU,CAAClC,MAAM,EAAE,IAAI,CAAC;EAC9B,OAAO0B,WAAW;AACtB;AAEA,SAASa,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}