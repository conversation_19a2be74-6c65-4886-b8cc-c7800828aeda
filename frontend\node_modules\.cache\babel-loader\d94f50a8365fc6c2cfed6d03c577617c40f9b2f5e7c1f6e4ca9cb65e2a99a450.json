{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getModalUtilityClass(slot) {\n  return generateUtilityClass('MuiModal', slot);\n}\nconst modalClasses = generateUtilityClasses('MuiModal', ['root', 'hidden', 'backdrop']);\nexport default modalClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getModalUtilityClass", "slot", "modalClasses"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/@mui/material/esm/Modal/modalClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getModalUtilityClass(slot) {\n  return generateUtilityClass('MuiModal', slot);\n}\nconst modalClasses = generateUtilityClasses('MuiModal', ['root', 'hidden', 'backdrop']);\nexport default modalClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,oBAAoBA,CAACC,IAAI,EAAE;EACzC,OAAOF,oBAAoB,CAAC,UAAU,EAAEE,IAAI,CAAC;AAC/C;AACA,MAAMC,YAAY,GAAGJ,sBAAsB,CAAC,UAAU,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;AACvF,eAAeI,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}