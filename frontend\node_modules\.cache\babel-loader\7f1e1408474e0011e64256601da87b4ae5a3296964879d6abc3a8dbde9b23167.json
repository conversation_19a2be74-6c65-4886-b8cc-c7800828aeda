{"ast": null, "code": "import { isDragActive } from '../drag/state/is-active.mjs';\nimport { isNodeOrChild } from '../utils/is-node-or-child.mjs';\nimport { isPrimaryPointer } from '../utils/is-primary-pointer.mjs';\nimport { setupGesture } from '../utils/setup.mjs';\nimport { isElementKeyboardAccessible } from './utils/is-keyboard-accessible.mjs';\nimport { enableKeyboardPress } from './utils/keyboard.mjs';\nimport { isPressing } from './utils/state.mjs';\n\n/**\n * Filter out events that are not primary pointer events, or are triggering\n * while a Motion gesture is active.\n */\nfunction isValidPressEvent(event) {\n  return isPrimaryPointer(event) && !isDragActive();\n}\n/**\n * Create a press gesture.\n *\n * Press is different to `\"pointerdown\"`, `\"pointerup\"` in that it\n * automatically filters out secondary pointer events like right\n * click and multitouch.\n *\n * It also adds accessibility support for keyboards, where\n * an element with a press gesture will receive focus and\n *  trigger on Enter `\"keydown\"` and `\"keyup\"` events.\n *\n * This is different to a browser's `\"click\"` event, which does\n * respond to keyboards but only for the `\"click\"` itself, rather\n * than the press start and end/cancel. The element also needs\n * to be focusable for this to work, whereas a press gesture will\n * make an element focusable by default.\n *\n * @public\n */\nfunction press(targetOrSelector, onPressStart, options = {}) {\n  const [targets, eventOptions, cancelEvents] = setupGesture(targetOrSelector, options);\n  const startPress = startEvent => {\n    const target = startEvent.currentTarget;\n    if (!isValidPressEvent(startEvent) || isPressing.has(target)) return;\n    isPressing.add(target);\n    const onPressEnd = onPressStart(target, startEvent);\n    const onPointerEnd = (endEvent, success) => {\n      window.removeEventListener(\"pointerup\", onPointerUp);\n      window.removeEventListener(\"pointercancel\", onPointerCancel);\n      if (!isValidPressEvent(endEvent) || !isPressing.has(target)) {\n        return;\n      }\n      isPressing.delete(target);\n      if (typeof onPressEnd === \"function\") {\n        onPressEnd(endEvent, {\n          success\n        });\n      }\n    };\n    const onPointerUp = upEvent => {\n      onPointerEnd(upEvent, target === window || target === document || options.useGlobalTarget || isNodeOrChild(target, upEvent.target));\n    };\n    const onPointerCancel = cancelEvent => {\n      onPointerEnd(cancelEvent, false);\n    };\n    window.addEventListener(\"pointerup\", onPointerUp, eventOptions);\n    window.addEventListener(\"pointercancel\", onPointerCancel, eventOptions);\n  };\n  targets.forEach(target => {\n    const pointerDownTarget = options.useGlobalTarget ? window : target;\n    pointerDownTarget.addEventListener(\"pointerdown\", startPress, eventOptions);\n    if (target instanceof HTMLElement) {\n      target.addEventListener(\"focus\", event => enableKeyboardPress(event, eventOptions));\n      if (!isElementKeyboardAccessible(target) && !target.hasAttribute(\"tabindex\")) {\n        target.tabIndex = 0;\n      }\n    }\n  });\n  return cancelEvents;\n}\nexport { press };", "map": {"version": 3, "names": ["isDragActive", "isNodeOrChild", "isPrimaryPointer", "setupGesture", "isElementKeyboardAccessible", "enableKeyboardPress", "isPressing", "isValidPressEvent", "event", "press", "targetOrSelector", "onPressStart", "options", "targets", "eventOptions", "cancelEvents", "startPress", "startEvent", "target", "currentTarget", "has", "add", "onPressEnd", "onPointerEnd", "endEvent", "success", "window", "removeEventListener", "onPointerUp", "onPointerCancel", "delete", "upEvent", "document", "useGlobalTarget", "cancelEvent", "addEventListener", "for<PERSON>ach", "pointerDownTarget", "HTMLElement", "hasAttribute", "tabIndex"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/motion-dom/dist/es/gestures/press/index.mjs"], "sourcesContent": ["import { isDragActive } from '../drag/state/is-active.mjs';\nimport { isNodeOrChild } from '../utils/is-node-or-child.mjs';\nimport { isPrimaryPointer } from '../utils/is-primary-pointer.mjs';\nimport { setupGesture } from '../utils/setup.mjs';\nimport { isElementKeyboardAccessible } from './utils/is-keyboard-accessible.mjs';\nimport { enableKeyboardPress } from './utils/keyboard.mjs';\nimport { isPressing } from './utils/state.mjs';\n\n/**\n * Filter out events that are not primary pointer events, or are triggering\n * while a Motion gesture is active.\n */\nfunction isValidPressEvent(event) {\n    return isPrimaryPointer(event) && !isDragActive();\n}\n/**\n * Create a press gesture.\n *\n * Press is different to `\"pointerdown\"`, `\"pointerup\"` in that it\n * automatically filters out secondary pointer events like right\n * click and multitouch.\n *\n * It also adds accessibility support for keyboards, where\n * an element with a press gesture will receive focus and\n *  trigger on Enter `\"keydown\"` and `\"keyup\"` events.\n *\n * This is different to a browser's `\"click\"` event, which does\n * respond to keyboards but only for the `\"click\"` itself, rather\n * than the press start and end/cancel. The element also needs\n * to be focusable for this to work, whereas a press gesture will\n * make an element focusable by default.\n *\n * @public\n */\nfunction press(targetOrSelector, onPressStart, options = {}) {\n    const [targets, eventOptions, cancelEvents] = setupGesture(targetOrSelector, options);\n    const startPress = (startEvent) => {\n        const target = startEvent.currentTarget;\n        if (!isValidPressEvent(startEvent) || isPressing.has(target))\n            return;\n        isPressing.add(target);\n        const onPressEnd = onPressStart(target, startEvent);\n        const onPointerEnd = (endEvent, success) => {\n            window.removeEventListener(\"pointerup\", onPointerUp);\n            window.removeEventListener(\"pointercancel\", onPointerCancel);\n            if (!isValidPressEvent(endEvent) || !isPressing.has(target)) {\n                return;\n            }\n            isPressing.delete(target);\n            if (typeof onPressEnd === \"function\") {\n                onPressEnd(endEvent, { success });\n            }\n        };\n        const onPointerUp = (upEvent) => {\n            onPointerEnd(upEvent, target === window ||\n                target === document ||\n                options.useGlobalTarget ||\n                isNodeOrChild(target, upEvent.target));\n        };\n        const onPointerCancel = (cancelEvent) => {\n            onPointerEnd(cancelEvent, false);\n        };\n        window.addEventListener(\"pointerup\", onPointerUp, eventOptions);\n        window.addEventListener(\"pointercancel\", onPointerCancel, eventOptions);\n    };\n    targets.forEach((target) => {\n        const pointerDownTarget = options.useGlobalTarget ? window : target;\n        pointerDownTarget.addEventListener(\"pointerdown\", startPress, eventOptions);\n        if (target instanceof HTMLElement) {\n            target.addEventListener(\"focus\", (event) => enableKeyboardPress(event, eventOptions));\n            if (!isElementKeyboardAccessible(target) &&\n                !target.hasAttribute(\"tabindex\")) {\n                target.tabIndex = 0;\n            }\n        }\n    });\n    return cancelEvents;\n}\n\nexport { press };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,aAAa,QAAQ,+BAA+B;AAC7D,SAASC,gBAAgB,QAAQ,iCAAiC;AAClE,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,2BAA2B,QAAQ,oCAAoC;AAChF,SAASC,mBAAmB,QAAQ,sBAAsB;AAC1D,SAASC,UAAU,QAAQ,mBAAmB;;AAE9C;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAACC,KAAK,EAAE;EAC9B,OAAON,gBAAgB,CAACM,KAAK,CAAC,IAAI,CAACR,YAAY,CAAC,CAAC;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,KAAKA,CAACC,gBAAgB,EAAEC,YAAY,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EACzD,MAAM,CAACC,OAAO,EAAEC,YAAY,EAAEC,YAAY,CAAC,GAAGZ,YAAY,CAACO,gBAAgB,EAAEE,OAAO,CAAC;EACrF,MAAMI,UAAU,GAAIC,UAAU,IAAK;IAC/B,MAAMC,MAAM,GAAGD,UAAU,CAACE,aAAa;IACvC,IAAI,CAACZ,iBAAiB,CAACU,UAAU,CAAC,IAAIX,UAAU,CAACc,GAAG,CAACF,MAAM,CAAC,EACxD;IACJZ,UAAU,CAACe,GAAG,CAACH,MAAM,CAAC;IACtB,MAAMI,UAAU,GAAGX,YAAY,CAACO,MAAM,EAAED,UAAU,CAAC;IACnD,MAAMM,YAAY,GAAGA,CAACC,QAAQ,EAAEC,OAAO,KAAK;MACxCC,MAAM,CAACC,mBAAmB,CAAC,WAAW,EAAEC,WAAW,CAAC;MACpDF,MAAM,CAACC,mBAAmB,CAAC,eAAe,EAAEE,eAAe,CAAC;MAC5D,IAAI,CAACtB,iBAAiB,CAACiB,QAAQ,CAAC,IAAI,CAAClB,UAAU,CAACc,GAAG,CAACF,MAAM,CAAC,EAAE;QACzD;MACJ;MACAZ,UAAU,CAACwB,MAAM,CAACZ,MAAM,CAAC;MACzB,IAAI,OAAOI,UAAU,KAAK,UAAU,EAAE;QAClCA,UAAU,CAACE,QAAQ,EAAE;UAAEC;QAAQ,CAAC,CAAC;MACrC;IACJ,CAAC;IACD,MAAMG,WAAW,GAAIG,OAAO,IAAK;MAC7BR,YAAY,CAACQ,OAAO,EAAEb,MAAM,KAAKQ,MAAM,IACnCR,MAAM,KAAKc,QAAQ,IACnBpB,OAAO,CAACqB,eAAe,IACvBhC,aAAa,CAACiB,MAAM,EAAEa,OAAO,CAACb,MAAM,CAAC,CAAC;IAC9C,CAAC;IACD,MAAMW,eAAe,GAAIK,WAAW,IAAK;MACrCX,YAAY,CAACW,WAAW,EAAE,KAAK,CAAC;IACpC,CAAC;IACDR,MAAM,CAACS,gBAAgB,CAAC,WAAW,EAAEP,WAAW,EAAEd,YAAY,CAAC;IAC/DY,MAAM,CAACS,gBAAgB,CAAC,eAAe,EAAEN,eAAe,EAAEf,YAAY,CAAC;EAC3E,CAAC;EACDD,OAAO,CAACuB,OAAO,CAAElB,MAAM,IAAK;IACxB,MAAMmB,iBAAiB,GAAGzB,OAAO,CAACqB,eAAe,GAAGP,MAAM,GAAGR,MAAM;IACnEmB,iBAAiB,CAACF,gBAAgB,CAAC,aAAa,EAAEnB,UAAU,EAAEF,YAAY,CAAC;IAC3E,IAAII,MAAM,YAAYoB,WAAW,EAAE;MAC/BpB,MAAM,CAACiB,gBAAgB,CAAC,OAAO,EAAG3B,KAAK,IAAKH,mBAAmB,CAACG,KAAK,EAAEM,YAAY,CAAC,CAAC;MACrF,IAAI,CAACV,2BAA2B,CAACc,MAAM,CAAC,IACpC,CAACA,MAAM,CAACqB,YAAY,CAAC,UAAU,CAAC,EAAE;QAClCrB,MAAM,CAACsB,QAAQ,GAAG,CAAC;MACvB;IACJ;EACJ,CAAC,CAAC;EACF,OAAOzB,YAAY;AACvB;AAEA,SAASN,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}