{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { createSvgIcon } from \"../../utils/index.js\";\n\n/**\n * @ignore - internal component.\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z\"\n}), 'Add');", "map": {"version": 3, "names": ["React", "createSvgIcon", "jsx", "_jsx", "d"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/@mui/material/esm/internal/svg-icons/Add.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { createSvgIcon } from \"../../utils/index.js\";\n\n/**\n * @ignore - internal component.\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z\"\n}), 'Add');"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,sBAAsB;;AAEpD;AACA;AACA;AACA,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACrDC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}