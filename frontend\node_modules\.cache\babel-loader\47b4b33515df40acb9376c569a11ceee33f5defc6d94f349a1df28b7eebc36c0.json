{"ast": null, "code": "const MotionGlobalConfig = {\n  skipAnimations: false,\n  useManualTiming: false\n};\nexport { MotionGlobalConfig };", "map": {"version": 3, "names": ["MotionGlobalConfig", "skipAnimations", "useManualTiming"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/motion-utils/dist/es/global-config.mjs"], "sourcesContent": ["const MotionGlobalConfig = {\n    skipAnimations: false,\n    useManualTiming: false,\n};\n\nexport { MotionGlobalConfig };\n"], "mappings": "AAAA,MAAMA,kBAAkB,GAAG;EACvBC,cAAc,EAAE,KAAK;EACrBC,eAAe,EAAE;AACrB,CAAC;AAED,SAASF,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}