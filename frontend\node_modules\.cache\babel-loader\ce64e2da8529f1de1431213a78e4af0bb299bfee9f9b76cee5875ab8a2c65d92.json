{"ast": null, "code": "import { resolveElements, getValueTransition, NativeAnimation } from 'motion-dom';\nimport { invariant, secondsToMilliseconds } from 'motion-utils';\nfunction animateElements(elementOrSelector, keyframes, options, scope) {\n  const elements = resolveElements(elementOrSelector, scope);\n  const numElements = elements.length;\n  invariant(Bo<PERSON>an(numElements), \"No valid element provided.\");\n  const animations = [];\n  for (let i = 0; i < numElements; i++) {\n    const element = elements[i];\n    const elementTransition = {\n      ...options\n    };\n    /**\n     * Resolve stagger function if provided.\n     */\n    if (typeof elementTransition.delay === \"function\") {\n      elementTransition.delay = elementTransition.delay(i, numElements);\n    }\n    for (const valueName in keyframes) {\n      const valueKeyframes = keyframes[valueName];\n      const valueOptions = {\n        ...getValueTransition(elementTransition, valueName)\n      };\n      valueOptions.duration && (valueOptions.duration = secondsToMilliseconds(valueOptions.duration));\n      valueOptions.delay && (valueOptions.delay = secondsToMilliseconds(valueOptions.delay));\n      animations.push(new NativeAnimation({\n        element,\n        name: valueName,\n        keyframes: valueKeyframes,\n        transition: valueOptions,\n        allowFlatten: !elementTransition.type && !elementTransition.ease\n      }));\n    }\n  }\n  return animations;\n}\nexport { animateElements };", "map": {"version": 3, "names": ["resolveElements", "getValueTransition", "NativeAnimation", "invariant", "secondsToMilliseconds", "animateElements", "elementOrSelector", "keyframes", "options", "scope", "elements", "numElements", "length", "Boolean", "animations", "i", "element", "elementTransition", "delay", "valueName", "valueKeyframes", "valueOptions", "duration", "push", "name", "transition", "allowFlatten", "type", "ease"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/framer-motion/dist/es/animation/animators/waapi/animate-elements.mjs"], "sourcesContent": ["import { resolveElements, getValueTransition, NativeAnimation } from 'motion-dom';\nimport { invariant, secondsToMilliseconds } from 'motion-utils';\n\nfunction animateElements(elementOrSelector, keyframes, options, scope) {\n    const elements = resolveElements(elementOrSelector, scope);\n    const numElements = elements.length;\n    invariant(Bo<PERSON>an(numElements), \"No valid element provided.\");\n    const animations = [];\n    for (let i = 0; i < numElements; i++) {\n        const element = elements[i];\n        const elementTransition = { ...options };\n        /**\n         * Resolve stagger function if provided.\n         */\n        if (typeof elementTransition.delay === \"function\") {\n            elementTransition.delay = elementTransition.delay(i, numElements);\n        }\n        for (const valueName in keyframes) {\n            const valueKeyframes = keyframes[valueName];\n            const valueOptions = {\n                ...getValueTransition(elementTransition, valueName),\n            };\n            valueOptions.duration && (valueOptions.duration = secondsToMilliseconds(valueOptions.duration));\n            valueOptions.delay && (valueOptions.delay = secondsToMilliseconds(valueOptions.delay));\n            animations.push(new NativeAnimation({\n                element,\n                name: valueName,\n                keyframes: valueKeyframes,\n                transition: valueOptions,\n                allowFlatten: !elementTransition.type && !elementTransition.ease,\n            }));\n        }\n    }\n    return animations;\n}\n\nexport { animateElements };\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,kBAAkB,EAAEC,eAAe,QAAQ,YAAY;AACjF,SAASC,SAAS,EAAEC,qBAAqB,QAAQ,cAAc;AAE/D,SAASC,eAAeA,CAACC,iBAAiB,EAAEC,SAAS,EAAEC,OAAO,EAAEC,KAAK,EAAE;EACnE,MAAMC,QAAQ,GAAGV,eAAe,CAACM,iBAAiB,EAAEG,KAAK,CAAC;EAC1D,MAAME,WAAW,GAAGD,QAAQ,CAACE,MAAM;EACnCT,SAAS,CAACU,OAAO,CAACF,WAAW,CAAC,EAAE,4BAA4B,CAAC;EAC7D,MAAMG,UAAU,GAAG,EAAE;EACrB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,WAAW,EAAEI,CAAC,EAAE,EAAE;IAClC,MAAMC,OAAO,GAAGN,QAAQ,CAACK,CAAC,CAAC;IAC3B,MAAME,iBAAiB,GAAG;MAAE,GAAGT;IAAQ,CAAC;IACxC;AACR;AACA;IACQ,IAAI,OAAOS,iBAAiB,CAACC,KAAK,KAAK,UAAU,EAAE;MAC/CD,iBAAiB,CAACC,KAAK,GAAGD,iBAAiB,CAACC,KAAK,CAACH,CAAC,EAAEJ,WAAW,CAAC;IACrE;IACA,KAAK,MAAMQ,SAAS,IAAIZ,SAAS,EAAE;MAC/B,MAAMa,cAAc,GAAGb,SAAS,CAACY,SAAS,CAAC;MAC3C,MAAME,YAAY,GAAG;QACjB,GAAGpB,kBAAkB,CAACgB,iBAAiB,EAAEE,SAAS;MACtD,CAAC;MACDE,YAAY,CAACC,QAAQ,KAAKD,YAAY,CAACC,QAAQ,GAAGlB,qBAAqB,CAACiB,YAAY,CAACC,QAAQ,CAAC,CAAC;MAC/FD,YAAY,CAACH,KAAK,KAAKG,YAAY,CAACH,KAAK,GAAGd,qBAAqB,CAACiB,YAAY,CAACH,KAAK,CAAC,CAAC;MACtFJ,UAAU,CAACS,IAAI,CAAC,IAAIrB,eAAe,CAAC;QAChCc,OAAO;QACPQ,IAAI,EAAEL,SAAS;QACfZ,SAAS,EAAEa,cAAc;QACzBK,UAAU,EAAEJ,YAAY;QACxBK,YAAY,EAAE,CAACT,iBAAiB,CAACU,IAAI,IAAI,CAACV,iBAAiB,CAACW;MAChE,CAAC,CAAC,CAAC;IACP;EACJ;EACA,OAAOd,UAAU;AACrB;AAEA,SAAST,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}