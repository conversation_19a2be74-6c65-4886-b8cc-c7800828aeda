{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { isFragment } from 'react-is';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport refType from '@mui/utils/refType';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport { styled, useTheme } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport debounce from \"../utils/debounce.js\";\nimport animate from \"../internal/animate.js\";\nimport ScrollbarSize from \"./ScrollbarSize.js\";\nimport TabScrollButton from \"../TabScrollButton/index.js\";\nimport useEventCallback from \"../utils/useEventCallback.js\";\nimport tabsClasses, { getTabsUtilityClass } from \"./tabsClasses.js\";\nimport ownerDocument from \"../utils/ownerDocument.js\";\nimport ownerWindow from \"../utils/ownerWindow.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst nextItem = (list, item) => {\n  if (list === item) {\n    return list.firstChild;\n  }\n  if (item && item.nextElementSibling) {\n    return item.nextElementSibling;\n  }\n  return list.firstChild;\n};\nconst previousItem = (list, item) => {\n  if (list === item) {\n    return list.lastChild;\n  }\n  if (item && item.previousElementSibling) {\n    return item.previousElementSibling;\n  }\n  return list.lastChild;\n};\nconst moveFocus = (list, currentFocus, traversalFunction) => {\n  let wrappedOnce = false;\n  let nextFocus = traversalFunction(list, currentFocus);\n  while (nextFocus) {\n    // Prevent infinite loop.\n    if (nextFocus === list.firstChild) {\n      if (wrappedOnce) {\n        return;\n      }\n      wrappedOnce = true;\n    }\n\n    // Same logic as useAutocomplete.js\n    const nextFocusDisabled = nextFocus.disabled || nextFocus.getAttribute('aria-disabled') === 'true';\n    if (!nextFocus.hasAttribute('tabindex') || nextFocusDisabled) {\n      // Move to the next element.\n      nextFocus = traversalFunction(list, nextFocus);\n    } else {\n      nextFocus.focus();\n      return;\n    }\n  }\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    vertical,\n    fixed,\n    hideScrollbar,\n    scrollableX,\n    scrollableY,\n    centered,\n    scrollButtonsHideMobile,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', vertical && 'vertical'],\n    scroller: ['scroller', fixed && 'fixed', hideScrollbar && 'hideScrollbar', scrollableX && 'scrollableX', scrollableY && 'scrollableY'],\n    list: ['list', 'flexContainer', vertical && 'flexContainerVertical', vertical && 'vertical', centered && 'centered'],\n    indicator: ['indicator'],\n    scrollButtons: ['scrollButtons', scrollButtonsHideMobile && 'scrollButtonsHideMobile'],\n    scrollableX: [scrollableX && 'scrollableX'],\n    hideScrollbar: [hideScrollbar && 'hideScrollbar']\n  };\n  return composeClasses(slots, getTabsUtilityClass, classes);\n};\nconst TabsRoot = styled('div', {\n  name: 'MuiTabs',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [`& .${tabsClasses.scrollButtons}`]: styles.scrollButtons\n    }, {\n      [`& .${tabsClasses.scrollButtons}`]: ownerState.scrollButtonsHideMobile && styles.scrollButtonsHideMobile\n    }, styles.root, ownerState.vertical && styles.vertical];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  overflow: 'hidden',\n  minHeight: 48,\n  // Add iOS momentum scrolling for iOS < 13.0\n  WebkitOverflowScrolling: 'touch',\n  display: 'flex',\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.vertical,\n    style: {\n      flexDirection: 'column'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.scrollButtonsHideMobile,\n    style: {\n      [`& .${tabsClasses.scrollButtons}`]: {\n        [theme.breakpoints.down('sm')]: {\n          display: 'none'\n        }\n      }\n    }\n  }]\n})));\nconst TabsScroller = styled('div', {\n  name: 'MuiTabs',\n  slot: 'Scroller',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.scroller, ownerState.fixed && styles.fixed, ownerState.hideScrollbar && styles.hideScrollbar, ownerState.scrollableX && styles.scrollableX, ownerState.scrollableY && styles.scrollableY];\n  }\n})({\n  position: 'relative',\n  display: 'inline-block',\n  flex: '1 1 auto',\n  whiteSpace: 'nowrap',\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.fixed,\n    style: {\n      overflowX: 'hidden',\n      width: '100%'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.hideScrollbar,\n    style: {\n      // Hide dimensionless scrollbar on macOS\n      scrollbarWidth: 'none',\n      // Firefox\n      '&::-webkit-scrollbar': {\n        display: 'none' // Safari + Chrome\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.scrollableX,\n    style: {\n      overflowX: 'auto',\n      overflowY: 'hidden'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.scrollableY,\n    style: {\n      overflowY: 'auto',\n      overflowX: 'hidden'\n    }\n  }]\n});\nconst List = styled('div', {\n  name: 'MuiTabs',\n  slot: 'List',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.list, styles.flexContainer, ownerState.vertical && styles.flexContainerVertical, ownerState.centered && styles.centered];\n  }\n})({\n  display: 'flex',\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.vertical,\n    style: {\n      flexDirection: 'column'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.centered,\n    style: {\n      justifyContent: 'center'\n    }\n  }]\n});\nconst TabsIndicator = styled('span', {\n  name: 'MuiTabs',\n  slot: 'Indicator',\n  overridesResolver: (props, styles) => styles.indicator\n})(memoTheme(({\n  theme\n}) => ({\n  position: 'absolute',\n  height: 2,\n  bottom: 0,\n  width: '100%',\n  transition: theme.transitions.create(),\n  variants: [{\n    props: {\n      indicatorColor: 'primary'\n    },\n    style: {\n      backgroundColor: (theme.vars || theme).palette.primary.main\n    }\n  }, {\n    props: {\n      indicatorColor: 'secondary'\n    },\n    style: {\n      backgroundColor: (theme.vars || theme).palette.secondary.main\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.vertical,\n    style: {\n      height: '100%',\n      width: 2,\n      right: 0\n    }\n  }]\n})));\nconst TabsScrollbarSize = styled(ScrollbarSize)({\n  overflowX: 'auto',\n  overflowY: 'hidden',\n  // Hide dimensionless scrollbar on macOS\n  scrollbarWidth: 'none',\n  // Firefox\n  '&::-webkit-scrollbar': {\n    display: 'none' // Safari + Chrome\n  }\n});\nconst defaultIndicatorStyle = {};\nlet warnedOnceTabPresent = false;\nconst Tabs = /*#__PURE__*/React.forwardRef(function Tabs(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiTabs'\n  });\n  const theme = useTheme();\n  const isRtl = useRtl();\n  const {\n    'aria-label': ariaLabel,\n    'aria-labelledby': ariaLabelledBy,\n    action,\n    centered = false,\n    children: childrenProp,\n    className,\n    component = 'div',\n    allowScrollButtonsMobile = false,\n    indicatorColor = 'primary',\n    onChange,\n    orientation = 'horizontal',\n    ScrollButtonComponent,\n    // TODO: remove in v7 (deprecated in v6)\n    scrollButtons = 'auto',\n    selectionFollowsFocus,\n    slots = {},\n    slotProps = {},\n    TabIndicatorProps = {},\n    // TODO: remove in v7 (deprecated in v6)\n    TabScrollButtonProps = {},\n    // TODO: remove in v7 (deprecated in v6)\n    textColor = 'primary',\n    value,\n    variant = 'standard',\n    visibleScrollbar = false,\n    ...other\n  } = props;\n  const scrollable = variant === 'scrollable';\n  const vertical = orientation === 'vertical';\n  const scrollStart = vertical ? 'scrollTop' : 'scrollLeft';\n  const start = vertical ? 'top' : 'left';\n  const end = vertical ? 'bottom' : 'right';\n  const clientSize = vertical ? 'clientHeight' : 'clientWidth';\n  const size = vertical ? 'height' : 'width';\n  const ownerState = {\n    ...props,\n    component,\n    allowScrollButtonsMobile,\n    indicatorColor,\n    orientation,\n    vertical,\n    scrollButtons,\n    textColor,\n    variant,\n    visibleScrollbar,\n    fixed: !scrollable,\n    hideScrollbar: scrollable && !visibleScrollbar,\n    scrollableX: scrollable && !vertical,\n    scrollableY: scrollable && vertical,\n    centered: centered && !scrollable,\n    scrollButtonsHideMobile: !allowScrollButtonsMobile\n  };\n  const classes = useUtilityClasses(ownerState);\n  const startScrollButtonIconProps = useSlotProps({\n    elementType: slots.StartScrollButtonIcon,\n    externalSlotProps: slotProps.startScrollButtonIcon,\n    ownerState\n  });\n  const endScrollButtonIconProps = useSlotProps({\n    elementType: slots.EndScrollButtonIcon,\n    externalSlotProps: slotProps.endScrollButtonIcon,\n    ownerState\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    if (centered && scrollable) {\n      console.error('MUI: You can not use the `centered={true}` and `variant=\"scrollable\"` properties ' + 'at the same time on a `Tabs` component.');\n    }\n  }\n  const [mounted, setMounted] = React.useState(false);\n  const [indicatorStyle, setIndicatorStyle] = React.useState(defaultIndicatorStyle);\n  const [displayStartScroll, setDisplayStartScroll] = React.useState(false);\n  const [displayEndScroll, setDisplayEndScroll] = React.useState(false);\n  const [updateScrollObserver, setUpdateScrollObserver] = React.useState(false);\n  const [scrollerStyle, setScrollerStyle] = React.useState({\n    overflow: 'hidden',\n    scrollbarWidth: 0\n  });\n  const valueToIndex = new Map();\n  const tabsRef = React.useRef(null);\n  const tabListRef = React.useRef(null);\n  const externalForwardedProps = {\n    slots,\n    slotProps: {\n      indicator: TabIndicatorProps,\n      scrollButton: TabScrollButtonProps,\n      ...slotProps\n    }\n  };\n  const getTabsMeta = () => {\n    const tabsNode = tabsRef.current;\n    let tabsMeta;\n    if (tabsNode) {\n      const rect = tabsNode.getBoundingClientRect();\n      // create a new object with ClientRect class props + scrollLeft\n      tabsMeta = {\n        clientWidth: tabsNode.clientWidth,\n        scrollLeft: tabsNode.scrollLeft,\n        scrollTop: tabsNode.scrollTop,\n        scrollWidth: tabsNode.scrollWidth,\n        top: rect.top,\n        bottom: rect.bottom,\n        left: rect.left,\n        right: rect.right\n      };\n    }\n    let tabMeta;\n    if (tabsNode && value !== false) {\n      const children = tabListRef.current.children;\n      if (children.length > 0) {\n        const tab = children[valueToIndex.get(value)];\n        if (process.env.NODE_ENV !== 'production') {\n          if (!tab) {\n            console.error([`MUI: The \\`value\\` provided to the Tabs component is invalid.`, `None of the Tabs' children match with \"${value}\".`, valueToIndex.keys ? `You can provide one of the following values: ${Array.from(valueToIndex.keys()).join(', ')}.` : null].join('\\n'));\n          }\n        }\n        tabMeta = tab ? tab.getBoundingClientRect() : null;\n        if (process.env.NODE_ENV !== 'production') {\n          if (process.env.NODE_ENV !== 'test' && !warnedOnceTabPresent && tabMeta && tabMeta.width === 0 && tabMeta.height === 0 &&\n          // if the whole Tabs component is hidden, don't warn\n          tabsMeta.clientWidth !== 0) {\n            tabsMeta = null;\n            console.error(['MUI: The `value` provided to the Tabs component is invalid.', `The Tab with this \\`value\\` (\"${value}\") is not part of the document layout.`, \"Make sure the tab item is present in the document or that it's not `display: none`.\"].join('\\n'));\n            warnedOnceTabPresent = true;\n          }\n        }\n      }\n    }\n    return {\n      tabsMeta,\n      tabMeta\n    };\n  };\n  const updateIndicatorState = useEventCallback(() => {\n    const {\n      tabsMeta,\n      tabMeta\n    } = getTabsMeta();\n    let startValue = 0;\n    let startIndicator;\n    if (vertical) {\n      startIndicator = 'top';\n      if (tabMeta && tabsMeta) {\n        startValue = tabMeta.top - tabsMeta.top + tabsMeta.scrollTop;\n      }\n    } else {\n      startIndicator = isRtl ? 'right' : 'left';\n      if (tabMeta && tabsMeta) {\n        startValue = (isRtl ? -1 : 1) * (tabMeta[startIndicator] - tabsMeta[startIndicator] + tabsMeta.scrollLeft);\n      }\n    }\n    const newIndicatorStyle = {\n      [startIndicator]: startValue,\n      // May be wrong until the font is loaded.\n      [size]: tabMeta ? tabMeta[size] : 0\n    };\n    if (typeof indicatorStyle[startIndicator] !== 'number' || typeof indicatorStyle[size] !== 'number') {\n      setIndicatorStyle(newIndicatorStyle);\n    } else {\n      const dStart = Math.abs(indicatorStyle[startIndicator] - newIndicatorStyle[startIndicator]);\n      const dSize = Math.abs(indicatorStyle[size] - newIndicatorStyle[size]);\n      if (dStart >= 1 || dSize >= 1) {\n        setIndicatorStyle(newIndicatorStyle);\n      }\n    }\n  });\n  const scroll = (scrollValue, {\n    animation = true\n  } = {}) => {\n    if (animation) {\n      animate(scrollStart, tabsRef.current, scrollValue, {\n        duration: theme.transitions.duration.standard\n      });\n    } else {\n      tabsRef.current[scrollStart] = scrollValue;\n    }\n  };\n  const moveTabsScroll = delta => {\n    let scrollValue = tabsRef.current[scrollStart];\n    if (vertical) {\n      scrollValue += delta;\n    } else {\n      scrollValue += delta * (isRtl ? -1 : 1);\n    }\n    scroll(scrollValue);\n  };\n  const getScrollSize = () => {\n    const containerSize = tabsRef.current[clientSize];\n    let totalSize = 0;\n    const children = Array.from(tabListRef.current.children);\n    for (let i = 0; i < children.length; i += 1) {\n      const tab = children[i];\n      if (totalSize + tab[clientSize] > containerSize) {\n        // If the first item is longer than the container size, then only scroll\n        // by the container size.\n        if (i === 0) {\n          totalSize = containerSize;\n        }\n        break;\n      }\n      totalSize += tab[clientSize];\n    }\n    return totalSize;\n  };\n  const handleStartScrollClick = () => {\n    moveTabsScroll(-1 * getScrollSize());\n  };\n  const handleEndScrollClick = () => {\n    moveTabsScroll(getScrollSize());\n  };\n  const [ScrollbarSlot, {\n    onChange: scrollbarOnChange,\n    ...scrollbarSlotProps\n  }] = useSlot('scrollbar', {\n    className: clsx(classes.scrollableX, classes.hideScrollbar),\n    elementType: TabsScrollbarSize,\n    shouldForwardComponentProp: true,\n    externalForwardedProps,\n    ownerState\n  });\n\n  // TODO Remove <ScrollbarSize /> as browser support for hiding the scrollbar\n  // with CSS improves.\n  const handleScrollbarSizeChange = React.useCallback(scrollbarWidth => {\n    scrollbarOnChange?.(scrollbarWidth);\n    setScrollerStyle({\n      overflow: null,\n      scrollbarWidth\n    });\n  }, [scrollbarOnChange]);\n  const [ScrollButtonsSlot, scrollButtonSlotProps] = useSlot('scrollButtons', {\n    className: clsx(classes.scrollButtons, TabScrollButtonProps.className),\n    elementType: TabScrollButton,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      orientation,\n      slots: {\n        StartScrollButtonIcon: slots.startScrollButtonIcon || slots.StartScrollButtonIcon,\n        EndScrollButtonIcon: slots.endScrollButtonIcon || slots.EndScrollButtonIcon\n      },\n      slotProps: {\n        startScrollButtonIcon: startScrollButtonIconProps,\n        endScrollButtonIcon: endScrollButtonIconProps\n      }\n    }\n  });\n  const getConditionalElements = () => {\n    const conditionalElements = {};\n    conditionalElements.scrollbarSizeListener = scrollable ? /*#__PURE__*/_jsx(ScrollbarSlot, {\n      ...scrollbarSlotProps,\n      onChange: handleScrollbarSizeChange\n    }) : null;\n    const scrollButtonsActive = displayStartScroll || displayEndScroll;\n    const showScrollButtons = scrollable && (scrollButtons === 'auto' && scrollButtonsActive || scrollButtons === true);\n    conditionalElements.scrollButtonStart = showScrollButtons ? /*#__PURE__*/_jsx(ScrollButtonsSlot, {\n      direction: isRtl ? 'right' : 'left',\n      onClick: handleStartScrollClick,\n      disabled: !displayStartScroll,\n      ...scrollButtonSlotProps\n    }) : null;\n    conditionalElements.scrollButtonEnd = showScrollButtons ? /*#__PURE__*/_jsx(ScrollButtonsSlot, {\n      direction: isRtl ? 'left' : 'right',\n      onClick: handleEndScrollClick,\n      disabled: !displayEndScroll,\n      ...scrollButtonSlotProps\n    }) : null;\n    return conditionalElements;\n  };\n  const scrollSelectedIntoView = useEventCallback(animation => {\n    const {\n      tabsMeta,\n      tabMeta\n    } = getTabsMeta();\n    if (!tabMeta || !tabsMeta) {\n      return;\n    }\n    if (tabMeta[start] < tabsMeta[start]) {\n      // left side of button is out of view\n      const nextScrollStart = tabsMeta[scrollStart] + (tabMeta[start] - tabsMeta[start]);\n      scroll(nextScrollStart, {\n        animation\n      });\n    } else if (tabMeta[end] > tabsMeta[end]) {\n      // right side of button is out of view\n      const nextScrollStart = tabsMeta[scrollStart] + (tabMeta[end] - tabsMeta[end]);\n      scroll(nextScrollStart, {\n        animation\n      });\n    }\n  });\n  const updateScrollButtonState = useEventCallback(() => {\n    if (scrollable && scrollButtons !== false) {\n      setUpdateScrollObserver(!updateScrollObserver);\n    }\n  });\n  React.useEffect(() => {\n    const handleResize = debounce(() => {\n      // If the Tabs component is replaced by Suspense with a fallback, the last\n      // ResizeObserver's handler that runs because of the change in the layout is trying to\n      // access a dom node that is no longer there (as the fallback component is being shown instead).\n      // See https://github.com/mui/material-ui/issues/33276\n      // TODO: Add tests that will ensure the component is not failing when\n      // replaced by Suspense with a fallback, once React is updated to version 18\n      if (tabsRef.current) {\n        updateIndicatorState();\n      }\n    });\n    let resizeObserver;\n\n    /**\n     * @type {MutationCallback}\n     */\n    const handleMutation = records => {\n      records.forEach(record => {\n        record.removedNodes.forEach(item => {\n          resizeObserver?.unobserve(item);\n        });\n        record.addedNodes.forEach(item => {\n          resizeObserver?.observe(item);\n        });\n      });\n      handleResize();\n      updateScrollButtonState();\n    };\n    const win = ownerWindow(tabsRef.current);\n    win.addEventListener('resize', handleResize);\n    let mutationObserver;\n    if (typeof ResizeObserver !== 'undefined') {\n      resizeObserver = new ResizeObserver(handleResize);\n      Array.from(tabListRef.current.children).forEach(child => {\n        resizeObserver.observe(child);\n      });\n    }\n    if (typeof MutationObserver !== 'undefined') {\n      mutationObserver = new MutationObserver(handleMutation);\n      mutationObserver.observe(tabListRef.current, {\n        childList: true\n      });\n    }\n    return () => {\n      handleResize.clear();\n      win.removeEventListener('resize', handleResize);\n      mutationObserver?.disconnect();\n      resizeObserver?.disconnect();\n    };\n  }, [updateIndicatorState, updateScrollButtonState]);\n\n  /**\n   * Toggle visibility of start and end scroll buttons\n   * Using IntersectionObserver on first and last Tabs.\n   */\n  React.useEffect(() => {\n    const tabListChildren = Array.from(tabListRef.current.children);\n    const length = tabListChildren.length;\n    if (typeof IntersectionObserver !== 'undefined' && length > 0 && scrollable && scrollButtons !== false) {\n      const firstTab = tabListChildren[0];\n      const lastTab = tabListChildren[length - 1];\n      const observerOptions = {\n        root: tabsRef.current,\n        threshold: 0.99\n      };\n      const handleScrollButtonStart = entries => {\n        setDisplayStartScroll(!entries[0].isIntersecting);\n      };\n      const firstObserver = new IntersectionObserver(handleScrollButtonStart, observerOptions);\n      firstObserver.observe(firstTab);\n      const handleScrollButtonEnd = entries => {\n        setDisplayEndScroll(!entries[0].isIntersecting);\n      };\n      const lastObserver = new IntersectionObserver(handleScrollButtonEnd, observerOptions);\n      lastObserver.observe(lastTab);\n      return () => {\n        firstObserver.disconnect();\n        lastObserver.disconnect();\n      };\n    }\n    return undefined;\n  }, [scrollable, scrollButtons, updateScrollObserver, childrenProp?.length]);\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n  React.useEffect(() => {\n    updateIndicatorState();\n  });\n  React.useEffect(() => {\n    // Don't animate on the first render.\n    scrollSelectedIntoView(defaultIndicatorStyle !== indicatorStyle);\n  }, [scrollSelectedIntoView, indicatorStyle]);\n  React.useImperativeHandle(action, () => ({\n    updateIndicator: updateIndicatorState,\n    updateScrollButtons: updateScrollButtonState\n  }), [updateIndicatorState, updateScrollButtonState]);\n  const [IndicatorSlot, indicatorSlotProps] = useSlot('indicator', {\n    className: clsx(classes.indicator, TabIndicatorProps.className),\n    elementType: TabsIndicator,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      style: indicatorStyle\n    }\n  });\n  const indicator = /*#__PURE__*/_jsx(IndicatorSlot, {\n    ...indicatorSlotProps\n  });\n  let childIndex = 0;\n  const children = React.Children.map(childrenProp, child => {\n    if (! /*#__PURE__*/React.isValidElement(child)) {\n      return null;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (isFragment(child)) {\n        console.error([\"MUI: The Tabs component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n      }\n    }\n    const childValue = child.props.value === undefined ? childIndex : child.props.value;\n    valueToIndex.set(childValue, childIndex);\n    const selected = childValue === value;\n    childIndex += 1;\n    return /*#__PURE__*/React.cloneElement(child, {\n      fullWidth: variant === 'fullWidth',\n      indicator: selected && !mounted && indicator,\n      selected,\n      selectionFollowsFocus,\n      onChange,\n      textColor,\n      value: childValue,\n      ...(childIndex === 1 && value === false && !child.props.tabIndex ? {\n        tabIndex: 0\n      } : {})\n    });\n  });\n  const handleKeyDown = event => {\n    // Check if a modifier key (Alt, Shift, Ctrl, Meta) is pressed\n    if (event.altKey || event.shiftKey || event.ctrlKey || event.metaKey) {\n      return;\n    }\n    const list = tabListRef.current;\n    const currentFocus = ownerDocument(list).activeElement;\n    // Keyboard navigation assumes that [role=\"tab\"] are siblings\n    // though we might warn in the future about nested, interactive elements\n    // as a a11y violation\n    const role = currentFocus.getAttribute('role');\n    if (role !== 'tab') {\n      return;\n    }\n    let previousItemKey = orientation === 'horizontal' ? 'ArrowLeft' : 'ArrowUp';\n    let nextItemKey = orientation === 'horizontal' ? 'ArrowRight' : 'ArrowDown';\n    if (orientation === 'horizontal' && isRtl) {\n      // swap previousItemKey with nextItemKey\n      previousItemKey = 'ArrowRight';\n      nextItemKey = 'ArrowLeft';\n    }\n    switch (event.key) {\n      case previousItemKey:\n        event.preventDefault();\n        moveFocus(list, currentFocus, previousItem);\n        break;\n      case nextItemKey:\n        event.preventDefault();\n        moveFocus(list, currentFocus, nextItem);\n        break;\n      case 'Home':\n        event.preventDefault();\n        moveFocus(list, null, nextItem);\n        break;\n      case 'End':\n        event.preventDefault();\n        moveFocus(list, null, previousItem);\n        break;\n      default:\n        break;\n    }\n  };\n  const conditionalElements = getConditionalElements();\n  const [RootSlot, rootSlotProps] = useSlot('root', {\n    ref,\n    className: clsx(classes.root, className),\n    elementType: TabsRoot,\n    externalForwardedProps: {\n      ...externalForwardedProps,\n      ...other,\n      component\n    },\n    ownerState\n  });\n  const [ScrollerSlot, scrollerSlotProps] = useSlot('scroller', {\n    ref: tabsRef,\n    className: classes.scroller,\n    elementType: TabsScroller,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      style: {\n        overflow: scrollerStyle.overflow,\n        [vertical ? `margin${isRtl ? 'Left' : 'Right'}` : 'marginBottom']: visibleScrollbar ? undefined : -scrollerStyle.scrollbarWidth\n      }\n    }\n  });\n  const [ListSlot, listSlotProps] = useSlot('list', {\n    ref: tabListRef,\n    className: clsx(classes.list, classes.flexContainer),\n    elementType: List,\n    externalForwardedProps,\n    ownerState,\n    getSlotProps: handlers => ({\n      ...handlers,\n      onKeyDown: event => {\n        handleKeyDown(event);\n        handlers.onKeyDown?.(event);\n      }\n    })\n  });\n  return /*#__PURE__*/_jsxs(RootSlot, {\n    ...rootSlotProps,\n    children: [conditionalElements.scrollButtonStart, conditionalElements.scrollbarSizeListener, /*#__PURE__*/_jsxs(ScrollerSlot, {\n      ...scrollerSlotProps,\n      children: [/*#__PURE__*/_jsx(ListSlot, {\n        \"aria-label\": ariaLabel,\n        \"aria-labelledby\": ariaLabelledBy,\n        \"aria-orientation\": orientation === 'vertical' ? 'vertical' : null,\n        role: \"tablist\",\n        ...listSlotProps,\n        children: children\n      }), mounted && indicator]\n    }), conditionalElements.scrollButtonEnd]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Tabs.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Callback fired when the component mounts.\n   * This is useful when you want to trigger an action programmatically.\n   * It supports two actions: `updateIndicator()` and `updateScrollButtons()`\n   *\n   * @param {object} actions This object contains all possible actions\n   * that can be triggered programmatically.\n   */\n  action: refType,\n  /**\n   * If `true`, the scroll buttons aren't forced hidden on mobile.\n   * By default the scroll buttons are hidden on mobile and takes precedence over `scrollButtons`.\n   * @default false\n   */\n  allowScrollButtonsMobile: PropTypes.bool,\n  /**\n   * The label for the Tabs as a string.\n   */\n  'aria-label': PropTypes.string,\n  /**\n   * An id or list of ids separated by a space that label the Tabs.\n   */\n  'aria-labelledby': PropTypes.string,\n  /**\n   * If `true`, the tabs are centered.\n   * This prop is intended for large views.\n   * @default false\n   */\n  centered: PropTypes.bool,\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Determines the color of the indicator.\n   * @default 'primary'\n   */\n  indicatorColor: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['primary', 'secondary']), PropTypes.string]),\n  /**\n   * Callback fired when the value changes.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback. **Warning**: This is a generic event not a change event.\n   * @param {any} value We default to the index of the child (number)\n   */\n  onChange: PropTypes.func,\n  /**\n   * The component orientation (layout flow direction).\n   * @default 'horizontal'\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * The component used to render the scroll buttons.\n   * @deprecated use the `slots.scrollButtons` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default TabScrollButton\n   */\n  ScrollButtonComponent: PropTypes.elementType,\n  /**\n   * Determine behavior of scroll buttons when tabs are set to scroll:\n   *\n   * - `auto` will only present them when not all the items are visible.\n   * - `true` will always present them.\n   * - `false` will never present them.\n   *\n   * By default the scroll buttons are hidden on mobile.\n   * This behavior can be disabled with `allowScrollButtonsMobile`.\n   * @default 'auto'\n   */\n  scrollButtons: PropTypes /* @typescript-to-proptypes-ignore */.oneOf(['auto', false, true]),\n  /**\n   * If `true` the selected tab changes on focus. Otherwise it only\n   * changes on activation.\n   */\n  selectionFollowsFocus: PropTypes.bool,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    endScrollButtonIcon: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    indicator: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    list: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    scrollbar: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    scrollButtons: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    scroller: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    startScrollButtonIcon: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    endScrollButtonIcon: PropTypes.elementType,\n    EndScrollButtonIcon: PropTypes.elementType,\n    indicator: PropTypes.elementType,\n    list: PropTypes.elementType,\n    root: PropTypes.elementType,\n    scrollbar: PropTypes.elementType,\n    scrollButtons: PropTypes.elementType,\n    scroller: PropTypes.elementType,\n    startScrollButtonIcon: PropTypes.elementType,\n    StartScrollButtonIcon: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Props applied to the tab indicator element.\n   * @deprecated use the `slotProps.indicator` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default  {}\n   */\n  TabIndicatorProps: PropTypes.object,\n  /**\n   * Props applied to the [`TabScrollButton`](https://mui.com/material-ui/api/tab-scroll-button/) element.\n   * @deprecated use the `slotProps.scrollButtons` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default {}\n   */\n  TabScrollButtonProps: PropTypes.object,\n  /**\n   * Determines the color of the `Tab`.\n   * @default 'primary'\n   */\n  textColor: PropTypes.oneOf(['inherit', 'primary', 'secondary']),\n  /**\n   * The value of the currently selected `Tab`.\n   * If you don't want any selected `Tab`, you can set this prop to `false`.\n   */\n  value: PropTypes.any,\n  /**\n   * Determines additional display behavior of the tabs:\n   *\n   *  - `scrollable` will invoke scrolling properties and allow for horizontally\n   *  scrolling (or swiping) of the tab bar.\n   *  - `fullWidth` will make the tabs grow to use all the available space,\n   *  which should be used for small views, like on mobile.\n   *  - `standard` will render the default state.\n   * @default 'standard'\n   */\n  variant: PropTypes.oneOf(['fullWidth', 'scrollable', 'standard']),\n  /**\n   * If `true`, the scrollbar is visible. It can be useful when displaying\n   * a long vertical list of tabs.\n   * @default false\n   */\n  visibleScrollbar: PropTypes.bool\n} : void 0;\nexport default Tabs;", "map": {"version": 3, "names": ["React", "isFragment", "PropTypes", "clsx", "refType", "composeClasses", "useRtl", "useSlotProps", "styled", "useTheme", "memoTheme", "useDefaultProps", "debounce", "animate", "ScrollbarSize", "TabScrollButton", "useEventCallback", "tabsClasses", "getTabsUtilityClass", "ownerDocument", "ownerWindow", "useSlot", "jsx", "_jsx", "jsxs", "_jsxs", "nextItem", "list", "item", "<PERSON><PERSON><PERSON><PERSON>", "nextElement<PERSON><PERSON>ling", "previousItem", "<PERSON><PERSON><PERSON><PERSON>", "previousElementSibling", "moveFocus", "currentFocus", "traversalFunction", "wrappedOnce", "nextFocus", "nextFocusDisabled", "disabled", "getAttribute", "hasAttribute", "focus", "useUtilityClasses", "ownerState", "vertical", "fixed", "hideScrollbar", "scrollableX", "scrollableY", "centered", "scrollButtonsHideMobile", "classes", "slots", "root", "scroller", "indicator", "scrollButtons", "TabsRoot", "name", "slot", "overridesResolver", "props", "styles", "theme", "overflow", "minHeight", "WebkitOverflowScrolling", "display", "variants", "style", "flexDirection", "breakpoints", "down", "TabsScroller", "position", "flex", "whiteSpace", "overflowX", "width", "scrollbarWidth", "overflowY", "List", "flexContainer", "flexContainerVertical", "justifyContent", "TabsIndicator", "height", "bottom", "transition", "transitions", "create", "indicatorColor", "backgroundColor", "vars", "palette", "primary", "main", "secondary", "right", "TabsScrollbarSize", "defaultIndicatorStyle", "warnedOnceTabPresent", "Tabs", "forwardRef", "inProps", "ref", "isRtl", "aria<PERSON><PERSON><PERSON>", "ariaLabelledBy", "action", "children", "childrenProp", "className", "component", "allowScrollButtonsMobile", "onChange", "orientation", "ScrollButtonComponent", "selectionFollowsFocus", "slotProps", "TabIndicatorProps", "TabScrollButtonProps", "textColor", "value", "variant", "visibleScrollbar", "other", "scrollable", "scrollStart", "start", "end", "clientSize", "size", "startScrollButtonIconProps", "elementType", "StartScrollButtonIcon", "externalSlotProps", "startScrollButtonIcon", "endScrollButtonIconProps", "EndScrollButtonIcon", "endScrollButtonIcon", "process", "env", "NODE_ENV", "console", "error", "mounted", "setMounted", "useState", "indicatorStyle", "setIndicatorStyle", "displayStartScroll", "setDisplayStartScroll", "displayEndScroll", "setDisplayEndScroll", "updateScrollObserver", "setUpdateScrollObserver", "scrollerStyle", "setScrollerStyle", "valueToIndex", "Map", "tabsRef", "useRef", "tabListRef", "externalForwardedProps", "scrollButton", "getTabsMeta", "tabsNode", "current", "tabsMeta", "rect", "getBoundingClientRect", "clientWidth", "scrollLeft", "scrollTop", "scrollWidth", "top", "left", "tabMeta", "length", "tab", "get", "keys", "Array", "from", "join", "updateIndicatorState", "startValue", "startIndicator", "newIndicatorStyle", "dStart", "Math", "abs", "dSize", "scroll", "scrollValue", "animation", "duration", "standard", "moveTabsScroll", "delta", "getScrollSize", "containerSize", "totalSize", "i", "handleStartScrollClick", "handleEndScrollClick", "ScrollbarSlot", "scrollbarOnChange", "scrollbarSlotProps", "shouldForwardComponentProp", "handleScrollbarSizeChange", "useCallback", "ScrollButtonsSlot", "scrollButtonSlotProps", "additionalProps", "getConditionalElements", "conditionalElements", "scrollbarSizeListener", "scrollButtonsActive", "showScrollButtons", "scrollButtonStart", "direction", "onClick", "scrollButtonEnd", "scrollSelectedIntoView", "nextScrollStart", "updateScrollButtonState", "useEffect", "handleResize", "resizeObserver", "handleMutation", "records", "for<PERSON>ach", "record", "removedNodes", "unobserve", "addedNodes", "observe", "win", "addEventListener", "mutationObserver", "ResizeObserver", "child", "MutationObserver", "childList", "clear", "removeEventListener", "disconnect", "tabList<PERSON><PERSON><PERSON>n", "IntersectionObserver", "firstTab", "lastTab", "observerOptions", "threshold", "handleScrollButtonStart", "entries", "isIntersecting", "firstObserver", "handleScrollButtonEnd", "lastObserver", "undefined", "useImperativeHandle", "updateIndicator", "updateScrollButtons", "IndicatorSlot", "indicatorSlotProps", "childIndex", "Children", "map", "isValidElement", "childValue", "set", "selected", "cloneElement", "fullWidth", "tabIndex", "handleKeyDown", "event", "altKey", "shift<PERSON>ey", "ctrl<PERSON>ey", "metaKey", "activeElement", "role", "previousItemKey", "nextItemKey", "key", "preventDefault", "RootSlot", "rootSlotProps", "ScrollerSlot", "scrollerSlotProps", "ListSlot", "listSlotProps", "getSlotProps", "handlers", "onKeyDown", "propTypes", "bool", "string", "node", "object", "oneOfType", "oneOf", "func", "shape", "scrollbar", "sx", "arrayOf", "any"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/@mui/material/esm/Tabs/Tabs.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { isFragment } from 'react-is';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport refType from '@mui/utils/refType';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport { styled, useTheme } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport debounce from \"../utils/debounce.js\";\nimport animate from \"../internal/animate.js\";\nimport ScrollbarSize from \"./ScrollbarSize.js\";\nimport TabScrollButton from \"../TabScrollButton/index.js\";\nimport useEventCallback from \"../utils/useEventCallback.js\";\nimport tabsClasses, { getTabsUtilityClass } from \"./tabsClasses.js\";\nimport ownerDocument from \"../utils/ownerDocument.js\";\nimport ownerWindow from \"../utils/ownerWindow.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst nextItem = (list, item) => {\n  if (list === item) {\n    return list.firstChild;\n  }\n  if (item && item.nextElementSibling) {\n    return item.nextElementSibling;\n  }\n  return list.firstChild;\n};\nconst previousItem = (list, item) => {\n  if (list === item) {\n    return list.lastChild;\n  }\n  if (item && item.previousElementSibling) {\n    return item.previousElementSibling;\n  }\n  return list.lastChild;\n};\nconst moveFocus = (list, currentFocus, traversalFunction) => {\n  let wrappedOnce = false;\n  let nextFocus = traversalFunction(list, currentFocus);\n  while (nextFocus) {\n    // Prevent infinite loop.\n    if (nextFocus === list.firstChild) {\n      if (wrappedOnce) {\n        return;\n      }\n      wrappedOnce = true;\n    }\n\n    // Same logic as useAutocomplete.js\n    const nextFocusDisabled = nextFocus.disabled || nextFocus.getAttribute('aria-disabled') === 'true';\n    if (!nextFocus.hasAttribute('tabindex') || nextFocusDisabled) {\n      // Move to the next element.\n      nextFocus = traversalFunction(list, nextFocus);\n    } else {\n      nextFocus.focus();\n      return;\n    }\n  }\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    vertical,\n    fixed,\n    hideScrollbar,\n    scrollableX,\n    scrollableY,\n    centered,\n    scrollButtonsHideMobile,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', vertical && 'vertical'],\n    scroller: ['scroller', fixed && 'fixed', hideScrollbar && 'hideScrollbar', scrollableX && 'scrollableX', scrollableY && 'scrollableY'],\n    list: ['list', 'flexContainer', vertical && 'flexContainerVertical', vertical && 'vertical', centered && 'centered'],\n    indicator: ['indicator'],\n    scrollButtons: ['scrollButtons', scrollButtonsHideMobile && 'scrollButtonsHideMobile'],\n    scrollableX: [scrollableX && 'scrollableX'],\n    hideScrollbar: [hideScrollbar && 'hideScrollbar']\n  };\n  return composeClasses(slots, getTabsUtilityClass, classes);\n};\nconst TabsRoot = styled('div', {\n  name: 'MuiTabs',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [`& .${tabsClasses.scrollButtons}`]: styles.scrollButtons\n    }, {\n      [`& .${tabsClasses.scrollButtons}`]: ownerState.scrollButtonsHideMobile && styles.scrollButtonsHideMobile\n    }, styles.root, ownerState.vertical && styles.vertical];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  overflow: 'hidden',\n  minHeight: 48,\n  // Add iOS momentum scrolling for iOS < 13.0\n  WebkitOverflowScrolling: 'touch',\n  display: 'flex',\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.vertical,\n    style: {\n      flexDirection: 'column'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.scrollButtonsHideMobile,\n    style: {\n      [`& .${tabsClasses.scrollButtons}`]: {\n        [theme.breakpoints.down('sm')]: {\n          display: 'none'\n        }\n      }\n    }\n  }]\n})));\nconst TabsScroller = styled('div', {\n  name: 'MuiTabs',\n  slot: 'Scroller',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.scroller, ownerState.fixed && styles.fixed, ownerState.hideScrollbar && styles.hideScrollbar, ownerState.scrollableX && styles.scrollableX, ownerState.scrollableY && styles.scrollableY];\n  }\n})({\n  position: 'relative',\n  display: 'inline-block',\n  flex: '1 1 auto',\n  whiteSpace: 'nowrap',\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.fixed,\n    style: {\n      overflowX: 'hidden',\n      width: '100%'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.hideScrollbar,\n    style: {\n      // Hide dimensionless scrollbar on macOS\n      scrollbarWidth: 'none',\n      // Firefox\n      '&::-webkit-scrollbar': {\n        display: 'none' // Safari + Chrome\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.scrollableX,\n    style: {\n      overflowX: 'auto',\n      overflowY: 'hidden'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.scrollableY,\n    style: {\n      overflowY: 'auto',\n      overflowX: 'hidden'\n    }\n  }]\n});\nconst List = styled('div', {\n  name: 'MuiTabs',\n  slot: 'List',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.list, styles.flexContainer, ownerState.vertical && styles.flexContainerVertical, ownerState.centered && styles.centered];\n  }\n})({\n  display: 'flex',\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.vertical,\n    style: {\n      flexDirection: 'column'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.centered,\n    style: {\n      justifyContent: 'center'\n    }\n  }]\n});\nconst TabsIndicator = styled('span', {\n  name: 'MuiTabs',\n  slot: 'Indicator',\n  overridesResolver: (props, styles) => styles.indicator\n})(memoTheme(({\n  theme\n}) => ({\n  position: 'absolute',\n  height: 2,\n  bottom: 0,\n  width: '100%',\n  transition: theme.transitions.create(),\n  variants: [{\n    props: {\n      indicatorColor: 'primary'\n    },\n    style: {\n      backgroundColor: (theme.vars || theme).palette.primary.main\n    }\n  }, {\n    props: {\n      indicatorColor: 'secondary'\n    },\n    style: {\n      backgroundColor: (theme.vars || theme).palette.secondary.main\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.vertical,\n    style: {\n      height: '100%',\n      width: 2,\n      right: 0\n    }\n  }]\n})));\nconst TabsScrollbarSize = styled(ScrollbarSize)({\n  overflowX: 'auto',\n  overflowY: 'hidden',\n  // Hide dimensionless scrollbar on macOS\n  scrollbarWidth: 'none',\n  // Firefox\n  '&::-webkit-scrollbar': {\n    display: 'none' // Safari + Chrome\n  }\n});\nconst defaultIndicatorStyle = {};\nlet warnedOnceTabPresent = false;\nconst Tabs = /*#__PURE__*/React.forwardRef(function Tabs(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiTabs'\n  });\n  const theme = useTheme();\n  const isRtl = useRtl();\n  const {\n    'aria-label': ariaLabel,\n    'aria-labelledby': ariaLabelledBy,\n    action,\n    centered = false,\n    children: childrenProp,\n    className,\n    component = 'div',\n    allowScrollButtonsMobile = false,\n    indicatorColor = 'primary',\n    onChange,\n    orientation = 'horizontal',\n    ScrollButtonComponent,\n    // TODO: remove in v7 (deprecated in v6)\n    scrollButtons = 'auto',\n    selectionFollowsFocus,\n    slots = {},\n    slotProps = {},\n    TabIndicatorProps = {},\n    // TODO: remove in v7 (deprecated in v6)\n    TabScrollButtonProps = {},\n    // TODO: remove in v7 (deprecated in v6)\n    textColor = 'primary',\n    value,\n    variant = 'standard',\n    visibleScrollbar = false,\n    ...other\n  } = props;\n  const scrollable = variant === 'scrollable';\n  const vertical = orientation === 'vertical';\n  const scrollStart = vertical ? 'scrollTop' : 'scrollLeft';\n  const start = vertical ? 'top' : 'left';\n  const end = vertical ? 'bottom' : 'right';\n  const clientSize = vertical ? 'clientHeight' : 'clientWidth';\n  const size = vertical ? 'height' : 'width';\n  const ownerState = {\n    ...props,\n    component,\n    allowScrollButtonsMobile,\n    indicatorColor,\n    orientation,\n    vertical,\n    scrollButtons,\n    textColor,\n    variant,\n    visibleScrollbar,\n    fixed: !scrollable,\n    hideScrollbar: scrollable && !visibleScrollbar,\n    scrollableX: scrollable && !vertical,\n    scrollableY: scrollable && vertical,\n    centered: centered && !scrollable,\n    scrollButtonsHideMobile: !allowScrollButtonsMobile\n  };\n  const classes = useUtilityClasses(ownerState);\n  const startScrollButtonIconProps = useSlotProps({\n    elementType: slots.StartScrollButtonIcon,\n    externalSlotProps: slotProps.startScrollButtonIcon,\n    ownerState\n  });\n  const endScrollButtonIconProps = useSlotProps({\n    elementType: slots.EndScrollButtonIcon,\n    externalSlotProps: slotProps.endScrollButtonIcon,\n    ownerState\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    if (centered && scrollable) {\n      console.error('MUI: You can not use the `centered={true}` and `variant=\"scrollable\"` properties ' + 'at the same time on a `Tabs` component.');\n    }\n  }\n  const [mounted, setMounted] = React.useState(false);\n  const [indicatorStyle, setIndicatorStyle] = React.useState(defaultIndicatorStyle);\n  const [displayStartScroll, setDisplayStartScroll] = React.useState(false);\n  const [displayEndScroll, setDisplayEndScroll] = React.useState(false);\n  const [updateScrollObserver, setUpdateScrollObserver] = React.useState(false);\n  const [scrollerStyle, setScrollerStyle] = React.useState({\n    overflow: 'hidden',\n    scrollbarWidth: 0\n  });\n  const valueToIndex = new Map();\n  const tabsRef = React.useRef(null);\n  const tabListRef = React.useRef(null);\n  const externalForwardedProps = {\n    slots,\n    slotProps: {\n      indicator: TabIndicatorProps,\n      scrollButton: TabScrollButtonProps,\n      ...slotProps\n    }\n  };\n  const getTabsMeta = () => {\n    const tabsNode = tabsRef.current;\n    let tabsMeta;\n    if (tabsNode) {\n      const rect = tabsNode.getBoundingClientRect();\n      // create a new object with ClientRect class props + scrollLeft\n      tabsMeta = {\n        clientWidth: tabsNode.clientWidth,\n        scrollLeft: tabsNode.scrollLeft,\n        scrollTop: tabsNode.scrollTop,\n        scrollWidth: tabsNode.scrollWidth,\n        top: rect.top,\n        bottom: rect.bottom,\n        left: rect.left,\n        right: rect.right\n      };\n    }\n    let tabMeta;\n    if (tabsNode && value !== false) {\n      const children = tabListRef.current.children;\n      if (children.length > 0) {\n        const tab = children[valueToIndex.get(value)];\n        if (process.env.NODE_ENV !== 'production') {\n          if (!tab) {\n            console.error([`MUI: The \\`value\\` provided to the Tabs component is invalid.`, `None of the Tabs' children match with \"${value}\".`, valueToIndex.keys ? `You can provide one of the following values: ${Array.from(valueToIndex.keys()).join(', ')}.` : null].join('\\n'));\n          }\n        }\n        tabMeta = tab ? tab.getBoundingClientRect() : null;\n        if (process.env.NODE_ENV !== 'production') {\n          if (process.env.NODE_ENV !== 'test' && !warnedOnceTabPresent && tabMeta && tabMeta.width === 0 && tabMeta.height === 0 &&\n          // if the whole Tabs component is hidden, don't warn\n          tabsMeta.clientWidth !== 0) {\n            tabsMeta = null;\n            console.error(['MUI: The `value` provided to the Tabs component is invalid.', `The Tab with this \\`value\\` (\"${value}\") is not part of the document layout.`, \"Make sure the tab item is present in the document or that it's not `display: none`.\"].join('\\n'));\n            warnedOnceTabPresent = true;\n          }\n        }\n      }\n    }\n    return {\n      tabsMeta,\n      tabMeta\n    };\n  };\n  const updateIndicatorState = useEventCallback(() => {\n    const {\n      tabsMeta,\n      tabMeta\n    } = getTabsMeta();\n    let startValue = 0;\n    let startIndicator;\n    if (vertical) {\n      startIndicator = 'top';\n      if (tabMeta && tabsMeta) {\n        startValue = tabMeta.top - tabsMeta.top + tabsMeta.scrollTop;\n      }\n    } else {\n      startIndicator = isRtl ? 'right' : 'left';\n      if (tabMeta && tabsMeta) {\n        startValue = (isRtl ? -1 : 1) * (tabMeta[startIndicator] - tabsMeta[startIndicator] + tabsMeta.scrollLeft);\n      }\n    }\n    const newIndicatorStyle = {\n      [startIndicator]: startValue,\n      // May be wrong until the font is loaded.\n      [size]: tabMeta ? tabMeta[size] : 0\n    };\n    if (typeof indicatorStyle[startIndicator] !== 'number' || typeof indicatorStyle[size] !== 'number') {\n      setIndicatorStyle(newIndicatorStyle);\n    } else {\n      const dStart = Math.abs(indicatorStyle[startIndicator] - newIndicatorStyle[startIndicator]);\n      const dSize = Math.abs(indicatorStyle[size] - newIndicatorStyle[size]);\n      if (dStart >= 1 || dSize >= 1) {\n        setIndicatorStyle(newIndicatorStyle);\n      }\n    }\n  });\n  const scroll = (scrollValue, {\n    animation = true\n  } = {}) => {\n    if (animation) {\n      animate(scrollStart, tabsRef.current, scrollValue, {\n        duration: theme.transitions.duration.standard\n      });\n    } else {\n      tabsRef.current[scrollStart] = scrollValue;\n    }\n  };\n  const moveTabsScroll = delta => {\n    let scrollValue = tabsRef.current[scrollStart];\n    if (vertical) {\n      scrollValue += delta;\n    } else {\n      scrollValue += delta * (isRtl ? -1 : 1);\n    }\n    scroll(scrollValue);\n  };\n  const getScrollSize = () => {\n    const containerSize = tabsRef.current[clientSize];\n    let totalSize = 0;\n    const children = Array.from(tabListRef.current.children);\n    for (let i = 0; i < children.length; i += 1) {\n      const tab = children[i];\n      if (totalSize + tab[clientSize] > containerSize) {\n        // If the first item is longer than the container size, then only scroll\n        // by the container size.\n        if (i === 0) {\n          totalSize = containerSize;\n        }\n        break;\n      }\n      totalSize += tab[clientSize];\n    }\n    return totalSize;\n  };\n  const handleStartScrollClick = () => {\n    moveTabsScroll(-1 * getScrollSize());\n  };\n  const handleEndScrollClick = () => {\n    moveTabsScroll(getScrollSize());\n  };\n  const [ScrollbarSlot, {\n    onChange: scrollbarOnChange,\n    ...scrollbarSlotProps\n  }] = useSlot('scrollbar', {\n    className: clsx(classes.scrollableX, classes.hideScrollbar),\n    elementType: TabsScrollbarSize,\n    shouldForwardComponentProp: true,\n    externalForwardedProps,\n    ownerState\n  });\n\n  // TODO Remove <ScrollbarSize /> as browser support for hiding the scrollbar\n  // with CSS improves.\n  const handleScrollbarSizeChange = React.useCallback(scrollbarWidth => {\n    scrollbarOnChange?.(scrollbarWidth);\n    setScrollerStyle({\n      overflow: null,\n      scrollbarWidth\n    });\n  }, [scrollbarOnChange]);\n  const [ScrollButtonsSlot, scrollButtonSlotProps] = useSlot('scrollButtons', {\n    className: clsx(classes.scrollButtons, TabScrollButtonProps.className),\n    elementType: TabScrollButton,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      orientation,\n      slots: {\n        StartScrollButtonIcon: slots.startScrollButtonIcon || slots.StartScrollButtonIcon,\n        EndScrollButtonIcon: slots.endScrollButtonIcon || slots.EndScrollButtonIcon\n      },\n      slotProps: {\n        startScrollButtonIcon: startScrollButtonIconProps,\n        endScrollButtonIcon: endScrollButtonIconProps\n      }\n    }\n  });\n  const getConditionalElements = () => {\n    const conditionalElements = {};\n    conditionalElements.scrollbarSizeListener = scrollable ? /*#__PURE__*/_jsx(ScrollbarSlot, {\n      ...scrollbarSlotProps,\n      onChange: handleScrollbarSizeChange\n    }) : null;\n    const scrollButtonsActive = displayStartScroll || displayEndScroll;\n    const showScrollButtons = scrollable && (scrollButtons === 'auto' && scrollButtonsActive || scrollButtons === true);\n    conditionalElements.scrollButtonStart = showScrollButtons ? /*#__PURE__*/_jsx(ScrollButtonsSlot, {\n      direction: isRtl ? 'right' : 'left',\n      onClick: handleStartScrollClick,\n      disabled: !displayStartScroll,\n      ...scrollButtonSlotProps\n    }) : null;\n    conditionalElements.scrollButtonEnd = showScrollButtons ? /*#__PURE__*/_jsx(ScrollButtonsSlot, {\n      direction: isRtl ? 'left' : 'right',\n      onClick: handleEndScrollClick,\n      disabled: !displayEndScroll,\n      ...scrollButtonSlotProps\n    }) : null;\n    return conditionalElements;\n  };\n  const scrollSelectedIntoView = useEventCallback(animation => {\n    const {\n      tabsMeta,\n      tabMeta\n    } = getTabsMeta();\n    if (!tabMeta || !tabsMeta) {\n      return;\n    }\n    if (tabMeta[start] < tabsMeta[start]) {\n      // left side of button is out of view\n      const nextScrollStart = tabsMeta[scrollStart] + (tabMeta[start] - tabsMeta[start]);\n      scroll(nextScrollStart, {\n        animation\n      });\n    } else if (tabMeta[end] > tabsMeta[end]) {\n      // right side of button is out of view\n      const nextScrollStart = tabsMeta[scrollStart] + (tabMeta[end] - tabsMeta[end]);\n      scroll(nextScrollStart, {\n        animation\n      });\n    }\n  });\n  const updateScrollButtonState = useEventCallback(() => {\n    if (scrollable && scrollButtons !== false) {\n      setUpdateScrollObserver(!updateScrollObserver);\n    }\n  });\n  React.useEffect(() => {\n    const handleResize = debounce(() => {\n      // If the Tabs component is replaced by Suspense with a fallback, the last\n      // ResizeObserver's handler that runs because of the change in the layout is trying to\n      // access a dom node that is no longer there (as the fallback component is being shown instead).\n      // See https://github.com/mui/material-ui/issues/33276\n      // TODO: Add tests that will ensure the component is not failing when\n      // replaced by Suspense with a fallback, once React is updated to version 18\n      if (tabsRef.current) {\n        updateIndicatorState();\n      }\n    });\n    let resizeObserver;\n\n    /**\n     * @type {MutationCallback}\n     */\n    const handleMutation = records => {\n      records.forEach(record => {\n        record.removedNodes.forEach(item => {\n          resizeObserver?.unobserve(item);\n        });\n        record.addedNodes.forEach(item => {\n          resizeObserver?.observe(item);\n        });\n      });\n      handleResize();\n      updateScrollButtonState();\n    };\n    const win = ownerWindow(tabsRef.current);\n    win.addEventListener('resize', handleResize);\n    let mutationObserver;\n    if (typeof ResizeObserver !== 'undefined') {\n      resizeObserver = new ResizeObserver(handleResize);\n      Array.from(tabListRef.current.children).forEach(child => {\n        resizeObserver.observe(child);\n      });\n    }\n    if (typeof MutationObserver !== 'undefined') {\n      mutationObserver = new MutationObserver(handleMutation);\n      mutationObserver.observe(tabListRef.current, {\n        childList: true\n      });\n    }\n    return () => {\n      handleResize.clear();\n      win.removeEventListener('resize', handleResize);\n      mutationObserver?.disconnect();\n      resizeObserver?.disconnect();\n    };\n  }, [updateIndicatorState, updateScrollButtonState]);\n\n  /**\n   * Toggle visibility of start and end scroll buttons\n   * Using IntersectionObserver on first and last Tabs.\n   */\n  React.useEffect(() => {\n    const tabListChildren = Array.from(tabListRef.current.children);\n    const length = tabListChildren.length;\n    if (typeof IntersectionObserver !== 'undefined' && length > 0 && scrollable && scrollButtons !== false) {\n      const firstTab = tabListChildren[0];\n      const lastTab = tabListChildren[length - 1];\n      const observerOptions = {\n        root: tabsRef.current,\n        threshold: 0.99\n      };\n      const handleScrollButtonStart = entries => {\n        setDisplayStartScroll(!entries[0].isIntersecting);\n      };\n      const firstObserver = new IntersectionObserver(handleScrollButtonStart, observerOptions);\n      firstObserver.observe(firstTab);\n      const handleScrollButtonEnd = entries => {\n        setDisplayEndScroll(!entries[0].isIntersecting);\n      };\n      const lastObserver = new IntersectionObserver(handleScrollButtonEnd, observerOptions);\n      lastObserver.observe(lastTab);\n      return () => {\n        firstObserver.disconnect();\n        lastObserver.disconnect();\n      };\n    }\n    return undefined;\n  }, [scrollable, scrollButtons, updateScrollObserver, childrenProp?.length]);\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n  React.useEffect(() => {\n    updateIndicatorState();\n  });\n  React.useEffect(() => {\n    // Don't animate on the first render.\n    scrollSelectedIntoView(defaultIndicatorStyle !== indicatorStyle);\n  }, [scrollSelectedIntoView, indicatorStyle]);\n  React.useImperativeHandle(action, () => ({\n    updateIndicator: updateIndicatorState,\n    updateScrollButtons: updateScrollButtonState\n  }), [updateIndicatorState, updateScrollButtonState]);\n  const [IndicatorSlot, indicatorSlotProps] = useSlot('indicator', {\n    className: clsx(classes.indicator, TabIndicatorProps.className),\n    elementType: TabsIndicator,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      style: indicatorStyle\n    }\n  });\n  const indicator = /*#__PURE__*/_jsx(IndicatorSlot, {\n    ...indicatorSlotProps\n  });\n  let childIndex = 0;\n  const children = React.Children.map(childrenProp, child => {\n    if (! /*#__PURE__*/React.isValidElement(child)) {\n      return null;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (isFragment(child)) {\n        console.error([\"MUI: The Tabs component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n      }\n    }\n    const childValue = child.props.value === undefined ? childIndex : child.props.value;\n    valueToIndex.set(childValue, childIndex);\n    const selected = childValue === value;\n    childIndex += 1;\n    return /*#__PURE__*/React.cloneElement(child, {\n      fullWidth: variant === 'fullWidth',\n      indicator: selected && !mounted && indicator,\n      selected,\n      selectionFollowsFocus,\n      onChange,\n      textColor,\n      value: childValue,\n      ...(childIndex === 1 && value === false && !child.props.tabIndex ? {\n        tabIndex: 0\n      } : {})\n    });\n  });\n  const handleKeyDown = event => {\n    // Check if a modifier key (Alt, Shift, Ctrl, Meta) is pressed\n    if (event.altKey || event.shiftKey || event.ctrlKey || event.metaKey) {\n      return;\n    }\n    const list = tabListRef.current;\n    const currentFocus = ownerDocument(list).activeElement;\n    // Keyboard navigation assumes that [role=\"tab\"] are siblings\n    // though we might warn in the future about nested, interactive elements\n    // as a a11y violation\n    const role = currentFocus.getAttribute('role');\n    if (role !== 'tab') {\n      return;\n    }\n    let previousItemKey = orientation === 'horizontal' ? 'ArrowLeft' : 'ArrowUp';\n    let nextItemKey = orientation === 'horizontal' ? 'ArrowRight' : 'ArrowDown';\n    if (orientation === 'horizontal' && isRtl) {\n      // swap previousItemKey with nextItemKey\n      previousItemKey = 'ArrowRight';\n      nextItemKey = 'ArrowLeft';\n    }\n    switch (event.key) {\n      case previousItemKey:\n        event.preventDefault();\n        moveFocus(list, currentFocus, previousItem);\n        break;\n      case nextItemKey:\n        event.preventDefault();\n        moveFocus(list, currentFocus, nextItem);\n        break;\n      case 'Home':\n        event.preventDefault();\n        moveFocus(list, null, nextItem);\n        break;\n      case 'End':\n        event.preventDefault();\n        moveFocus(list, null, previousItem);\n        break;\n      default:\n        break;\n    }\n  };\n  const conditionalElements = getConditionalElements();\n  const [RootSlot, rootSlotProps] = useSlot('root', {\n    ref,\n    className: clsx(classes.root, className),\n    elementType: TabsRoot,\n    externalForwardedProps: {\n      ...externalForwardedProps,\n      ...other,\n      component\n    },\n    ownerState\n  });\n  const [ScrollerSlot, scrollerSlotProps] = useSlot('scroller', {\n    ref: tabsRef,\n    className: classes.scroller,\n    elementType: TabsScroller,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      style: {\n        overflow: scrollerStyle.overflow,\n        [vertical ? `margin${isRtl ? 'Left' : 'Right'}` : 'marginBottom']: visibleScrollbar ? undefined : -scrollerStyle.scrollbarWidth\n      }\n    }\n  });\n  const [ListSlot, listSlotProps] = useSlot('list', {\n    ref: tabListRef,\n    className: clsx(classes.list, classes.flexContainer),\n    elementType: List,\n    externalForwardedProps,\n    ownerState,\n    getSlotProps: handlers => ({\n      ...handlers,\n      onKeyDown: event => {\n        handleKeyDown(event);\n        handlers.onKeyDown?.(event);\n      }\n    })\n  });\n  return /*#__PURE__*/_jsxs(RootSlot, {\n    ...rootSlotProps,\n    children: [conditionalElements.scrollButtonStart, conditionalElements.scrollbarSizeListener, /*#__PURE__*/_jsxs(ScrollerSlot, {\n      ...scrollerSlotProps,\n      children: [/*#__PURE__*/_jsx(ListSlot, {\n        \"aria-label\": ariaLabel,\n        \"aria-labelledby\": ariaLabelledBy,\n        \"aria-orientation\": orientation === 'vertical' ? 'vertical' : null,\n        role: \"tablist\",\n        ...listSlotProps,\n        children: children\n      }), mounted && indicator]\n    }), conditionalElements.scrollButtonEnd]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Tabs.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Callback fired when the component mounts.\n   * This is useful when you want to trigger an action programmatically.\n   * It supports two actions: `updateIndicator()` and `updateScrollButtons()`\n   *\n   * @param {object} actions This object contains all possible actions\n   * that can be triggered programmatically.\n   */\n  action: refType,\n  /**\n   * If `true`, the scroll buttons aren't forced hidden on mobile.\n   * By default the scroll buttons are hidden on mobile and takes precedence over `scrollButtons`.\n   * @default false\n   */\n  allowScrollButtonsMobile: PropTypes.bool,\n  /**\n   * The label for the Tabs as a string.\n   */\n  'aria-label': PropTypes.string,\n  /**\n   * An id or list of ids separated by a space that label the Tabs.\n   */\n  'aria-labelledby': PropTypes.string,\n  /**\n   * If `true`, the tabs are centered.\n   * This prop is intended for large views.\n   * @default false\n   */\n  centered: PropTypes.bool,\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Determines the color of the indicator.\n   * @default 'primary'\n   */\n  indicatorColor: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['primary', 'secondary']), PropTypes.string]),\n  /**\n   * Callback fired when the value changes.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback. **Warning**: This is a generic event not a change event.\n   * @param {any} value We default to the index of the child (number)\n   */\n  onChange: PropTypes.func,\n  /**\n   * The component orientation (layout flow direction).\n   * @default 'horizontal'\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * The component used to render the scroll buttons.\n   * @deprecated use the `slots.scrollButtons` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default TabScrollButton\n   */\n  ScrollButtonComponent: PropTypes.elementType,\n  /**\n   * Determine behavior of scroll buttons when tabs are set to scroll:\n   *\n   * - `auto` will only present them when not all the items are visible.\n   * - `true` will always present them.\n   * - `false` will never present them.\n   *\n   * By default the scroll buttons are hidden on mobile.\n   * This behavior can be disabled with `allowScrollButtonsMobile`.\n   * @default 'auto'\n   */\n  scrollButtons: PropTypes /* @typescript-to-proptypes-ignore */.oneOf(['auto', false, true]),\n  /**\n   * If `true` the selected tab changes on focus. Otherwise it only\n   * changes on activation.\n   */\n  selectionFollowsFocus: PropTypes.bool,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    endScrollButtonIcon: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    indicator: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    list: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    scrollbar: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    scrollButtons: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    scroller: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    startScrollButtonIcon: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    endScrollButtonIcon: PropTypes.elementType,\n    EndScrollButtonIcon: PropTypes.elementType,\n    indicator: PropTypes.elementType,\n    list: PropTypes.elementType,\n    root: PropTypes.elementType,\n    scrollbar: PropTypes.elementType,\n    scrollButtons: PropTypes.elementType,\n    scroller: PropTypes.elementType,\n    startScrollButtonIcon: PropTypes.elementType,\n    StartScrollButtonIcon: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Props applied to the tab indicator element.\n   * @deprecated use the `slotProps.indicator` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default  {}\n   */\n  TabIndicatorProps: PropTypes.object,\n  /**\n   * Props applied to the [`TabScrollButton`](https://mui.com/material-ui/api/tab-scroll-button/) element.\n   * @deprecated use the `slotProps.scrollButtons` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default {}\n   */\n  TabScrollButtonProps: PropTypes.object,\n  /**\n   * Determines the color of the `Tab`.\n   * @default 'primary'\n   */\n  textColor: PropTypes.oneOf(['inherit', 'primary', 'secondary']),\n  /**\n   * The value of the currently selected `Tab`.\n   * If you don't want any selected `Tab`, you can set this prop to `false`.\n   */\n  value: PropTypes.any,\n  /**\n   * Determines additional display behavior of the tabs:\n   *\n   *  - `scrollable` will invoke scrolling properties and allow for horizontally\n   *  scrolling (or swiping) of the tab bar.\n   *  - `fullWidth` will make the tabs grow to use all the available space,\n   *  which should be used for small views, like on mobile.\n   *  - `standard` will render the default state.\n   * @default 'standard'\n   */\n  variant: PropTypes.oneOf(['fullWidth', 'scrollable', 'standard']),\n  /**\n   * If `true`, the scrollbar is visible. It can be useful when displaying\n   * a long vertical list of tabs.\n   * @default false\n   */\n  visibleScrollbar: PropTypes.bool\n} : void 0;\nexport default Tabs;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,UAAU;AACrC,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,YAAY,MAAM,yBAAyB;AAClD,SAASC,MAAM,EAAEC,QAAQ,QAAQ,yBAAyB;AAC1D,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,SAASC,eAAe,QAAQ,kCAAkC;AAClE,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,OAAO,MAAM,wBAAwB;AAC5C,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,OAAOC,WAAW,IAAIC,mBAAmB,QAAQ,kBAAkB;AACnE,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,OAAO,MAAM,qBAAqB;AACzC,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,MAAMC,QAAQ,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK;EAC/B,IAAID,IAAI,KAAKC,IAAI,EAAE;IACjB,OAAOD,IAAI,CAACE,UAAU;EACxB;EACA,IAAID,IAAI,IAAIA,IAAI,CAACE,kBAAkB,EAAE;IACnC,OAAOF,IAAI,CAACE,kBAAkB;EAChC;EACA,OAAOH,IAAI,CAACE,UAAU;AACxB,CAAC;AACD,MAAME,YAAY,GAAGA,CAACJ,IAAI,EAAEC,IAAI,KAAK;EACnC,IAAID,IAAI,KAAKC,IAAI,EAAE;IACjB,OAAOD,IAAI,CAACK,SAAS;EACvB;EACA,IAAIJ,IAAI,IAAIA,IAAI,CAACK,sBAAsB,EAAE;IACvC,OAAOL,IAAI,CAACK,sBAAsB;EACpC;EACA,OAAON,IAAI,CAACK,SAAS;AACvB,CAAC;AACD,MAAME,SAAS,GAAGA,CAACP,IAAI,EAAEQ,YAAY,EAAEC,iBAAiB,KAAK;EAC3D,IAAIC,WAAW,GAAG,KAAK;EACvB,IAAIC,SAAS,GAAGF,iBAAiB,CAACT,IAAI,EAAEQ,YAAY,CAAC;EACrD,OAAOG,SAAS,EAAE;IAChB;IACA,IAAIA,SAAS,KAAKX,IAAI,CAACE,UAAU,EAAE;MACjC,IAAIQ,WAAW,EAAE;QACf;MACF;MACAA,WAAW,GAAG,IAAI;IACpB;;IAEA;IACA,MAAME,iBAAiB,GAAGD,SAAS,CAACE,QAAQ,IAAIF,SAAS,CAACG,YAAY,CAAC,eAAe,CAAC,KAAK,MAAM;IAClG,IAAI,CAACH,SAAS,CAACI,YAAY,CAAC,UAAU,CAAC,IAAIH,iBAAiB,EAAE;MAC5D;MACAD,SAAS,GAAGF,iBAAiB,CAACT,IAAI,EAAEW,SAAS,CAAC;IAChD,CAAC,MAAM;MACLA,SAAS,CAACK,KAAK,CAAC,CAAC;MACjB;IACF;EACF;AACF,CAAC;AACD,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,QAAQ;IACRC,KAAK;IACLC,aAAa;IACbC,WAAW;IACXC,WAAW;IACXC,QAAQ;IACRC,uBAAuB;IACvBC;EACF,CAAC,GAAGR,UAAU;EACd,MAAMS,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAET,QAAQ,IAAI,UAAU,CAAC;IACtCU,QAAQ,EAAE,CAAC,UAAU,EAAET,KAAK,IAAI,OAAO,EAAEC,aAAa,IAAI,eAAe,EAAEC,WAAW,IAAI,aAAa,EAAEC,WAAW,IAAI,aAAa,CAAC;IACtIvB,IAAI,EAAE,CAAC,MAAM,EAAE,eAAe,EAAEmB,QAAQ,IAAI,uBAAuB,EAAEA,QAAQ,IAAI,UAAU,EAAEK,QAAQ,IAAI,UAAU,CAAC;IACpHM,SAAS,EAAE,CAAC,WAAW,CAAC;IACxBC,aAAa,EAAE,CAAC,eAAe,EAAEN,uBAAuB,IAAI,yBAAyB,CAAC;IACtFH,WAAW,EAAE,CAACA,WAAW,IAAI,aAAa,CAAC;IAC3CD,aAAa,EAAE,CAACA,aAAa,IAAI,eAAe;EAClD,CAAC;EACD,OAAO3C,cAAc,CAACiD,KAAK,EAAEpC,mBAAmB,EAAEmC,OAAO,CAAC;AAC5D,CAAC;AACD,MAAMM,QAAQ,GAAGnD,MAAM,CAAC,KAAK,EAAE;EAC7BoD,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJnB;IACF,CAAC,GAAGkB,KAAK;IACT,OAAO,CAAC;MACN,CAAC,MAAM9C,WAAW,CAACyC,aAAa,EAAE,GAAGM,MAAM,CAACN;IAC9C,CAAC,EAAE;MACD,CAAC,MAAMzC,WAAW,CAACyC,aAAa,EAAE,GAAGb,UAAU,CAACO,uBAAuB,IAAIY,MAAM,CAACZ;IACpF,CAAC,EAAEY,MAAM,CAACT,IAAI,EAAEV,UAAU,CAACC,QAAQ,IAAIkB,MAAM,CAAClB,QAAQ,CAAC;EACzD;AACF,CAAC,CAAC,CAACpC,SAAS,CAAC,CAAC;EACZuD;AACF,CAAC,MAAM;EACLC,QAAQ,EAAE,QAAQ;EAClBC,SAAS,EAAE,EAAE;EACb;EACAC,uBAAuB,EAAE,OAAO;EAChCC,OAAO,EAAE,MAAM;EACfC,QAAQ,EAAE,CAAC;IACTP,KAAK,EAAEA,CAAC;MACNlB;IACF,CAAC,KAAKA,UAAU,CAACC,QAAQ;IACzByB,KAAK,EAAE;MACLC,aAAa,EAAE;IACjB;EACF,CAAC,EAAE;IACDT,KAAK,EAAEA,CAAC;MACNlB;IACF,CAAC,KAAKA,UAAU,CAACO,uBAAuB;IACxCmB,KAAK,EAAE;MACL,CAAC,MAAMtD,WAAW,CAACyC,aAAa,EAAE,GAAG;QACnC,CAACO,KAAK,CAACQ,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,GAAG;UAC9BL,OAAO,EAAE;QACX;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC,CAAC,CAAC;AACJ,MAAMM,YAAY,GAAGnE,MAAM,CAAC,KAAK,EAAE;EACjCoD,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,UAAU;EAChBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJnB;IACF,CAAC,GAAGkB,KAAK;IACT,OAAO,CAACC,MAAM,CAACR,QAAQ,EAAEX,UAAU,CAACE,KAAK,IAAIiB,MAAM,CAACjB,KAAK,EAAEF,UAAU,CAACG,aAAa,IAAIgB,MAAM,CAAChB,aAAa,EAAEH,UAAU,CAACI,WAAW,IAAIe,MAAM,CAACf,WAAW,EAAEJ,UAAU,CAACK,WAAW,IAAIc,MAAM,CAACd,WAAW,CAAC;EAC1M;AACF,CAAC,CAAC,CAAC;EACD0B,QAAQ,EAAE,UAAU;EACpBP,OAAO,EAAE,cAAc;EACvBQ,IAAI,EAAE,UAAU;EAChBC,UAAU,EAAE,QAAQ;EACpBR,QAAQ,EAAE,CAAC;IACTP,KAAK,EAAEA,CAAC;MACNlB;IACF,CAAC,KAAKA,UAAU,CAACE,KAAK;IACtBwB,KAAK,EAAE;MACLQ,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE;IACT;EACF,CAAC,EAAE;IACDjB,KAAK,EAAEA,CAAC;MACNlB;IACF,CAAC,KAAKA,UAAU,CAACG,aAAa;IAC9BuB,KAAK,EAAE;MACL;MACAU,cAAc,EAAE,MAAM;MACtB;MACA,sBAAsB,EAAE;QACtBZ,OAAO,EAAE,MAAM,CAAC;MAClB;IACF;EACF,CAAC,EAAE;IACDN,KAAK,EAAEA,CAAC;MACNlB;IACF,CAAC,KAAKA,UAAU,CAACI,WAAW;IAC5BsB,KAAK,EAAE;MACLQ,SAAS,EAAE,MAAM;MACjBG,SAAS,EAAE;IACb;EACF,CAAC,EAAE;IACDnB,KAAK,EAAEA,CAAC;MACNlB;IACF,CAAC,KAAKA,UAAU,CAACK,WAAW;IAC5BqB,KAAK,EAAE;MACLW,SAAS,EAAE,MAAM;MACjBH,SAAS,EAAE;IACb;EACF,CAAC;AACH,CAAC,CAAC;AACF,MAAMI,IAAI,GAAG3E,MAAM,CAAC,KAAK,EAAE;EACzBoD,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJnB;IACF,CAAC,GAAGkB,KAAK;IACT,OAAO,CAACC,MAAM,CAACrC,IAAI,EAAEqC,MAAM,CAACoB,aAAa,EAAEvC,UAAU,CAACC,QAAQ,IAAIkB,MAAM,CAACqB,qBAAqB,EAAExC,UAAU,CAACM,QAAQ,IAAIa,MAAM,CAACb,QAAQ,CAAC;EACzI;AACF,CAAC,CAAC,CAAC;EACDkB,OAAO,EAAE,MAAM;EACfC,QAAQ,EAAE,CAAC;IACTP,KAAK,EAAEA,CAAC;MACNlB;IACF,CAAC,KAAKA,UAAU,CAACC,QAAQ;IACzByB,KAAK,EAAE;MACLC,aAAa,EAAE;IACjB;EACF,CAAC,EAAE;IACDT,KAAK,EAAEA,CAAC;MACNlB;IACF,CAAC,KAAKA,UAAU,CAACM,QAAQ;IACzBoB,KAAK,EAAE;MACLe,cAAc,EAAE;IAClB;EACF,CAAC;AACH,CAAC,CAAC;AACF,MAAMC,aAAa,GAAG/E,MAAM,CAAC,MAAM,EAAE;EACnCoD,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,WAAW;EACjBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACP;AAC/C,CAAC,CAAC,CAAC/C,SAAS,CAAC,CAAC;EACZuD;AACF,CAAC,MAAM;EACLW,QAAQ,EAAE,UAAU;EACpBY,MAAM,EAAE,CAAC;EACTC,MAAM,EAAE,CAAC;EACTT,KAAK,EAAE,MAAM;EACbU,UAAU,EAAEzB,KAAK,CAAC0B,WAAW,CAACC,MAAM,CAAC,CAAC;EACtCtB,QAAQ,EAAE,CAAC;IACTP,KAAK,EAAE;MACL8B,cAAc,EAAE;IAClB,CAAC;IACDtB,KAAK,EAAE;MACLuB,eAAe,EAAE,CAAC7B,KAAK,CAAC8B,IAAI,IAAI9B,KAAK,EAAE+B,OAAO,CAACC,OAAO,CAACC;IACzD;EACF,CAAC,EAAE;IACDnC,KAAK,EAAE;MACL8B,cAAc,EAAE;IAClB,CAAC;IACDtB,KAAK,EAAE;MACLuB,eAAe,EAAE,CAAC7B,KAAK,CAAC8B,IAAI,IAAI9B,KAAK,EAAE+B,OAAO,CAACG,SAAS,CAACD;IAC3D;EACF,CAAC,EAAE;IACDnC,KAAK,EAAEA,CAAC;MACNlB;IACF,CAAC,KAAKA,UAAU,CAACC,QAAQ;IACzByB,KAAK,EAAE;MACLiB,MAAM,EAAE,MAAM;MACdR,KAAK,EAAE,CAAC;MACRoB,KAAK,EAAE;IACT;EACF,CAAC;AACH,CAAC,CAAC,CAAC,CAAC;AACJ,MAAMC,iBAAiB,GAAG7F,MAAM,CAACM,aAAa,CAAC,CAAC;EAC9CiE,SAAS,EAAE,MAAM;EACjBG,SAAS,EAAE,QAAQ;EACnB;EACAD,cAAc,EAAE,MAAM;EACtB;EACA,sBAAsB,EAAE;IACtBZ,OAAO,EAAE,MAAM,CAAC;EAClB;AACF,CAAC,CAAC;AACF,MAAMiC,qBAAqB,GAAG,CAAC,CAAC;AAChC,IAAIC,oBAAoB,GAAG,KAAK;AAChC,MAAMC,IAAI,GAAG,aAAaxG,KAAK,CAACyG,UAAU,CAAC,SAASD,IAAIA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACrE,MAAM5C,KAAK,GAAGpD,eAAe,CAAC;IAC5BoD,KAAK,EAAE2C,OAAO;IACd9C,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAMK,KAAK,GAAGxD,QAAQ,CAAC,CAAC;EACxB,MAAMmG,KAAK,GAAGtG,MAAM,CAAC,CAAC;EACtB,MAAM;IACJ,YAAY,EAAEuG,SAAS;IACvB,iBAAiB,EAAEC,cAAc;IACjCC,MAAM;IACN5D,QAAQ,GAAG,KAAK;IAChB6D,QAAQ,EAAEC,YAAY;IACtBC,SAAS;IACTC,SAAS,GAAG,KAAK;IACjBC,wBAAwB,GAAG,KAAK;IAChCvB,cAAc,GAAG,SAAS;IAC1BwB,QAAQ;IACRC,WAAW,GAAG,YAAY;IAC1BC,qBAAqB;IACrB;IACA7D,aAAa,GAAG,MAAM;IACtB8D,qBAAqB;IACrBlE,KAAK,GAAG,CAAC,CAAC;IACVmE,SAAS,GAAG,CAAC,CAAC;IACdC,iBAAiB,GAAG,CAAC,CAAC;IACtB;IACAC,oBAAoB,GAAG,CAAC,CAAC;IACzB;IACAC,SAAS,GAAG,SAAS;IACrBC,KAAK;IACLC,OAAO,GAAG,UAAU;IACpBC,gBAAgB,GAAG,KAAK;IACxB,GAAGC;EACL,CAAC,GAAGjE,KAAK;EACT,MAAMkE,UAAU,GAAGH,OAAO,KAAK,YAAY;EAC3C,MAAMhF,QAAQ,GAAGwE,WAAW,KAAK,UAAU;EAC3C,MAAMY,WAAW,GAAGpF,QAAQ,GAAG,WAAW,GAAG,YAAY;EACzD,MAAMqF,KAAK,GAAGrF,QAAQ,GAAG,KAAK,GAAG,MAAM;EACvC,MAAMsF,GAAG,GAAGtF,QAAQ,GAAG,QAAQ,GAAG,OAAO;EACzC,MAAMuF,UAAU,GAAGvF,QAAQ,GAAG,cAAc,GAAG,aAAa;EAC5D,MAAMwF,IAAI,GAAGxF,QAAQ,GAAG,QAAQ,GAAG,OAAO;EAC1C,MAAMD,UAAU,GAAG;IACjB,GAAGkB,KAAK;IACRoD,SAAS;IACTC,wBAAwB;IACxBvB,cAAc;IACdyB,WAAW;IACXxE,QAAQ;IACRY,aAAa;IACbkE,SAAS;IACTE,OAAO;IACPC,gBAAgB;IAChBhF,KAAK,EAAE,CAACkF,UAAU;IAClBjF,aAAa,EAAEiF,UAAU,IAAI,CAACF,gBAAgB;IAC9C9E,WAAW,EAAEgF,UAAU,IAAI,CAACnF,QAAQ;IACpCI,WAAW,EAAE+E,UAAU,IAAInF,QAAQ;IACnCK,QAAQ,EAAEA,QAAQ,IAAI,CAAC8E,UAAU;IACjC7E,uBAAuB,EAAE,CAACgE;EAC5B,CAAC;EACD,MAAM/D,OAAO,GAAGT,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAM0F,0BAA0B,GAAGhI,YAAY,CAAC;IAC9CiI,WAAW,EAAElF,KAAK,CAACmF,qBAAqB;IACxCC,iBAAiB,EAAEjB,SAAS,CAACkB,qBAAqB;IAClD9F;EACF,CAAC,CAAC;EACF,MAAM+F,wBAAwB,GAAGrI,YAAY,CAAC;IAC5CiI,WAAW,EAAElF,KAAK,CAACuF,mBAAmB;IACtCH,iBAAiB,EAAEjB,SAAS,CAACqB,mBAAmB;IAChDjG;EACF,CAAC,CAAC;EACF,IAAIkG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAI9F,QAAQ,IAAI8E,UAAU,EAAE;MAC1BiB,OAAO,CAACC,KAAK,CAAC,mFAAmF,GAAG,yCAAyC,CAAC;IAChJ;EACF;EACA,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGrJ,KAAK,CAACsJ,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGxJ,KAAK,CAACsJ,QAAQ,CAAChD,qBAAqB,CAAC;EACjF,MAAM,CAACmD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1J,KAAK,CAACsJ,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACK,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5J,KAAK,CAACsJ,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACO,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9J,KAAK,CAACsJ,QAAQ,CAAC,KAAK,CAAC;EAC7E,MAAM,CAACS,aAAa,EAAEC,gBAAgB,CAAC,GAAGhK,KAAK,CAACsJ,QAAQ,CAAC;IACvDpF,QAAQ,EAAE,QAAQ;IAClBe,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAMgF,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC9B,MAAMC,OAAO,GAAGnK,KAAK,CAACoK,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMC,UAAU,GAAGrK,KAAK,CAACoK,MAAM,CAAC,IAAI,CAAC;EACrC,MAAME,sBAAsB,GAAG;IAC7BhH,KAAK;IACLmE,SAAS,EAAE;MACThE,SAAS,EAAEiE,iBAAiB;MAC5B6C,YAAY,EAAE5C,oBAAoB;MAClC,GAAGF;IACL;EACF,CAAC;EACD,MAAM+C,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,QAAQ,GAAGN,OAAO,CAACO,OAAO;IAChC,IAAIC,QAAQ;IACZ,IAAIF,QAAQ,EAAE;MACZ,MAAMG,IAAI,GAAGH,QAAQ,CAACI,qBAAqB,CAAC,CAAC;MAC7C;MACAF,QAAQ,GAAG;QACTG,WAAW,EAAEL,QAAQ,CAACK,WAAW;QACjCC,UAAU,EAAEN,QAAQ,CAACM,UAAU;QAC/BC,SAAS,EAAEP,QAAQ,CAACO,SAAS;QAC7BC,WAAW,EAAER,QAAQ,CAACQ,WAAW;QACjCC,GAAG,EAAEN,IAAI,CAACM,GAAG;QACbzF,MAAM,EAAEmF,IAAI,CAACnF,MAAM;QACnB0F,IAAI,EAAEP,IAAI,CAACO,IAAI;QACf/E,KAAK,EAAEwE,IAAI,CAACxE;MACd,CAAC;IACH;IACA,IAAIgF,OAAO;IACX,IAAIX,QAAQ,IAAI5C,KAAK,KAAK,KAAK,EAAE;MAC/B,MAAMb,QAAQ,GAAGqD,UAAU,CAACK,OAAO,CAAC1D,QAAQ;MAC5C,IAAIA,QAAQ,CAACqE,MAAM,GAAG,CAAC,EAAE;QACvB,MAAMC,GAAG,GAAGtE,QAAQ,CAACiD,YAAY,CAACsB,GAAG,CAAC1D,KAAK,CAAC,CAAC;QAC7C,IAAIkB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzC,IAAI,CAACqC,GAAG,EAAE;YACRpC,OAAO,CAACC,KAAK,CAAC,CAAC,+DAA+D,EAAE,0CAA0CtB,KAAK,IAAI,EAAEoC,YAAY,CAACuB,IAAI,GAAG,gDAAgDC,KAAK,CAACC,IAAI,CAACzB,YAAY,CAACuB,IAAI,CAAC,CAAC,CAAC,CAACG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC,CAAC;UAC5Q;QACF;QACAP,OAAO,GAAGE,GAAG,GAAGA,GAAG,CAACT,qBAAqB,CAAC,CAAC,GAAG,IAAI;QAClD,IAAI9B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzC,IAAIF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,IAAI,CAAC1C,oBAAoB,IAAI6E,OAAO,IAAIA,OAAO,CAACpG,KAAK,KAAK,CAAC,IAAIoG,OAAO,CAAC5F,MAAM,KAAK,CAAC;UACtH;UACAmF,QAAQ,CAACG,WAAW,KAAK,CAAC,EAAE;YAC1BH,QAAQ,GAAG,IAAI;YACfzB,OAAO,CAACC,KAAK,CAAC,CAAC,6DAA6D,EAAE,iCAAiCtB,KAAK,wCAAwC,EAAE,qFAAqF,CAAC,CAAC8D,IAAI,CAAC,IAAI,CAAC,CAAC;YAChQpF,oBAAoB,GAAG,IAAI;UAC7B;QACF;MACF;IACF;IACA,OAAO;MACLoE,QAAQ;MACRS;IACF,CAAC;EACH,CAAC;EACD,MAAMQ,oBAAoB,GAAG5K,gBAAgB,CAAC,MAAM;IAClD,MAAM;MACJ2J,QAAQ;MACRS;IACF,CAAC,GAAGZ,WAAW,CAAC,CAAC;IACjB,IAAIqB,UAAU,GAAG,CAAC;IAClB,IAAIC,cAAc;IAClB,IAAIhJ,QAAQ,EAAE;MACZgJ,cAAc,GAAG,KAAK;MACtB,IAAIV,OAAO,IAAIT,QAAQ,EAAE;QACvBkB,UAAU,GAAGT,OAAO,CAACF,GAAG,GAAGP,QAAQ,CAACO,GAAG,GAAGP,QAAQ,CAACK,SAAS;MAC9D;IACF,CAAC,MAAM;MACLc,cAAc,GAAGlF,KAAK,GAAG,OAAO,GAAG,MAAM;MACzC,IAAIwE,OAAO,IAAIT,QAAQ,EAAE;QACvBkB,UAAU,GAAG,CAACjF,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,KAAKwE,OAAO,CAACU,cAAc,CAAC,GAAGnB,QAAQ,CAACmB,cAAc,CAAC,GAAGnB,QAAQ,CAACI,UAAU,CAAC;MAC5G;IACF;IACA,MAAMgB,iBAAiB,GAAG;MACxB,CAACD,cAAc,GAAGD,UAAU;MAC5B;MACA,CAACvD,IAAI,GAAG8C,OAAO,GAAGA,OAAO,CAAC9C,IAAI,CAAC,GAAG;IACpC,CAAC;IACD,IAAI,OAAOiB,cAAc,CAACuC,cAAc,CAAC,KAAK,QAAQ,IAAI,OAAOvC,cAAc,CAACjB,IAAI,CAAC,KAAK,QAAQ,EAAE;MAClGkB,iBAAiB,CAACuC,iBAAiB,CAAC;IACtC,CAAC,MAAM;MACL,MAAMC,MAAM,GAAGC,IAAI,CAACC,GAAG,CAAC3C,cAAc,CAACuC,cAAc,CAAC,GAAGC,iBAAiB,CAACD,cAAc,CAAC,CAAC;MAC3F,MAAMK,KAAK,GAAGF,IAAI,CAACC,GAAG,CAAC3C,cAAc,CAACjB,IAAI,CAAC,GAAGyD,iBAAiB,CAACzD,IAAI,CAAC,CAAC;MACtE,IAAI0D,MAAM,IAAI,CAAC,IAAIG,KAAK,IAAI,CAAC,EAAE;QAC7B3C,iBAAiB,CAACuC,iBAAiB,CAAC;MACtC;IACF;EACF,CAAC,CAAC;EACF,MAAMK,MAAM,GAAGA,CAACC,WAAW,EAAE;IAC3BC,SAAS,GAAG;EACd,CAAC,GAAG,CAAC,CAAC,KAAK;IACT,IAAIA,SAAS,EAAE;MACbzL,OAAO,CAACqH,WAAW,EAAEiC,OAAO,CAACO,OAAO,EAAE2B,WAAW,EAAE;QACjDE,QAAQ,EAAEtI,KAAK,CAAC0B,WAAW,CAAC4G,QAAQ,CAACC;MACvC,CAAC,CAAC;IACJ,CAAC,MAAM;MACLrC,OAAO,CAACO,OAAO,CAACxC,WAAW,CAAC,GAAGmE,WAAW;IAC5C;EACF,CAAC;EACD,MAAMI,cAAc,GAAGC,KAAK,IAAI;IAC9B,IAAIL,WAAW,GAAGlC,OAAO,CAACO,OAAO,CAACxC,WAAW,CAAC;IAC9C,IAAIpF,QAAQ,EAAE;MACZuJ,WAAW,IAAIK,KAAK;IACtB,CAAC,MAAM;MACLL,WAAW,IAAIK,KAAK,IAAI9F,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IACzC;IACAwF,MAAM,CAACC,WAAW,CAAC;EACrB,CAAC;EACD,MAAMM,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,aAAa,GAAGzC,OAAO,CAACO,OAAO,CAACrC,UAAU,CAAC;IACjD,IAAIwE,SAAS,GAAG,CAAC;IACjB,MAAM7F,QAAQ,GAAGyE,KAAK,CAACC,IAAI,CAACrB,UAAU,CAACK,OAAO,CAAC1D,QAAQ,CAAC;IACxD,KAAK,IAAI8F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9F,QAAQ,CAACqE,MAAM,EAAEyB,CAAC,IAAI,CAAC,EAAE;MAC3C,MAAMxB,GAAG,GAAGtE,QAAQ,CAAC8F,CAAC,CAAC;MACvB,IAAID,SAAS,GAAGvB,GAAG,CAACjD,UAAU,CAAC,GAAGuE,aAAa,EAAE;QAC/C;QACA;QACA,IAAIE,CAAC,KAAK,CAAC,EAAE;UACXD,SAAS,GAAGD,aAAa;QAC3B;QACA;MACF;MACAC,SAAS,IAAIvB,GAAG,CAACjD,UAAU,CAAC;IAC9B;IACA,OAAOwE,SAAS;EAClB,CAAC;EACD,MAAME,sBAAsB,GAAGA,CAAA,KAAM;IACnCN,cAAc,CAAC,CAAC,CAAC,GAAGE,aAAa,CAAC,CAAC,CAAC;EACtC,CAAC;EACD,MAAMK,oBAAoB,GAAGA,CAAA,KAAM;IACjCP,cAAc,CAACE,aAAa,CAAC,CAAC,CAAC;EACjC,CAAC;EACD,MAAM,CAACM,aAAa,EAAE;IACpB5F,QAAQ,EAAE6F,iBAAiB;IAC3B,GAAGC;EACL,CAAC,CAAC,GAAG9L,OAAO,CAAC,WAAW,EAAE;IACxB6F,SAAS,EAAE/G,IAAI,CAACkD,OAAO,CAACJ,WAAW,EAAEI,OAAO,CAACL,aAAa,CAAC;IAC3DwF,WAAW,EAAEnC,iBAAiB;IAC9B+G,0BAA0B,EAAE,IAAI;IAChC9C,sBAAsB;IACtBzH;EACF,CAAC,CAAC;;EAEF;EACA;EACA,MAAMwK,yBAAyB,GAAGrN,KAAK,CAACsN,WAAW,CAACrI,cAAc,IAAI;IACpEiI,iBAAiB,GAAGjI,cAAc,CAAC;IACnC+E,gBAAgB,CAAC;MACf9F,QAAQ,EAAE,IAAI;MACde;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACiI,iBAAiB,CAAC,CAAC;EACvB,MAAM,CAACK,iBAAiB,EAAEC,qBAAqB,CAAC,GAAGnM,OAAO,CAAC,eAAe,EAAE;IAC1E6F,SAAS,EAAE/G,IAAI,CAACkD,OAAO,CAACK,aAAa,EAAEiE,oBAAoB,CAACT,SAAS,CAAC;IACtEsB,WAAW,EAAEzH,eAAe;IAC5BuJ,sBAAsB;IACtBzH,UAAU;IACV4K,eAAe,EAAE;MACfnG,WAAW;MACXhE,KAAK,EAAE;QACLmF,qBAAqB,EAAEnF,KAAK,CAACqF,qBAAqB,IAAIrF,KAAK,CAACmF,qBAAqB;QACjFI,mBAAmB,EAAEvF,KAAK,CAACwF,mBAAmB,IAAIxF,KAAK,CAACuF;MAC1D,CAAC;MACDpB,SAAS,EAAE;QACTkB,qBAAqB,EAAEJ,0BAA0B;QACjDO,mBAAmB,EAAEF;MACvB;IACF;EACF,CAAC,CAAC;EACF,MAAM8E,sBAAsB,GAAGA,CAAA,KAAM;IACnC,MAAMC,mBAAmB,GAAG,CAAC,CAAC;IAC9BA,mBAAmB,CAACC,qBAAqB,GAAG3F,UAAU,GAAG,aAAa1G,IAAI,CAAC0L,aAAa,EAAE;MACxF,GAAGE,kBAAkB;MACrB9F,QAAQ,EAAEgG;IACZ,CAAC,CAAC,GAAG,IAAI;IACT,MAAMQ,mBAAmB,GAAGpE,kBAAkB,IAAIE,gBAAgB;IAClE,MAAMmE,iBAAiB,GAAG7F,UAAU,KAAKvE,aAAa,KAAK,MAAM,IAAImK,mBAAmB,IAAInK,aAAa,KAAK,IAAI,CAAC;IACnHiK,mBAAmB,CAACI,iBAAiB,GAAGD,iBAAiB,GAAG,aAAavM,IAAI,CAACgM,iBAAiB,EAAE;MAC/FS,SAAS,EAAEpH,KAAK,GAAG,OAAO,GAAG,MAAM;MACnCqH,OAAO,EAAElB,sBAAsB;MAC/BvK,QAAQ,EAAE,CAACiH,kBAAkB;MAC7B,GAAG+D;IACL,CAAC,CAAC,GAAG,IAAI;IACTG,mBAAmB,CAACO,eAAe,GAAGJ,iBAAiB,GAAG,aAAavM,IAAI,CAACgM,iBAAiB,EAAE;MAC7FS,SAAS,EAAEpH,KAAK,GAAG,MAAM,GAAG,OAAO;MACnCqH,OAAO,EAAEjB,oBAAoB;MAC7BxK,QAAQ,EAAE,CAACmH,gBAAgB;MAC3B,GAAG6D;IACL,CAAC,CAAC,GAAG,IAAI;IACT,OAAOG,mBAAmB;EAC5B,CAAC;EACD,MAAMQ,sBAAsB,GAAGnN,gBAAgB,CAACsL,SAAS,IAAI;IAC3D,MAAM;MACJ3B,QAAQ;MACRS;IACF,CAAC,GAAGZ,WAAW,CAAC,CAAC;IACjB,IAAI,CAACY,OAAO,IAAI,CAACT,QAAQ,EAAE;MACzB;IACF;IACA,IAAIS,OAAO,CAACjD,KAAK,CAAC,GAAGwC,QAAQ,CAACxC,KAAK,CAAC,EAAE;MACpC;MACA,MAAMiG,eAAe,GAAGzD,QAAQ,CAACzC,WAAW,CAAC,IAAIkD,OAAO,CAACjD,KAAK,CAAC,GAAGwC,QAAQ,CAACxC,KAAK,CAAC,CAAC;MAClFiE,MAAM,CAACgC,eAAe,EAAE;QACtB9B;MACF,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIlB,OAAO,CAAChD,GAAG,CAAC,GAAGuC,QAAQ,CAACvC,GAAG,CAAC,EAAE;MACvC;MACA,MAAMgG,eAAe,GAAGzD,QAAQ,CAACzC,WAAW,CAAC,IAAIkD,OAAO,CAAChD,GAAG,CAAC,GAAGuC,QAAQ,CAACvC,GAAG,CAAC,CAAC;MAC9EgE,MAAM,CAACgC,eAAe,EAAE;QACtB9B;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,MAAM+B,uBAAuB,GAAGrN,gBAAgB,CAAC,MAAM;IACrD,IAAIiH,UAAU,IAAIvE,aAAa,KAAK,KAAK,EAAE;MACzCoG,uBAAuB,CAAC,CAACD,oBAAoB,CAAC;IAChD;EACF,CAAC,CAAC;EACF7J,KAAK,CAACsO,SAAS,CAAC,MAAM;IACpB,MAAMC,YAAY,GAAG3N,QAAQ,CAAC,MAAM;MAClC;MACA;MACA;MACA;MACA;MACA;MACA,IAAIuJ,OAAO,CAACO,OAAO,EAAE;QACnBkB,oBAAoB,CAAC,CAAC;MACxB;IACF,CAAC,CAAC;IACF,IAAI4C,cAAc;;IAElB;AACJ;AACA;IACI,MAAMC,cAAc,GAAGC,OAAO,IAAI;MAChCA,OAAO,CAACC,OAAO,CAACC,MAAM,IAAI;QACxBA,MAAM,CAACC,YAAY,CAACF,OAAO,CAAC/M,IAAI,IAAI;UAClC4M,cAAc,EAAEM,SAAS,CAAClN,IAAI,CAAC;QACjC,CAAC,CAAC;QACFgN,MAAM,CAACG,UAAU,CAACJ,OAAO,CAAC/M,IAAI,IAAI;UAChC4M,cAAc,EAAEQ,OAAO,CAACpN,IAAI,CAAC;QAC/B,CAAC,CAAC;MACJ,CAAC,CAAC;MACF2M,YAAY,CAAC,CAAC;MACdF,uBAAuB,CAAC,CAAC;IAC3B,CAAC;IACD,MAAMY,GAAG,GAAG7N,WAAW,CAAC+I,OAAO,CAACO,OAAO,CAAC;IACxCuE,GAAG,CAACC,gBAAgB,CAAC,QAAQ,EAAEX,YAAY,CAAC;IAC5C,IAAIY,gBAAgB;IACpB,IAAI,OAAOC,cAAc,KAAK,WAAW,EAAE;MACzCZ,cAAc,GAAG,IAAIY,cAAc,CAACb,YAAY,CAAC;MACjD9C,KAAK,CAACC,IAAI,CAACrB,UAAU,CAACK,OAAO,CAAC1D,QAAQ,CAAC,CAAC2H,OAAO,CAACU,KAAK,IAAI;QACvDb,cAAc,CAACQ,OAAO,CAACK,KAAK,CAAC;MAC/B,CAAC,CAAC;IACJ;IACA,IAAI,OAAOC,gBAAgB,KAAK,WAAW,EAAE;MAC3CH,gBAAgB,GAAG,IAAIG,gBAAgB,CAACb,cAAc,CAAC;MACvDU,gBAAgB,CAACH,OAAO,CAAC3E,UAAU,CAACK,OAAO,EAAE;QAC3C6E,SAAS,EAAE;MACb,CAAC,CAAC;IACJ;IACA,OAAO,MAAM;MACXhB,YAAY,CAACiB,KAAK,CAAC,CAAC;MACpBP,GAAG,CAACQ,mBAAmB,CAAC,QAAQ,EAAElB,YAAY,CAAC;MAC/CY,gBAAgB,EAAEO,UAAU,CAAC,CAAC;MAC9BlB,cAAc,EAAEkB,UAAU,CAAC,CAAC;IAC9B,CAAC;EACH,CAAC,EAAE,CAAC9D,oBAAoB,EAAEyC,uBAAuB,CAAC,CAAC;;EAEnD;AACF;AACA;AACA;EACErO,KAAK,CAACsO,SAAS,CAAC,MAAM;IACpB,MAAMqB,eAAe,GAAGlE,KAAK,CAACC,IAAI,CAACrB,UAAU,CAACK,OAAO,CAAC1D,QAAQ,CAAC;IAC/D,MAAMqE,MAAM,GAAGsE,eAAe,CAACtE,MAAM;IACrC,IAAI,OAAOuE,oBAAoB,KAAK,WAAW,IAAIvE,MAAM,GAAG,CAAC,IAAIpD,UAAU,IAAIvE,aAAa,KAAK,KAAK,EAAE;MACtG,MAAMmM,QAAQ,GAAGF,eAAe,CAAC,CAAC,CAAC;MACnC,MAAMG,OAAO,GAAGH,eAAe,CAACtE,MAAM,GAAG,CAAC,CAAC;MAC3C,MAAM0E,eAAe,GAAG;QACtBxM,IAAI,EAAE4G,OAAO,CAACO,OAAO;QACrBsF,SAAS,EAAE;MACb,CAAC;MACD,MAAMC,uBAAuB,GAAGC,OAAO,IAAI;QACzCxG,qBAAqB,CAAC,CAACwG,OAAO,CAAC,CAAC,CAAC,CAACC,cAAc,CAAC;MACnD,CAAC;MACD,MAAMC,aAAa,GAAG,IAAIR,oBAAoB,CAACK,uBAAuB,EAAEF,eAAe,CAAC;MACxFK,aAAa,CAACpB,OAAO,CAACa,QAAQ,CAAC;MAC/B,MAAMQ,qBAAqB,GAAGH,OAAO,IAAI;QACvCtG,mBAAmB,CAAC,CAACsG,OAAO,CAAC,CAAC,CAAC,CAACC,cAAc,CAAC;MACjD,CAAC;MACD,MAAMG,YAAY,GAAG,IAAIV,oBAAoB,CAACS,qBAAqB,EAAEN,eAAe,CAAC;MACrFO,YAAY,CAACtB,OAAO,CAACc,OAAO,CAAC;MAC7B,OAAO,MAAM;QACXM,aAAa,CAACV,UAAU,CAAC,CAAC;QAC1BY,YAAY,CAACZ,UAAU,CAAC,CAAC;MAC3B,CAAC;IACH;IACA,OAAOa,SAAS;EAClB,CAAC,EAAE,CAACtI,UAAU,EAAEvE,aAAa,EAAEmG,oBAAoB,EAAE5C,YAAY,EAAEoE,MAAM,CAAC,CAAC;EAC3ErL,KAAK,CAACsO,SAAS,CAAC,MAAM;IACpBjF,UAAU,CAAC,IAAI,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EACNrJ,KAAK,CAACsO,SAAS,CAAC,MAAM;IACpB1C,oBAAoB,CAAC,CAAC;EACxB,CAAC,CAAC;EACF5L,KAAK,CAACsO,SAAS,CAAC,MAAM;IACpB;IACAH,sBAAsB,CAAC7H,qBAAqB,KAAKiD,cAAc,CAAC;EAClE,CAAC,EAAE,CAAC4E,sBAAsB,EAAE5E,cAAc,CAAC,CAAC;EAC5CvJ,KAAK,CAACwQ,mBAAmB,CAACzJ,MAAM,EAAE,OAAO;IACvC0J,eAAe,EAAE7E,oBAAoB;IACrC8E,mBAAmB,EAAErC;EACvB,CAAC,CAAC,EAAE,CAACzC,oBAAoB,EAAEyC,uBAAuB,CAAC,CAAC;EACpD,MAAM,CAACsC,aAAa,EAAEC,kBAAkB,CAAC,GAAGvP,OAAO,CAAC,WAAW,EAAE;IAC/D6F,SAAS,EAAE/G,IAAI,CAACkD,OAAO,CAACI,SAAS,EAAEiE,iBAAiB,CAACR,SAAS,CAAC;IAC/DsB,WAAW,EAAEjD,aAAa;IAC1B+E,sBAAsB;IACtBzH,UAAU;IACV4K,eAAe,EAAE;MACflJ,KAAK,EAAEgF;IACT;EACF,CAAC,CAAC;EACF,MAAM9F,SAAS,GAAG,aAAalC,IAAI,CAACoP,aAAa,EAAE;IACjD,GAAGC;EACL,CAAC,CAAC;EACF,IAAIC,UAAU,GAAG,CAAC;EAClB,MAAM7J,QAAQ,GAAGhH,KAAK,CAAC8Q,QAAQ,CAACC,GAAG,CAAC9J,YAAY,EAAEoI,KAAK,IAAI;IACzD,IAAI,EAAE,aAAarP,KAAK,CAACgR,cAAc,CAAC3B,KAAK,CAAC,EAAE;MAC9C,OAAO,IAAI;IACb;IACA,IAAItG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIhJ,UAAU,CAACoP,KAAK,CAAC,EAAE;QACrBnG,OAAO,CAACC,KAAK,CAAC,CAAC,+DAA+D,EAAE,sCAAsC,CAAC,CAACwC,IAAI,CAAC,IAAI,CAAC,CAAC;MACrI;IACF;IACA,MAAMsF,UAAU,GAAG5B,KAAK,CAACtL,KAAK,CAAC8D,KAAK,KAAK0I,SAAS,GAAGM,UAAU,GAAGxB,KAAK,CAACtL,KAAK,CAAC8D,KAAK;IACnFoC,YAAY,CAACiH,GAAG,CAACD,UAAU,EAAEJ,UAAU,CAAC;IACxC,MAAMM,QAAQ,GAAGF,UAAU,KAAKpJ,KAAK;IACrCgJ,UAAU,IAAI,CAAC;IACf,OAAO,aAAa7Q,KAAK,CAACoR,YAAY,CAAC/B,KAAK,EAAE;MAC5CgC,SAAS,EAAEvJ,OAAO,KAAK,WAAW;MAClCrE,SAAS,EAAE0N,QAAQ,IAAI,CAAC/H,OAAO,IAAI3F,SAAS;MAC5C0N,QAAQ;MACR3J,qBAAqB;MACrBH,QAAQ;MACRO,SAAS;MACTC,KAAK,EAAEoJ,UAAU;MACjB,IAAIJ,UAAU,KAAK,CAAC,IAAIhJ,KAAK,KAAK,KAAK,IAAI,CAACwH,KAAK,CAACtL,KAAK,CAACuN,QAAQ,GAAG;QACjEA,QAAQ,EAAE;MACZ,CAAC,GAAG,CAAC,CAAC;IACR,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAMC,aAAa,GAAGC,KAAK,IAAI;IAC7B;IACA,IAAIA,KAAK,CAACC,MAAM,IAAID,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACG,OAAO,IAAIH,KAAK,CAACI,OAAO,EAAE;MACpE;IACF;IACA,MAAMjQ,IAAI,GAAG0I,UAAU,CAACK,OAAO;IAC/B,MAAMvI,YAAY,GAAGhB,aAAa,CAACQ,IAAI,CAAC,CAACkQ,aAAa;IACtD;IACA;IACA;IACA,MAAMC,IAAI,GAAG3P,YAAY,CAACM,YAAY,CAAC,MAAM,CAAC;IAC9C,IAAIqP,IAAI,KAAK,KAAK,EAAE;MAClB;IACF;IACA,IAAIC,eAAe,GAAGzK,WAAW,KAAK,YAAY,GAAG,WAAW,GAAG,SAAS;IAC5E,IAAI0K,WAAW,GAAG1K,WAAW,KAAK,YAAY,GAAG,YAAY,GAAG,WAAW;IAC3E,IAAIA,WAAW,KAAK,YAAY,IAAIV,KAAK,EAAE;MACzC;MACAmL,eAAe,GAAG,YAAY;MAC9BC,WAAW,GAAG,WAAW;IAC3B;IACA,QAAQR,KAAK,CAACS,GAAG;MACf,KAAKF,eAAe;QAClBP,KAAK,CAACU,cAAc,CAAC,CAAC;QACtBhQ,SAAS,CAACP,IAAI,EAAEQ,YAAY,EAAEJ,YAAY,CAAC;QAC3C;MACF,KAAKiQ,WAAW;QACdR,KAAK,CAACU,cAAc,CAAC,CAAC;QACtBhQ,SAAS,CAACP,IAAI,EAAEQ,YAAY,EAAET,QAAQ,CAAC;QACvC;MACF,KAAK,MAAM;QACT8P,KAAK,CAACU,cAAc,CAAC,CAAC;QACtBhQ,SAAS,CAACP,IAAI,EAAE,IAAI,EAAED,QAAQ,CAAC;QAC/B;MACF,KAAK,KAAK;QACR8P,KAAK,CAACU,cAAc,CAAC,CAAC;QACtBhQ,SAAS,CAACP,IAAI,EAAE,IAAI,EAAEI,YAAY,CAAC;QACnC;MACF;QACE;IACJ;EACF,CAAC;EACD,MAAM4L,mBAAmB,GAAGD,sBAAsB,CAAC,CAAC;EACpD,MAAM,CAACyE,QAAQ,EAAEC,aAAa,CAAC,GAAG/Q,OAAO,CAAC,MAAM,EAAE;IAChDsF,GAAG;IACHO,SAAS,EAAE/G,IAAI,CAACkD,OAAO,CAACE,IAAI,EAAE2D,SAAS,CAAC;IACxCsB,WAAW,EAAE7E,QAAQ;IACrB2G,sBAAsB,EAAE;MACtB,GAAGA,sBAAsB;MACzB,GAAGtC,KAAK;MACRb;IACF,CAAC;IACDtE;EACF,CAAC,CAAC;EACF,MAAM,CAACwP,YAAY,EAAEC,iBAAiB,CAAC,GAAGjR,OAAO,CAAC,UAAU,EAAE;IAC5DsF,GAAG,EAAEwD,OAAO;IACZjD,SAAS,EAAE7D,OAAO,CAACG,QAAQ;IAC3BgF,WAAW,EAAE7D,YAAY;IACzB2F,sBAAsB;IACtBzH,UAAU;IACV4K,eAAe,EAAE;MACflJ,KAAK,EAAE;QACLL,QAAQ,EAAE6F,aAAa,CAAC7F,QAAQ;QAChC,CAACpB,QAAQ,GAAG,SAAS8D,KAAK,GAAG,MAAM,GAAG,OAAO,EAAE,GAAG,cAAc,GAAGmB,gBAAgB,GAAGwI,SAAS,GAAG,CAACxG,aAAa,CAAC9E;MACnH;IACF;EACF,CAAC,CAAC;EACF,MAAM,CAACsN,QAAQ,EAAEC,aAAa,CAAC,GAAGnR,OAAO,CAAC,MAAM,EAAE;IAChDsF,GAAG,EAAE0D,UAAU;IACfnD,SAAS,EAAE/G,IAAI,CAACkD,OAAO,CAAC1B,IAAI,EAAE0B,OAAO,CAAC+B,aAAa,CAAC;IACpDoD,WAAW,EAAErD,IAAI;IACjBmF,sBAAsB;IACtBzH,UAAU;IACV4P,YAAY,EAAEC,QAAQ,KAAK;MACzB,GAAGA,QAAQ;MACXC,SAAS,EAAEnB,KAAK,IAAI;QAClBD,aAAa,CAACC,KAAK,CAAC;QACpBkB,QAAQ,CAACC,SAAS,GAAGnB,KAAK,CAAC;MAC7B;IACF,CAAC;EACH,CAAC,CAAC;EACF,OAAO,aAAa/P,KAAK,CAAC0Q,QAAQ,EAAE;IAClC,GAAGC,aAAa;IAChBpL,QAAQ,EAAE,CAAC2G,mBAAmB,CAACI,iBAAiB,EAAEJ,mBAAmB,CAACC,qBAAqB,EAAE,aAAanM,KAAK,CAAC4Q,YAAY,EAAE;MAC5H,GAAGC,iBAAiB;MACpBtL,QAAQ,EAAE,CAAC,aAAazF,IAAI,CAACgR,QAAQ,EAAE;QACrC,YAAY,EAAE1L,SAAS;QACvB,iBAAiB,EAAEC,cAAc;QACjC,kBAAkB,EAAEQ,WAAW,KAAK,UAAU,GAAG,UAAU,GAAG,IAAI;QAClEwK,IAAI,EAAE,SAAS;QACf,GAAGU,aAAa;QAChBxL,QAAQ,EAAEA;MACZ,CAAC,CAAC,EAAEoC,OAAO,IAAI3F,SAAS;IAC1B,CAAC,CAAC,EAAEkK,mBAAmB,CAACO,eAAe;EACzC,CAAC,CAAC;AACJ,CAAC,CAAC;AACFnF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGzC,IAAI,CAACoM,SAAS,CAAC,yBAAyB;EAC9E;EACA;EACA;EACA;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE7L,MAAM,EAAE3G,OAAO;EACf;AACF;AACA;AACA;AACA;EACEgH,wBAAwB,EAAElH,SAAS,CAAC2S,IAAI;EACxC;AACF;AACA;EACE,YAAY,EAAE3S,SAAS,CAAC4S,MAAM;EAC9B;AACF;AACA;EACE,iBAAiB,EAAE5S,SAAS,CAAC4S,MAAM;EACnC;AACF;AACA;AACA;AACA;EACE3P,QAAQ,EAAEjD,SAAS,CAAC2S,IAAI;EACxB;AACF;AACA;EACE7L,QAAQ,EAAE9G,SAAS,CAAC6S,IAAI;EACxB;AACF;AACA;EACE1P,OAAO,EAAEnD,SAAS,CAAC8S,MAAM;EACzB;AACF;AACA;EACE9L,SAAS,EAAEhH,SAAS,CAAC4S,MAAM;EAC3B;AACF;AACA;AACA;EACE3L,SAAS,EAAEjH,SAAS,CAACsI,WAAW;EAChC;AACF;AACA;AACA;EACE3C,cAAc,EAAE3F,SAAS,CAAC,sCAAsC+S,SAAS,CAAC,CAAC/S,SAAS,CAACgT,KAAK,CAAC,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,EAAEhT,SAAS,CAAC4S,MAAM,CAAC,CAAC;EACxI;AACF;AACA;AACA;AACA;AACA;EACEzL,QAAQ,EAAEnH,SAAS,CAACiT,IAAI;EACxB;AACF;AACA;AACA;EACE7L,WAAW,EAAEpH,SAAS,CAACgT,KAAK,CAAC,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;EACxD;AACF;AACA;AACA;AACA;EACE3L,qBAAqB,EAAErH,SAAS,CAACsI,WAAW;EAC5C;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE9E,aAAa,EAAExD,SAAS,CAAC,sCAAsCgT,KAAK,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;EAC3F;AACF;AACA;AACA;EACE1L,qBAAqB,EAAEtH,SAAS,CAAC2S,IAAI;EACrC;AACF;AACA;AACA;EACEpL,SAAS,EAAEvH,SAAS,CAACkT,KAAK,CAAC;IACzBtK,mBAAmB,EAAE5I,SAAS,CAAC+S,SAAS,CAAC,CAAC/S,SAAS,CAACiT,IAAI,EAAEjT,SAAS,CAAC8S,MAAM,CAAC,CAAC;IAC5EvP,SAAS,EAAEvD,SAAS,CAAC+S,SAAS,CAAC,CAAC/S,SAAS,CAACiT,IAAI,EAAEjT,SAAS,CAAC8S,MAAM,CAAC,CAAC;IAClErR,IAAI,EAAEzB,SAAS,CAAC+S,SAAS,CAAC,CAAC/S,SAAS,CAACiT,IAAI,EAAEjT,SAAS,CAAC8S,MAAM,CAAC,CAAC;IAC7DzP,IAAI,EAAErD,SAAS,CAAC+S,SAAS,CAAC,CAAC/S,SAAS,CAACiT,IAAI,EAAEjT,SAAS,CAAC8S,MAAM,CAAC,CAAC;IAC7DK,SAAS,EAAEnT,SAAS,CAAC+S,SAAS,CAAC,CAAC/S,SAAS,CAACiT,IAAI,EAAEjT,SAAS,CAAC8S,MAAM,CAAC,CAAC;IAClEtP,aAAa,EAAExD,SAAS,CAAC+S,SAAS,CAAC,CAAC/S,SAAS,CAACiT,IAAI,EAAEjT,SAAS,CAAC8S,MAAM,CAAC,CAAC;IACtExP,QAAQ,EAAEtD,SAAS,CAAC+S,SAAS,CAAC,CAAC/S,SAAS,CAACiT,IAAI,EAAEjT,SAAS,CAAC8S,MAAM,CAAC,CAAC;IACjErK,qBAAqB,EAAEzI,SAAS,CAAC+S,SAAS,CAAC,CAAC/S,SAAS,CAACiT,IAAI,EAAEjT,SAAS,CAAC8S,MAAM,CAAC;EAC/E,CAAC,CAAC;EACF;AACF;AACA;AACA;EACE1P,KAAK,EAAEpD,SAAS,CAACkT,KAAK,CAAC;IACrBtK,mBAAmB,EAAE5I,SAAS,CAACsI,WAAW;IAC1CK,mBAAmB,EAAE3I,SAAS,CAACsI,WAAW;IAC1C/E,SAAS,EAAEvD,SAAS,CAACsI,WAAW;IAChC7G,IAAI,EAAEzB,SAAS,CAACsI,WAAW;IAC3BjF,IAAI,EAAErD,SAAS,CAACsI,WAAW;IAC3B6K,SAAS,EAAEnT,SAAS,CAACsI,WAAW;IAChC9E,aAAa,EAAExD,SAAS,CAACsI,WAAW;IACpChF,QAAQ,EAAEtD,SAAS,CAACsI,WAAW;IAC/BG,qBAAqB,EAAEzI,SAAS,CAACsI,WAAW;IAC5CC,qBAAqB,EAAEvI,SAAS,CAACsI;EACnC,CAAC,CAAC;EACF;AACF;AACA;EACE8K,EAAE,EAAEpT,SAAS,CAAC+S,SAAS,CAAC,CAAC/S,SAAS,CAACqT,OAAO,CAACrT,SAAS,CAAC+S,SAAS,CAAC,CAAC/S,SAAS,CAACiT,IAAI,EAAEjT,SAAS,CAAC8S,MAAM,EAAE9S,SAAS,CAAC2S,IAAI,CAAC,CAAC,CAAC,EAAE3S,SAAS,CAACiT,IAAI,EAAEjT,SAAS,CAAC8S,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;AACA;EACEtL,iBAAiB,EAAExH,SAAS,CAAC8S,MAAM;EACnC;AACF;AACA;AACA;AACA;EACErL,oBAAoB,EAAEzH,SAAS,CAAC8S,MAAM;EACtC;AACF;AACA;AACA;EACEpL,SAAS,EAAE1H,SAAS,CAACgT,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;EAC/D;AACF;AACA;AACA;EACErL,KAAK,EAAE3H,SAAS,CAACsT,GAAG;EACpB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE1L,OAAO,EAAE5H,SAAS,CAACgT,KAAK,CAAC,CAAC,WAAW,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;EACjE;AACF;AACA;AACA;AACA;EACEnL,gBAAgB,EAAE7H,SAAS,CAAC2S;AAC9B,CAAC,GAAG,KAAK,CAAC;AACV,eAAerM,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}