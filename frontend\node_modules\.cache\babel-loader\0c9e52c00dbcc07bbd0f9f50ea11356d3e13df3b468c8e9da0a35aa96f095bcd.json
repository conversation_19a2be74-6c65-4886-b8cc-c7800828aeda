{"ast": null, "code": "import { measureViewportBox } from '../../projection/utils/measure.mjs';\nimport { DOMVisualElement } from '../dom/DOMVisualElement.mjs';\nimport { isCSSVariableName } from '../dom/utils/is-css-variable.mjs';\nimport { buildHTMLStyles } from './utils/build-styles.mjs';\nimport { transformProps } from './utils/keys-transform.mjs';\nimport { readTransformValue } from './utils/parse-transform.mjs';\nimport { renderHTML } from './utils/render.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\nfunction getComputedStyle(element) {\n  return window.getComputedStyle(element);\n}\nclass HTMLVisualElement extends DOMVisualElement {\n  constructor() {\n    super(...arguments);\n    this.type = \"html\";\n    this.renderInstance = renderHTML;\n  }\n  readValueFromInstance(instance, key) {\n    if (transformProps.has(key)) {\n      return readTransformValue(instance, key);\n    } else {\n      const computedStyle = getComputedStyle(instance);\n      const value = (isCSSVariableName(key) ? computedStyle.getPropertyValue(key) : computedStyle[key]) || 0;\n      return typeof value === \"string\" ? value.trim() : value;\n    }\n  }\n  measureInstanceViewportBox(instance, {\n    transformPagePoint\n  }) {\n    return measureViewportBox(instance, transformPagePoint);\n  }\n  build(renderState, latestValues, props) {\n    buildHTMLStyles(renderState, latestValues, props.transformTemplate);\n  }\n  scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n    return scrapeMotionValuesFromProps(props, prevProps, visualElement);\n  }\n}\nexport { HTMLVisualElement, getComputedStyle };", "map": {"version": 3, "names": ["measureViewportBox", "DOMVisualElement", "isCSSVariableName", "buildHTMLStyles", "transformProps", "readTransformValue", "renderHTML", "scrapeMotionValuesFromProps", "getComputedStyle", "element", "window", "HTMLVisualElement", "constructor", "arguments", "type", "renderInstance", "readValueFromInstance", "instance", "key", "has", "computedStyle", "value", "getPropertyValue", "trim", "measureInstanceViewportBox", "transformPagePoint", "build", "renderState", "latestValues", "props", "transformTemplate", "prevProps", "visualElement"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/framer-motion/dist/es/render/html/HTMLVisualElement.mjs"], "sourcesContent": ["import { measureViewportBox } from '../../projection/utils/measure.mjs';\nimport { DOMVisualElement } from '../dom/DOMVisualElement.mjs';\nimport { isCSSVariableName } from '../dom/utils/is-css-variable.mjs';\nimport { buildHTMLStyles } from './utils/build-styles.mjs';\nimport { transformProps } from './utils/keys-transform.mjs';\nimport { readTransformValue } from './utils/parse-transform.mjs';\nimport { renderHTML } from './utils/render.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\n\nfunction getComputedStyle(element) {\n    return window.getComputedStyle(element);\n}\nclass HTMLVisualElement extends DOMVisualElement {\n    constructor() {\n        super(...arguments);\n        this.type = \"html\";\n        this.renderInstance = renderHTML;\n    }\n    readValueFromInstance(instance, key) {\n        if (transformProps.has(key)) {\n            return readTransformValue(instance, key);\n        }\n        else {\n            const computedStyle = getComputedStyle(instance);\n            const value = (isCSSVariableName(key)\n                ? computedStyle.getPropertyValue(key)\n                : computedStyle[key]) || 0;\n            return typeof value === \"string\" ? value.trim() : value;\n        }\n    }\n    measureInstanceViewportBox(instance, { transformPagePoint }) {\n        return measureViewportBox(instance, transformPagePoint);\n    }\n    build(renderState, latestValues, props) {\n        buildHTMLStyles(renderState, latestValues, props.transformTemplate);\n    }\n    scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n        return scrapeMotionValuesFromProps(props, prevProps, visualElement);\n    }\n}\n\nexport { HTMLVisualElement, getComputedStyle };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,oCAAoC;AACvE,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,2BAA2B,QAAQ,kCAAkC;AAE9E,SAASC,gBAAgBA,CAACC,OAAO,EAAE;EAC/B,OAAOC,MAAM,CAACF,gBAAgB,CAACC,OAAO,CAAC;AAC3C;AACA,MAAME,iBAAiB,SAASV,gBAAgB,CAAC;EAC7CW,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,IAAI,GAAG,MAAM;IAClB,IAAI,CAACC,cAAc,GAAGT,UAAU;EACpC;EACAU,qBAAqBA,CAACC,QAAQ,EAAEC,GAAG,EAAE;IACjC,IAAId,cAAc,CAACe,GAAG,CAACD,GAAG,CAAC,EAAE;MACzB,OAAOb,kBAAkB,CAACY,QAAQ,EAAEC,GAAG,CAAC;IAC5C,CAAC,MACI;MACD,MAAME,aAAa,GAAGZ,gBAAgB,CAACS,QAAQ,CAAC;MAChD,MAAMI,KAAK,GAAG,CAACnB,iBAAiB,CAACgB,GAAG,CAAC,GAC/BE,aAAa,CAACE,gBAAgB,CAACJ,GAAG,CAAC,GACnCE,aAAa,CAACF,GAAG,CAAC,KAAK,CAAC;MAC9B,OAAO,OAAOG,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAACE,IAAI,CAAC,CAAC,GAAGF,KAAK;IAC3D;EACJ;EACAG,0BAA0BA,CAACP,QAAQ,EAAE;IAAEQ;EAAmB,CAAC,EAAE;IACzD,OAAOzB,kBAAkB,CAACiB,QAAQ,EAAEQ,kBAAkB,CAAC;EAC3D;EACAC,KAAKA,CAACC,WAAW,EAAEC,YAAY,EAAEC,KAAK,EAAE;IACpC1B,eAAe,CAACwB,WAAW,EAAEC,YAAY,EAAEC,KAAK,CAACC,iBAAiB,CAAC;EACvE;EACAvB,2BAA2BA,CAACsB,KAAK,EAAEE,SAAS,EAAEC,aAAa,EAAE;IACzD,OAAOzB,2BAA2B,CAACsB,KAAK,EAAEE,SAAS,EAAEC,aAAa,CAAC;EACvE;AACJ;AAEA,SAASrB,iBAAiB,EAAEH,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}