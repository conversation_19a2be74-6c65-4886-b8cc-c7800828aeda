{"ast": null, "code": "import { isDragActive } from './drag/state/is-active.mjs';\nimport { setupGesture } from './utils/setup.mjs';\nfunction isValidHover(event) {\n  return !(event.pointerType === \"touch\" || isDragActive());\n}\n/**\n * Create a hover gesture. hover() is different to .addEventListener(\"pointerenter\")\n * in that it has an easier syntax, filters out polyfilled touch events, interoperates\n * with drag gestures, and automatically removes the \"pointerennd\" event listener when the hover ends.\n *\n * @public\n */\nfunction hover(elementOrSelector, onHoverStart, options = {}) {\n  const [elements, eventOptions, cancel] = setupGesture(elementOrSelector, options);\n  const onPointerEnter = enterEvent => {\n    if (!isValidHover(enterEvent)) return;\n    const {\n      target\n    } = enterEvent;\n    const onHoverEnd = onHoverStart(target, enterEvent);\n    if (typeof onHoverEnd !== \"function\" || !target) return;\n    const onPointerLeave = leaveEvent => {\n      if (!isValidHover(leaveEvent)) return;\n      onHoverEnd(leaveEvent);\n      target.removeEventListener(\"pointerleave\", onPointerLeave);\n    };\n    target.addEventListener(\"pointerleave\", onPointerLeave, eventOptions);\n  };\n  elements.forEach(element => {\n    element.addEventListener(\"pointerenter\", onPointerEnter, eventOptions);\n  });\n  return cancel;\n}\nexport { hover };", "map": {"version": 3, "names": ["isDragActive", "setupGesture", "isValidHover", "event", "pointerType", "hover", "elementOrSelector", "onHoverStart", "options", "elements", "eventOptions", "cancel", "onPointerEnter", "enterEvent", "target", "onHoverEnd", "onPointerLeave", "leaveEvent", "removeEventListener", "addEventListener", "for<PERSON>ach", "element"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/motion-dom/dist/es/gestures/hover.mjs"], "sourcesContent": ["import { isDragActive } from './drag/state/is-active.mjs';\nimport { setupGesture } from './utils/setup.mjs';\n\nfunction isValidHover(event) {\n    return !(event.pointerType === \"touch\" || isDragActive());\n}\n/**\n * Create a hover gesture. hover() is different to .addEventListener(\"pointerenter\")\n * in that it has an easier syntax, filters out polyfilled touch events, interoperates\n * with drag gestures, and automatically removes the \"pointerennd\" event listener when the hover ends.\n *\n * @public\n */\nfunction hover(elementOrSelector, onHoverStart, options = {}) {\n    const [elements, eventOptions, cancel] = setupGesture(elementOrSelector, options);\n    const onPointerEnter = (enterEvent) => {\n        if (!isValidHover(enterEvent))\n            return;\n        const { target } = enterEvent;\n        const onHoverEnd = onHoverStart(target, enterEvent);\n        if (typeof onHoverEnd !== \"function\" || !target)\n            return;\n        const onPointerLeave = (leaveEvent) => {\n            if (!isValidHover(leaveEvent))\n                return;\n            onHoverEnd(leaveEvent);\n            target.removeEventListener(\"pointerleave\", onPointerLeave);\n        };\n        target.addEventListener(\"pointerleave\", onPointerLeave, eventOptions);\n    };\n    elements.forEach((element) => {\n        element.addEventListener(\"pointerenter\", onPointerEnter, eventOptions);\n    });\n    return cancel;\n}\n\nexport { hover };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,4BAA4B;AACzD,SAASC,YAAY,QAAQ,mBAAmB;AAEhD,SAASC,YAAYA,CAACC,KAAK,EAAE;EACzB,OAAO,EAAEA,KAAK,CAACC,WAAW,KAAK,OAAO,IAAIJ,YAAY,CAAC,CAAC,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,KAAKA,CAACC,iBAAiB,EAAEC,YAAY,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EAC1D,MAAM,CAACC,QAAQ,EAAEC,YAAY,EAAEC,MAAM,CAAC,GAAGV,YAAY,CAACK,iBAAiB,EAAEE,OAAO,CAAC;EACjF,MAAMI,cAAc,GAAIC,UAAU,IAAK;IACnC,IAAI,CAACX,YAAY,CAACW,UAAU,CAAC,EACzB;IACJ,MAAM;MAAEC;IAAO,CAAC,GAAGD,UAAU;IAC7B,MAAME,UAAU,GAAGR,YAAY,CAACO,MAAM,EAAED,UAAU,CAAC;IACnD,IAAI,OAAOE,UAAU,KAAK,UAAU,IAAI,CAACD,MAAM,EAC3C;IACJ,MAAME,cAAc,GAAIC,UAAU,IAAK;MACnC,IAAI,CAACf,YAAY,CAACe,UAAU,CAAC,EACzB;MACJF,UAAU,CAACE,UAAU,CAAC;MACtBH,MAAM,CAACI,mBAAmB,CAAC,cAAc,EAAEF,cAAc,CAAC;IAC9D,CAAC;IACDF,MAAM,CAACK,gBAAgB,CAAC,cAAc,EAAEH,cAAc,EAAEN,YAAY,CAAC;EACzE,CAAC;EACDD,QAAQ,CAACW,OAAO,CAAEC,OAAO,IAAK;IAC1BA,OAAO,CAACF,gBAAgB,CAAC,cAAc,EAAEP,cAAc,EAAEF,YAAY,CAAC;EAC1E,CAAC,CAAC;EACF,OAAOC,MAAM;AACjB;AAEA,SAASN,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}