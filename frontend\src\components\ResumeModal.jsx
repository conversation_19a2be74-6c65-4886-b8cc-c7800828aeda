import React from "react";
import { Mo<PERSON>, <PERSON>, IconButton, Button } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import DownloadIcon from "@mui/icons-material/Download";
import { motion } from "framer-motion";

const ResumeModal = ({ open, handleClose }) => {
  const handleDownload = () => {
    // Create a temporary link element to trigger download
    const link = document.createElement("a");
    link.href = "/resume.pdf";
    link.download = "Abhishek_DS_Resume.pdf";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <Modal 
      open={open} 
      onClose={handleClose}
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.8 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
      >
        <Box
          sx={{
            position: "relative",
            width: "90vw",
            height: "90vh",
            maxWidth: "900px",
            maxHeight: "800px",
            bgcolor: "#1a1a1a",
            borderRadius: "16px",
            boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.8)",
            border: "1px solid #333",
            overflow: "hidden",
            display: "flex",
            flexDirection: "column",
          }}
        >
          {/* Header with Close Button */}
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              p: 2,
              borderBottom: "1px solid #333",
              background: "linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%)",
            }}
          >
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                gap: 2,
              }}
            >
              <Box
                sx={{
                  width: 12,
                  height: 12,
                  borderRadius: "50%",
                  bgcolor: "#ff5f57",
                }}
              />
              <Box
                sx={{
                  width: 12,
                  height: 12,
                  borderRadius: "50%",
                  bgcolor: "#ffbd2e",
                }}
              />
              <Box
                sx={{
                  width: 12,
                  height: 12,
                  borderRadius: "50%",
                  bgcolor: "#28ca42",
                }}
              />
            </Box>

            <Box
              sx={{
                fontFamily: "'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif",
                fontSize: "1.1rem",
                fontWeight: 600,
                color: "#fff",
                letterSpacing: "-0.02em",
              }}
            >
              Resume - Abhishek D S
            </Box>

            <IconButton
              onClick={handleClose}
              sx={{
                color: "#fff",
                "&:hover": {
                  bgcolor: "rgba(255, 255, 255, 0.1)",
                  transform: "scale(1.1)",
                },
                transition: "all 0.2s ease",
              }}
            >
              <CloseIcon />
            </IconButton>
          </Box>

          {/* PDF Viewer */}
          <Box
            sx={{
              flex: 1,
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              p: 2,
              bgcolor: "#0f0f0f",
            }}
          >
            <iframe
              src="/resume.pdf"
              style={{
                width: "100%",
                height: "100%",
                border: "none",
                borderRadius: "8px",
                backgroundColor: "#fff",
              }}
              title="Resume PDF"
            />
          </Box>

          {/* Download Button */}
          <Box
            sx={{
              p: 3,
              borderTop: "1px solid #333",
              background: "linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%)",
              display: "flex",
              justifyContent: "center",
            }}
          >
            <Button
              onClick={handleDownload}
              startIcon={<DownloadIcon />}
              sx={{
                fontFamily: "'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif",
                fontSize: "1rem",
                fontWeight: 600,
                letterSpacing: "-0.01em",
                px: 4,
                py: 1.5,
                bgcolor: "#FFD700",
                color: "#000",
                borderRadius: "12px",
                textTransform: "none",
                boxShadow: "0 4px 14px 0 rgba(255, 215, 0, 0.3)",
                "&:hover": {
                  bgcolor: "#FFC700",
                  transform: "translateY(-2px)",
                  boxShadow: "0 8px 25px 0 rgba(255, 215, 0, 0.4)",
                },
                "&:active": {
                  transform: "translateY(0px)",
                },
                transition: "all 0.2s ease",
              }}
            >
              Download Resume
            </Button>
          </Box>
        </Box>
      </motion.div>
    </Modal>
  );
};

export default ResumeModal;
