{"ast": null, "code": "function getLayerName(pseudoElement) {\n  const match = pseudoElement.match(/::view-transition-(old|new|group|image-pair)\\((.*?)\\)/);\n  if (!match) return null;\n  return {\n    layer: match[2],\n    type: match[1]\n  };\n}\nexport { getLayerName };", "map": {"version": 3, "names": ["getLayerName", "pseudoElement", "match", "layer", "type"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/motion-dom/dist/es/view/utils/get-layer-name.mjs"], "sourcesContent": ["function getLayerName(pseudoElement) {\n    const match = pseudoElement.match(/::view-transition-(old|new|group|image-pair)\\((.*?)\\)/);\n    if (!match)\n        return null;\n    return { layer: match[2], type: match[1] };\n}\n\nexport { getLayerName };\n"], "mappings": "AAAA,SAASA,YAAYA,CAACC,aAAa,EAAE;EACjC,MAAMC,KAAK,GAAGD,aAAa,CAACC,KAAK,CAAC,uDAAuD,CAAC;EAC1F,IAAI,CAACA,KAAK,EACN,OAAO,IAAI;EACf,OAAO;IAAEC,KAAK,EAAED,KAAK,CAAC,CAAC,CAAC;IAAEE,IAAI,EAAEF,KAAK,CAAC,CAAC;EAAE,CAAC;AAC9C;AAEA,SAASF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}