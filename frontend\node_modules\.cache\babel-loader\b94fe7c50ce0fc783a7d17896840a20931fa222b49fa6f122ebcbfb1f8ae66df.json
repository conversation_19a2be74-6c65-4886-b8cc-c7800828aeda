{"ast": null, "code": "import _formatErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nimport deepmerge from '@mui/utils/deepmerge';\nimport styleFunctionSx, { unstable_defaultSxConfig as defaultSxConfig } from '@mui/system/styleFunctionSx';\nimport systemCreateTheme from '@mui/system/createTheme';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport createMixins from \"./createMixins.js\";\nimport createPalette from \"./createPalette.js\";\nimport createTypography from \"./createTypography.js\";\nimport shadows from \"./shadows.js\";\nimport createTransitions from \"./createTransitions.js\";\nimport zIndex from \"./zIndex.js\";\nimport { stringifyTheme } from \"./stringifyTheme.js\";\nfunction createThemeNoVars(options = {}, ...args) {\n  const {\n    breakpoints: breakpointsInput,\n    mixins: mixinsInput = {},\n    spacing: spacingInput,\n    palette: paletteInput = {},\n    transitions: transitionsInput = {},\n    typography: typographyInput = {},\n    shape: shapeInput,\n    ...other\n  } = options;\n  if (options.vars &&\n  // The error should throw only for the root theme creation because user is not allowed to use a custom node `vars`.\n  // `generateThemeVars` is the closest identifier for checking that the `options` is a result of `createTheme` with CSS variables so that user can create new theme for nested ThemeProvider.\n  options.generateThemeVars === undefined) {\n    throw new Error(process.env.NODE_ENV !== \"production\" ? 'MUI: `vars` is a private field used for CSS variables support.\\n' +\n    // #host-reference\n    'Please use another name or follow the [docs](https://mui.com/material-ui/customization/css-theme-variables/usage/) to enable the feature.' : _formatErrorMessage(20));\n  }\n  const palette = createPalette(paletteInput);\n  const systemTheme = systemCreateTheme(options);\n  let muiTheme = deepmerge(systemTheme, {\n    mixins: createMixins(systemTheme.breakpoints, mixinsInput),\n    palette,\n    // Don't use [...shadows] until you've verified its transpiled code is not invoking the iterator protocol.\n    shadows: shadows.slice(),\n    typography: createTypography(palette, typographyInput),\n    transitions: createTransitions(transitionsInput),\n    zIndex: {\n      ...zIndex\n    }\n  });\n  muiTheme = deepmerge(muiTheme, other);\n  muiTheme = args.reduce((acc, argument) => deepmerge(acc, argument), muiTheme);\n  if (process.env.NODE_ENV !== 'production') {\n    // TODO v6: Refactor to use globalStateClassesMapping from @mui/utils once `readOnly` state class is used in Rating component.\n    const stateClasses = ['active', 'checked', 'completed', 'disabled', 'error', 'expanded', 'focused', 'focusVisible', 'required', 'selected'];\n    const traverse = (node, component) => {\n      let key;\n\n      // eslint-disable-next-line guard-for-in\n      for (key in node) {\n        const child = node[key];\n        if (stateClasses.includes(key) && Object.keys(child).length > 0) {\n          if (process.env.NODE_ENV !== 'production') {\n            const stateClass = generateUtilityClass('', key);\n            console.error([`MUI: The \\`${component}\\` component increases ` + `the CSS specificity of the \\`${key}\\` internal state.`, 'You can not override it like this: ', JSON.stringify(node, null, 2), '', `Instead, you need to use the '&.${stateClass}' syntax:`, JSON.stringify({\n              root: {\n                [`&.${stateClass}`]: child\n              }\n            }, null, 2), '', 'https://mui.com/r/state-classes-guide'].join('\\n'));\n          }\n          // Remove the style to prevent global conflicts.\n          node[key] = {};\n        }\n      }\n    };\n    Object.keys(muiTheme.components).forEach(component => {\n      const styleOverrides = muiTheme.components[component].styleOverrides;\n      if (styleOverrides && component.startsWith('Mui')) {\n        traverse(styleOverrides, component);\n      }\n    });\n  }\n  muiTheme.unstable_sxConfig = {\n    ...defaultSxConfig,\n    ...other?.unstable_sxConfig\n  };\n  muiTheme.unstable_sx = function sx(props) {\n    return styleFunctionSx({\n      sx: props,\n      theme: this\n    });\n  };\n  muiTheme.toRuntimeSource = stringifyTheme; // for Pigment CSS integration\n\n  return muiTheme;\n}\nexport default createThemeNoVars;", "map": {"version": 3, "names": ["_formatErrorMessage", "deepmerge", "styleFunctionSx", "unstable_defaultSxConfig", "defaultSxConfig", "systemCreateTheme", "generateUtilityClass", "createMixins", "createPalette", "createTypography", "shadows", "createTransitions", "zIndex", "stringifyTheme", "createThemeNoVars", "options", "args", "breakpoints", "breakpointsInput", "mixins", "mixinsInput", "spacing", "spacingInput", "palette", "paletteInput", "transitions", "transitionsInput", "typography", "typographyInput", "shape", "shapeInput", "other", "vars", "generateThemeVars", "undefined", "Error", "process", "env", "NODE_ENV", "systemTheme", "muiTheme", "slice", "reduce", "acc", "argument", "stateClasses", "traverse", "node", "component", "key", "child", "includes", "Object", "keys", "length", "stateClass", "console", "error", "JSON", "stringify", "root", "join", "components", "for<PERSON>ach", "styleOverrides", "startsWith", "unstable_sxConfig", "unstable_sx", "sx", "props", "theme", "toRuntimeSource"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/@mui/material/esm/styles/createThemeNoVars.js"], "sourcesContent": ["import _formatErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nimport deepmerge from '@mui/utils/deepmerge';\nimport styleFunctionSx, { unstable_defaultSxConfig as defaultSxConfig } from '@mui/system/styleFunctionSx';\nimport systemCreateTheme from '@mui/system/createTheme';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport createMixins from \"./createMixins.js\";\nimport createPalette from \"./createPalette.js\";\nimport createTypography from \"./createTypography.js\";\nimport shadows from \"./shadows.js\";\nimport createTransitions from \"./createTransitions.js\";\nimport zIndex from \"./zIndex.js\";\nimport { stringifyTheme } from \"./stringifyTheme.js\";\nfunction createThemeNoVars(options = {}, ...args) {\n  const {\n    breakpoints: breakpointsInput,\n    mixins: mixinsInput = {},\n    spacing: spacingInput,\n    palette: paletteInput = {},\n    transitions: transitionsInput = {},\n    typography: typographyInput = {},\n    shape: shapeInput,\n    ...other\n  } = options;\n  if (options.vars &&\n  // The error should throw only for the root theme creation because user is not allowed to use a custom node `vars`.\n  // `generateThemeVars` is the closest identifier for checking that the `options` is a result of `createTheme` with CSS variables so that user can create new theme for nested ThemeProvider.\n  options.generateThemeVars === undefined) {\n    throw new Error(process.env.NODE_ENV !== \"production\" ? 'MUI: `vars` is a private field used for CSS variables support.\\n' +\n    // #host-reference\n    'Please use another name or follow the [docs](https://mui.com/material-ui/customization/css-theme-variables/usage/) to enable the feature.' : _formatErrorMessage(20));\n  }\n  const palette = createPalette(paletteInput);\n  const systemTheme = systemCreateTheme(options);\n  let muiTheme = deepmerge(systemTheme, {\n    mixins: createMixins(systemTheme.breakpoints, mixinsInput),\n    palette,\n    // Don't use [...shadows] until you've verified its transpiled code is not invoking the iterator protocol.\n    shadows: shadows.slice(),\n    typography: createTypography(palette, typographyInput),\n    transitions: createTransitions(transitionsInput),\n    zIndex: {\n      ...zIndex\n    }\n  });\n  muiTheme = deepmerge(muiTheme, other);\n  muiTheme = args.reduce((acc, argument) => deepmerge(acc, argument), muiTheme);\n  if (process.env.NODE_ENV !== 'production') {\n    // TODO v6: Refactor to use globalStateClassesMapping from @mui/utils once `readOnly` state class is used in Rating component.\n    const stateClasses = ['active', 'checked', 'completed', 'disabled', 'error', 'expanded', 'focused', 'focusVisible', 'required', 'selected'];\n    const traverse = (node, component) => {\n      let key;\n\n      // eslint-disable-next-line guard-for-in\n      for (key in node) {\n        const child = node[key];\n        if (stateClasses.includes(key) && Object.keys(child).length > 0) {\n          if (process.env.NODE_ENV !== 'production') {\n            const stateClass = generateUtilityClass('', key);\n            console.error([`MUI: The \\`${component}\\` component increases ` + `the CSS specificity of the \\`${key}\\` internal state.`, 'You can not override it like this: ', JSON.stringify(node, null, 2), '', `Instead, you need to use the '&.${stateClass}' syntax:`, JSON.stringify({\n              root: {\n                [`&.${stateClass}`]: child\n              }\n            }, null, 2), '', 'https://mui.com/r/state-classes-guide'].join('\\n'));\n          }\n          // Remove the style to prevent global conflicts.\n          node[key] = {};\n        }\n      }\n    };\n    Object.keys(muiTheme.components).forEach(component => {\n      const styleOverrides = muiTheme.components[component].styleOverrides;\n      if (styleOverrides && component.startsWith('Mui')) {\n        traverse(styleOverrides, component);\n      }\n    });\n  }\n  muiTheme.unstable_sxConfig = {\n    ...defaultSxConfig,\n    ...other?.unstable_sxConfig\n  };\n  muiTheme.unstable_sx = function sx(props) {\n    return styleFunctionSx({\n      sx: props,\n      theme: this\n    });\n  };\n  muiTheme.toRuntimeSource = stringifyTheme; // for Pigment CSS integration\n\n  return muiTheme;\n}\nexport default createThemeNoVars;"], "mappings": "AAAA,OAAOA,mBAAmB,MAAM,kCAAkC;AAClE,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,eAAe,IAAIC,wBAAwB,IAAIC,eAAe,QAAQ,6BAA6B;AAC1G,OAAOC,iBAAiB,MAAM,yBAAyB;AACvD,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAOC,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,gBAAgB,MAAM,uBAAuB;AACpD,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,iBAAiB,MAAM,wBAAwB;AACtD,OAAOC,MAAM,MAAM,aAAa;AAChC,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,iBAAiBA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE,GAAGC,IAAI,EAAE;EAChD,MAAM;IACJC,WAAW,EAAEC,gBAAgB;IAC7BC,MAAM,EAAEC,WAAW,GAAG,CAAC,CAAC;IACxBC,OAAO,EAAEC,YAAY;IACrBC,OAAO,EAAEC,YAAY,GAAG,CAAC,CAAC;IAC1BC,WAAW,EAAEC,gBAAgB,GAAG,CAAC,CAAC;IAClCC,UAAU,EAAEC,eAAe,GAAG,CAAC,CAAC;IAChCC,KAAK,EAAEC,UAAU;IACjB,GAAGC;EACL,CAAC,GAAGhB,OAAO;EACX,IAAIA,OAAO,CAACiB,IAAI;EAChB;EACA;EACAjB,OAAO,CAACkB,iBAAiB,KAAKC,SAAS,EAAE;IACvC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG,kEAAkE;IAC1H;IACA,2IAA2I,GAAGtC,mBAAmB,CAAC,EAAE,CAAC,CAAC;EACxK;EACA,MAAMuB,OAAO,GAAGf,aAAa,CAACgB,YAAY,CAAC;EAC3C,MAAMe,WAAW,GAAGlC,iBAAiB,CAACU,OAAO,CAAC;EAC9C,IAAIyB,QAAQ,GAAGvC,SAAS,CAACsC,WAAW,EAAE;IACpCpB,MAAM,EAAEZ,YAAY,CAACgC,WAAW,CAACtB,WAAW,EAAEG,WAAW,CAAC;IAC1DG,OAAO;IACP;IACAb,OAAO,EAAEA,OAAO,CAAC+B,KAAK,CAAC,CAAC;IACxBd,UAAU,EAAElB,gBAAgB,CAACc,OAAO,EAAEK,eAAe,CAAC;IACtDH,WAAW,EAAEd,iBAAiB,CAACe,gBAAgB,CAAC;IAChDd,MAAM,EAAE;MACN,GAAGA;IACL;EACF,CAAC,CAAC;EACF4B,QAAQ,GAAGvC,SAAS,CAACuC,QAAQ,EAAET,KAAK,CAAC;EACrCS,QAAQ,GAAGxB,IAAI,CAAC0B,MAAM,CAAC,CAACC,GAAG,EAAEC,QAAQ,KAAK3C,SAAS,CAAC0C,GAAG,EAAEC,QAAQ,CAAC,EAAEJ,QAAQ,CAAC;EAC7E,IAAIJ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACA,MAAMO,YAAY,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,cAAc,EAAE,UAAU,EAAE,UAAU,CAAC;IAC3I,MAAMC,QAAQ,GAAGA,CAACC,IAAI,EAAEC,SAAS,KAAK;MACpC,IAAIC,GAAG;;MAEP;MACA,KAAKA,GAAG,IAAIF,IAAI,EAAE;QAChB,MAAMG,KAAK,GAAGH,IAAI,CAACE,GAAG,CAAC;QACvB,IAAIJ,YAAY,CAACM,QAAQ,CAACF,GAAG,CAAC,IAAIG,MAAM,CAACC,IAAI,CAACH,KAAK,CAAC,CAACI,MAAM,GAAG,CAAC,EAAE;UAC/D,IAAIlB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;YACzC,MAAMiB,UAAU,GAAGjD,oBAAoB,CAAC,EAAE,EAAE2C,GAAG,CAAC;YAChDO,OAAO,CAACC,KAAK,CAAC,CAAC,cAAcT,SAAS,yBAAyB,GAAG,gCAAgCC,GAAG,oBAAoB,EAAE,qCAAqC,EAAES,IAAI,CAACC,SAAS,CAACZ,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,mCAAmCQ,UAAU,WAAW,EAAEG,IAAI,CAACC,SAAS,CAAC;cAC5QC,IAAI,EAAE;gBACJ,CAAC,KAAKL,UAAU,EAAE,GAAGL;cACvB;YACF,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,uCAAuC,CAAC,CAACW,IAAI,CAAC,IAAI,CAAC,CAAC;UACvE;UACA;UACAd,IAAI,CAACE,GAAG,CAAC,GAAG,CAAC,CAAC;QAChB;MACF;IACF,CAAC;IACDG,MAAM,CAACC,IAAI,CAACb,QAAQ,CAACsB,UAAU,CAAC,CAACC,OAAO,CAACf,SAAS,IAAI;MACpD,MAAMgB,cAAc,GAAGxB,QAAQ,CAACsB,UAAU,CAACd,SAAS,CAAC,CAACgB,cAAc;MACpE,IAAIA,cAAc,IAAIhB,SAAS,CAACiB,UAAU,CAAC,KAAK,CAAC,EAAE;QACjDnB,QAAQ,CAACkB,cAAc,EAAEhB,SAAS,CAAC;MACrC;IACF,CAAC,CAAC;EACJ;EACAR,QAAQ,CAAC0B,iBAAiB,GAAG;IAC3B,GAAG9D,eAAe;IAClB,GAAG2B,KAAK,EAAEmC;EACZ,CAAC;EACD1B,QAAQ,CAAC2B,WAAW,GAAG,SAASC,EAAEA,CAACC,KAAK,EAAE;IACxC,OAAOnE,eAAe,CAAC;MACrBkE,EAAE,EAAEC,KAAK;MACTC,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC;EACD9B,QAAQ,CAAC+B,eAAe,GAAG1D,cAAc,CAAC,CAAC;;EAE3C,OAAO2B,QAAQ;AACjB;AACA,eAAe1B,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}