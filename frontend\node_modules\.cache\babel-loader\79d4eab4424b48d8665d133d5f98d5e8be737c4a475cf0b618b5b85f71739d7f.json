{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport rootShouldForwardProp from \"../styles/rootShouldForwardProp.js\";\nimport capitalize from \"../utils/capitalize.js\";\nimport { isHorizontal } from \"../Drawer/Drawer.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst SwipeAreaRoot = styled('div', {\n  shouldForwardProp: rootShouldForwardProp\n})(memoTheme(({\n  theme\n}) => ({\n  position: 'fixed',\n  top: 0,\n  left: 0,\n  bottom: 0,\n  zIndex: theme.zIndex.drawer - 1,\n  variants: [{\n    props: {\n      anchor: 'left'\n    },\n    style: {\n      right: 'auto'\n    }\n  }, {\n    props: {\n      anchor: 'right'\n    },\n    style: {\n      left: 'auto',\n      right: 0\n    }\n  }, {\n    props: {\n      anchor: 'top'\n    },\n    style: {\n      bottom: 'auto',\n      right: 0\n    }\n  }, {\n    props: {\n      anchor: 'bottom'\n    },\n    style: {\n      top: 'auto',\n      bottom: 0,\n      right: 0\n    }\n  }]\n})));\n\n/**\n * @ignore - internal component.\n */\nconst SwipeArea = /*#__PURE__*/React.forwardRef(function SwipeArea(props, ref) {\n  const {\n    anchor,\n    classes = {},\n    className,\n    width,\n    style,\n    ...other\n  } = props;\n  const ownerState = props;\n  return /*#__PURE__*/_jsx(SwipeAreaRoot, {\n    className: clsx('PrivateSwipeArea-root', classes.root, classes[`anchor${capitalize(anchor)}`], className),\n    ref: ref,\n    style: {\n      [isHorizontal(anchor) ? 'width' : 'height']: width,\n      ...style\n    },\n    ownerState: ownerState,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? SwipeArea.propTypes = {\n  /**\n   * Side on which to attach the discovery area.\n   */\n  anchor: PropTypes.oneOf(['left', 'top', 'right', 'bottom']).isRequired,\n  /**\n   * @ignore\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The width of the left most (or right most) area in `px` where the\n   * drawer can be swiped open from.\n   */\n  width: PropTypes.number.isRequired\n} : void 0;\nexport default SwipeArea;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "styled", "memoTheme", "rootShouldForwardProp", "capitalize", "isHorizontal", "jsx", "_jsx", "SwipeAreaRoot", "shouldForwardProp", "theme", "position", "top", "left", "bottom", "zIndex", "drawer", "variants", "props", "anchor", "style", "right", "SwipeArea", "forwardRef", "ref", "classes", "className", "width", "other", "ownerState", "root", "process", "env", "NODE_ENV", "propTypes", "oneOf", "isRequired", "object", "string", "number"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/@mui/material/esm/SwipeableDrawer/SwipeArea.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport rootShouldForwardProp from \"../styles/rootShouldForwardProp.js\";\nimport capitalize from \"../utils/capitalize.js\";\nimport { isHorizontal } from \"../Drawer/Drawer.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst SwipeAreaRoot = styled('div', {\n  shouldForwardProp: rootShouldForwardProp\n})(memoTheme(({\n  theme\n}) => ({\n  position: 'fixed',\n  top: 0,\n  left: 0,\n  bottom: 0,\n  zIndex: theme.zIndex.drawer - 1,\n  variants: [{\n    props: {\n      anchor: 'left'\n    },\n    style: {\n      right: 'auto'\n    }\n  }, {\n    props: {\n      anchor: 'right'\n    },\n    style: {\n      left: 'auto',\n      right: 0\n    }\n  }, {\n    props: {\n      anchor: 'top'\n    },\n    style: {\n      bottom: 'auto',\n      right: 0\n    }\n  }, {\n    props: {\n      anchor: 'bottom'\n    },\n    style: {\n      top: 'auto',\n      bottom: 0,\n      right: 0\n    }\n  }]\n})));\n\n/**\n * @ignore - internal component.\n */\nconst SwipeArea = /*#__PURE__*/React.forwardRef(function SwipeArea(props, ref) {\n  const {\n    anchor,\n    classes = {},\n    className,\n    width,\n    style,\n    ...other\n  } = props;\n  const ownerState = props;\n  return /*#__PURE__*/_jsx(SwipeAreaRoot, {\n    className: clsx('PrivateSwipeArea-root', classes.root, classes[`anchor${capitalize(anchor)}`], className),\n    ref: ref,\n    style: {\n      [isHorizontal(anchor) ? 'width' : 'height']: width,\n      ...style\n    },\n    ownerState: ownerState,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? SwipeArea.propTypes = {\n  /**\n   * Side on which to attach the discovery area.\n   */\n  anchor: PropTypes.oneOf(['left', 'top', 'right', 'bottom']).isRequired,\n  /**\n   * @ignore\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The width of the left most (or right most) area in `px` where the\n   * drawer can be swiped open from.\n   */\n  width: PropTypes.number.isRequired\n} : void 0;\nexport default SwipeArea;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,qBAAqB,MAAM,oCAAoC;AACtE,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,SAASC,YAAY,QAAQ,qBAAqB;AAClD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,aAAa,GAAGP,MAAM,CAAC,KAAK,EAAE;EAClCQ,iBAAiB,EAAEN;AACrB,CAAC,CAAC,CAACD,SAAS,CAAC,CAAC;EACZQ;AACF,CAAC,MAAM;EACLC,QAAQ,EAAE,OAAO;EACjBC,GAAG,EAAE,CAAC;EACNC,IAAI,EAAE,CAAC;EACPC,MAAM,EAAE,CAAC;EACTC,MAAM,EAAEL,KAAK,CAACK,MAAM,CAACC,MAAM,GAAG,CAAC;EAC/BC,QAAQ,EAAE,CAAC;IACTC,KAAK,EAAE;MACLC,MAAM,EAAE;IACV,CAAC;IACDC,KAAK,EAAE;MACLC,KAAK,EAAE;IACT;EACF,CAAC,EAAE;IACDH,KAAK,EAAE;MACLC,MAAM,EAAE;IACV,CAAC;IACDC,KAAK,EAAE;MACLP,IAAI,EAAE,MAAM;MACZQ,KAAK,EAAE;IACT;EACF,CAAC,EAAE;IACDH,KAAK,EAAE;MACLC,MAAM,EAAE;IACV,CAAC;IACDC,KAAK,EAAE;MACLN,MAAM,EAAE,MAAM;MACdO,KAAK,EAAE;IACT;EACF,CAAC,EAAE;IACDH,KAAK,EAAE;MACLC,MAAM,EAAE;IACV,CAAC;IACDC,KAAK,EAAE;MACLR,GAAG,EAAE,MAAM;MACXE,MAAM,EAAE,CAAC;MACTO,KAAK,EAAE;IACT;EACF,CAAC;AACH,CAAC,CAAC,CAAC,CAAC;;AAEJ;AACA;AACA;AACA,MAAMC,SAAS,GAAG,aAAaxB,KAAK,CAACyB,UAAU,CAAC,SAASD,SAASA,CAACJ,KAAK,EAAEM,GAAG,EAAE;EAC7E,MAAM;IACJL,MAAM;IACNM,OAAO,GAAG,CAAC,CAAC;IACZC,SAAS;IACTC,KAAK;IACLP,KAAK;IACL,GAAGQ;EACL,CAAC,GAAGV,KAAK;EACT,MAAMW,UAAU,GAAGX,KAAK;EACxB,OAAO,aAAaX,IAAI,CAACC,aAAa,EAAE;IACtCkB,SAAS,EAAE1B,IAAI,CAAC,uBAAuB,EAAEyB,OAAO,CAACK,IAAI,EAAEL,OAAO,CAAC,SAASrB,UAAU,CAACe,MAAM,CAAC,EAAE,CAAC,EAAEO,SAAS,CAAC;IACzGF,GAAG,EAAEA,GAAG;IACRJ,KAAK,EAAE;MACL,CAACf,YAAY,CAACc,MAAM,CAAC,GAAG,OAAO,GAAG,QAAQ,GAAGQ,KAAK;MAClD,GAAGP;IACL,CAAC;IACDS,UAAU,EAAEA,UAAU;IACtB,GAAGD;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGX,SAAS,CAACY,SAAS,GAAG;EAC5D;AACF;AACA;EACEf,MAAM,EAAEpB,SAAS,CAACoC,KAAK,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC,CAACC,UAAU;EACtE;AACF;AACA;EACEX,OAAO,EAAE1B,SAAS,CAACsC,MAAM;EACzB;AACF;AACA;EACEX,SAAS,EAAE3B,SAAS,CAACuC,MAAM;EAC3B;AACF;AACA;EACElB,KAAK,EAAErB,SAAS,CAACsC,MAAM;EACvB;AACF;AACA;AACA;EACEV,KAAK,EAAE5B,SAAS,CAACwC,MAAM,CAACH;AAC1B,CAAC,GAAG,KAAK,CAAC;AACV,eAAed,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}