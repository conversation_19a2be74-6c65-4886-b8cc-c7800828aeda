{"ast": null, "code": "import * as React from 'react';\n\n/**\n * Returns the ref of a React node handling differences between React 19 and older versions.\n * It will return null if the node is not a valid React element.\n *\n * @param element React.ReactNode\n * @returns React.Ref<any> | null\n *\n * @deprecated Use getReactElementRef instead\n */\nexport default function getReactNodeRef(element) {\n  if (!element || ! /*#__PURE__*/React.isValidElement(element)) {\n    return null;\n  }\n\n  // 'ref' is passed as prop in React 19, whereas 'ref' is directly attached to children in older versions\n  return element.props.propertyIsEnumerable('ref') ? element.props.ref :\n  // @ts-expect-error element.ref is not included in the ReactElement type\n  // We cannot check for it, but isValidElement is true at this point\n  // https://github.com/DefinitelyTyped/DefinitelyTyped/discussions/70189\n  element.ref;\n}", "map": {"version": 3, "names": ["React", "getReactNodeRef", "element", "isValidElement", "props", "propertyIsEnumerable", "ref"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/@mui/utils/esm/getReactNodeRef/getReactNodeRef.js"], "sourcesContent": ["import * as React from 'react';\n\n/**\n * Returns the ref of a React node handling differences between React 19 and older versions.\n * It will return null if the node is not a valid React element.\n *\n * @param element React.ReactNode\n * @returns React.Ref<any> | null\n *\n * @deprecated Use getReactElementRef instead\n */\nexport default function getReactNodeRef(element) {\n  if (!element || ! /*#__PURE__*/React.isValidElement(element)) {\n    return null;\n  }\n\n  // 'ref' is passed as prop in React 19, whereas 'ref' is directly attached to children in older versions\n  return element.props.propertyIsEnumerable('ref') ? element.props.ref :\n  // @ts-expect-error element.ref is not included in the ReactElement type\n  // We cannot check for it, but isValidElement is true at this point\n  // https://github.com/DefinitelyTyped/DefinitelyTyped/discussions/70189\n  element.ref;\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,eAAeA,CAACC,OAAO,EAAE;EAC/C,IAAI,CAACA,OAAO,IAAI,EAAE,aAAaF,KAAK,CAACG,cAAc,CAACD,OAAO,CAAC,EAAE;IAC5D,OAAO,IAAI;EACb;;EAEA;EACA,OAAOA,OAAO,CAACE,KAAK,CAACC,oBAAoB,CAAC,KAAK,CAAC,GAAGH,OAAO,CAACE,KAAK,CAACE,GAAG;EACpE;EACA;EACA;EACAJ,OAAO,CAACI,GAAG;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}