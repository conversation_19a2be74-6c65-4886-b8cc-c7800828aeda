{"ast": null, "code": "import { invariant, millisecondsToSeconds, secondsToMilliseconds, noop } from 'motion-utils';\nimport { style } from '../render/dom/style.mjs';\nimport { getFinalKeyframe } from './keyframes/get-final.mjs';\nimport { hydrateKeyframes } from './keyframes/hydrate.mjs';\nimport { startWaapiAnimation } from './waapi/start-waapi-animation.mjs';\nimport { applyGeneratorOptions } from './waapi/utils/apply-generator.mjs';\nconst animationMaps = new WeakMap();\nconst animationMapKey = (name, pseudoElement) => `${name}:${pseudoElement}`;\nfunction getAnimationMap(element) {\n  const map = animationMaps.get(element) || new Map();\n  animationMaps.set(element, map);\n  return map;\n}\n/**\n * NativeAnimation implements AnimationPlaybackControls for the browser's Web Animations API.\n */\nclass NativeAnimation {\n  constructor(options) {\n    /**\n     * If we already have an animation, we don't need to instantiate one\n     * and can just use this as a controls interface.\n     */\n    if (\"animation\" in options) {\n      this.animation = options.animation;\n      return;\n    }\n    const {\n      element,\n      name,\n      keyframes: unresolvedKeyframes,\n      pseudoElement,\n      allowFlatten = false\n    } = options;\n    let {\n      transition\n    } = options;\n    this.allowFlatten = allowFlatten;\n    /**\n     * Stop any existing animations on the element before reading existing keyframes.\n     *\n     * TODO: Check for VisualElement before using animation state. This is a fallback\n     * for mini animate(). Do this when implementing NativeAnimationExtended.\n     */\n    const animationMap = getAnimationMap(element);\n    const key = animationMapKey(name, pseudoElement || \"\");\n    const currentAnimation = animationMap.get(key);\n    currentAnimation && currentAnimation.stop();\n    /**\n     * TODO: If these keyframes aren't correctly hydrated then we want to throw\n     * run an instant animation.\n     */\n    const keyframes = hydrateKeyframes(element, name, unresolvedKeyframes, pseudoElement);\n    invariant(typeof transition.type !== \"string\", `animateMini doesn't support \"type\" as a string. Did you mean to import { spring } from \"motion\"?`);\n    transition = applyGeneratorOptions(transition);\n    this.animation = startWaapiAnimation(element, name, keyframes, transition, pseudoElement);\n    if (transition.autoplay === false) {\n      this.animation.pause();\n    }\n    this.removeAnimation = () => animationMap.delete(key);\n    this.animation.onfinish = () => {\n      if (!pseudoElement) {\n        style.set(element, name, getFinalKeyframe(keyframes, transition));\n      } else {\n        this.commitStyles();\n      }\n      this.cancel();\n    };\n    /**\n     * TODO: Check for VisualElement before using animation state.\n     */\n    animationMap.set(key, this);\n  }\n  play() {\n    this.animation.play();\n  }\n  pause() {\n    this.animation.pause();\n  }\n  complete() {\n    this.animation.finish();\n  }\n  cancel() {\n    try {\n      this.animation.cancel();\n    } catch (e) {}\n    this.removeAnimation();\n  }\n  stop() {\n    const {\n      state\n    } = this;\n    if (state === \"idle\" || state === \"finished\") {\n      return;\n    }\n    this.commitStyles();\n    this.cancel();\n  }\n  /**\n   * WAAPI doesn't natively have any interruption capabilities.\n   *\n   * In this method, we commit styles back to the DOM before cancelling\n   * the animation.\n   *\n   * This is designed to be overridden by NativeAnimationExtended, which\n   * will create a renderless JS animation and sample it twice to calculate\n   * its current value, \"previous\" value, and therefore allow\n   * Motion to also correctly calculate velocity for any subsequent animation\n   * while deferring the commit until the next animation frame.\n   */\n  commitStyles() {\n    this.animation.commitStyles?.();\n  }\n  get duration() {\n    console.log(this.animation.effect?.getComputedTiming());\n    const duration = this.animation.effect?.getComputedTiming().duration || 0;\n    return millisecondsToSeconds(Number(duration));\n  }\n  get time() {\n    return millisecondsToSeconds(Number(this.animation.currentTime) || 0);\n  }\n  set time(newTime) {\n    this.animation.currentTime = secondsToMilliseconds(newTime);\n  }\n  /**\n   * The playback speed of the animation.\n   * 1 = normal speed, 2 = double speed, 0.5 = half speed.\n   */\n  get speed() {\n    return this.animation.playbackRate;\n  }\n  set speed(newSpeed) {\n    this.animation.playbackRate = newSpeed;\n  }\n  get state() {\n    return this.animation.playState;\n  }\n  get startTime() {\n    return Number(this.animation.startTime);\n  }\n  get finished() {\n    return this.animation.finished;\n  }\n  flatten() {\n    if (this.allowFlatten) {\n      this.animation.effect?.updateTiming({\n        easing: \"linear\"\n      });\n    }\n  }\n  /**\n   * Attaches a timeline to the animation, for instance the `ScrollTimeline`.\n   */\n  attachTimeline(timeline) {\n    this.animation.timeline = timeline;\n    this.animation.onfinish = null;\n    return noop;\n  }\n  /**\n   * Allows the animation to be awaited.\n   *\n   * @deprecated Use `finished` instead.\n   */\n  then(onResolve, onReject) {\n    return this.finished.then(onResolve).catch(onReject);\n  }\n}\nexport { NativeAnimation };", "map": {"version": 3, "names": ["invariant", "millisecondsToSeconds", "secondsToMilliseconds", "noop", "style", "getFinalKeyframe", "hydrateKeyframes", "startWaapiAnimation", "applyGeneratorOptions", "animationMaps", "WeakMap", "animationMapKey", "name", "pseudoElement", "getAnimationMap", "element", "map", "get", "Map", "set", "NativeAnimation", "constructor", "options", "animation", "keyframes", "unresolvedKeyframes", "allowFlatten", "transition", "animationMap", "key", "currentAnimation", "stop", "type", "autoplay", "pause", "removeAnimation", "delete", "onfinish", "commitStyles", "cancel", "play", "complete", "finish", "e", "state", "duration", "console", "log", "effect", "getComputedTiming", "Number", "time", "currentTime", "newTime", "speed", "playbackRate", "newSpeed", "playState", "startTime", "finished", "flatten", "updateTiming", "easing", "attachTimeline", "timeline", "then", "onResolve", "onReject", "catch"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/motion-dom/dist/es/animation/NativeAnimation.mjs"], "sourcesContent": ["import { invariant, millisecondsToSeconds, secondsToMilliseconds, noop } from 'motion-utils';\nimport { style } from '../render/dom/style.mjs';\nimport { getFinalKeyframe } from './keyframes/get-final.mjs';\nimport { hydrateKeyframes } from './keyframes/hydrate.mjs';\nimport { startWaapiAnimation } from './waapi/start-waapi-animation.mjs';\nimport { applyGeneratorOptions } from './waapi/utils/apply-generator.mjs';\n\nconst animationMaps = new WeakMap();\nconst animationMapKey = (name, pseudoElement) => `${name}:${pseudoElement}`;\nfunction getAnimationMap(element) {\n    const map = animationMaps.get(element) || new Map();\n    animationMaps.set(element, map);\n    return map;\n}\n/**\n * NativeAnimation implements AnimationPlaybackControls for the browser's Web Animations API.\n */\nclass NativeAnimation {\n    constructor(options) {\n        /**\n         * If we already have an animation, we don't need to instantiate one\n         * and can just use this as a controls interface.\n         */\n        if (\"animation\" in options) {\n            this.animation = options.animation;\n            return;\n        }\n        const { element, name, keyframes: unresolvedKeyframes, pseudoElement, allowFlatten = false, } = options;\n        let { transition } = options;\n        this.allowFlatten = allowFlatten;\n        /**\n         * Stop any existing animations on the element before reading existing keyframes.\n         *\n         * TODO: Check for VisualElement before using animation state. This is a fallback\n         * for mini animate(). Do this when implementing NativeAnimationExtended.\n         */\n        const animationMap = getAnimationMap(element);\n        const key = animationMapKey(name, pseudoElement || \"\");\n        const currentAnimation = animationMap.get(key);\n        currentAnimation && currentAnimation.stop();\n        /**\n         * TODO: If these keyframes aren't correctly hydrated then we want to throw\n         * run an instant animation.\n         */\n        const keyframes = hydrateKeyframes(element, name, unresolvedKeyframes, pseudoElement);\n        invariant(typeof transition.type !== \"string\", `animateMini doesn't support \"type\" as a string. Did you mean to import { spring } from \"motion\"?`);\n        transition = applyGeneratorOptions(transition);\n        this.animation = startWaapiAnimation(element, name, keyframes, transition, pseudoElement);\n        if (transition.autoplay === false) {\n            this.animation.pause();\n        }\n        this.removeAnimation = () => animationMap.delete(key);\n        this.animation.onfinish = () => {\n            if (!pseudoElement) {\n                style.set(element, name, getFinalKeyframe(keyframes, transition));\n            }\n            else {\n                this.commitStyles();\n            }\n            this.cancel();\n        };\n        /**\n         * TODO: Check for VisualElement before using animation state.\n         */\n        animationMap.set(key, this);\n    }\n    play() {\n        this.animation.play();\n    }\n    pause() {\n        this.animation.pause();\n    }\n    complete() {\n        this.animation.finish();\n    }\n    cancel() {\n        try {\n            this.animation.cancel();\n        }\n        catch (e) { }\n        this.removeAnimation();\n    }\n    stop() {\n        const { state } = this;\n        if (state === \"idle\" || state === \"finished\") {\n            return;\n        }\n        this.commitStyles();\n        this.cancel();\n    }\n    /**\n     * WAAPI doesn't natively have any interruption capabilities.\n     *\n     * In this method, we commit styles back to the DOM before cancelling\n     * the animation.\n     *\n     * This is designed to be overridden by NativeAnimationExtended, which\n     * will create a renderless JS animation and sample it twice to calculate\n     * its current value, \"previous\" value, and therefore allow\n     * Motion to also correctly calculate velocity for any subsequent animation\n     * while deferring the commit until the next animation frame.\n     */\n    commitStyles() {\n        this.animation.commitStyles?.();\n    }\n    get duration() {\n        console.log(this.animation.effect?.getComputedTiming());\n        const duration = this.animation.effect?.getComputedTiming().duration || 0;\n        return millisecondsToSeconds(Number(duration));\n    }\n    get time() {\n        return millisecondsToSeconds(Number(this.animation.currentTime) || 0);\n    }\n    set time(newTime) {\n        this.animation.currentTime = secondsToMilliseconds(newTime);\n    }\n    /**\n     * The playback speed of the animation.\n     * 1 = normal speed, 2 = double speed, 0.5 = half speed.\n     */\n    get speed() {\n        return this.animation.playbackRate;\n    }\n    set speed(newSpeed) {\n        this.animation.playbackRate = newSpeed;\n    }\n    get state() {\n        return this.animation.playState;\n    }\n    get startTime() {\n        return Number(this.animation.startTime);\n    }\n    get finished() {\n        return this.animation.finished;\n    }\n    flatten() {\n        if (this.allowFlatten) {\n            this.animation.effect?.updateTiming({ easing: \"linear\" });\n        }\n    }\n    /**\n     * Attaches a timeline to the animation, for instance the `ScrollTimeline`.\n     */\n    attachTimeline(timeline) {\n        this.animation.timeline = timeline;\n        this.animation.onfinish = null;\n        return noop;\n    }\n    /**\n     * Allows the animation to be awaited.\n     *\n     * @deprecated Use `finished` instead.\n     */\n    then(onResolve, onReject) {\n        return this.finished.then(onResolve).catch(onReject);\n    }\n}\n\nexport { NativeAnimation };\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,qBAAqB,EAAEC,qBAAqB,EAAEC,IAAI,QAAQ,cAAc;AAC5F,SAASC,KAAK,QAAQ,yBAAyB;AAC/C,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,gBAAgB,QAAQ,yBAAyB;AAC1D,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,qBAAqB,QAAQ,mCAAmC;AAEzE,MAAMC,aAAa,GAAG,IAAIC,OAAO,CAAC,CAAC;AACnC,MAAMC,eAAe,GAAGA,CAACC,IAAI,EAAEC,aAAa,KAAK,GAAGD,IAAI,IAAIC,aAAa,EAAE;AAC3E,SAASC,eAAeA,CAACC,OAAO,EAAE;EAC9B,MAAMC,GAAG,GAAGP,aAAa,CAACQ,GAAG,CAACF,OAAO,CAAC,IAAI,IAAIG,GAAG,CAAC,CAAC;EACnDT,aAAa,CAACU,GAAG,CAACJ,OAAO,EAAEC,GAAG,CAAC;EAC/B,OAAOA,GAAG;AACd;AACA;AACA;AACA;AACA,MAAMI,eAAe,CAAC;EAClBC,WAAWA,CAACC,OAAO,EAAE;IACjB;AACR;AACA;AACA;IACQ,IAAI,WAAW,IAAIA,OAAO,EAAE;MACxB,IAAI,CAACC,SAAS,GAAGD,OAAO,CAACC,SAAS;MAClC;IACJ;IACA,MAAM;MAAER,OAAO;MAAEH,IAAI;MAAEY,SAAS,EAAEC,mBAAmB;MAAEZ,aAAa;MAAEa,YAAY,GAAG;IAAO,CAAC,GAAGJ,OAAO;IACvG,IAAI;MAAEK;IAAW,CAAC,GAAGL,OAAO;IAC5B,IAAI,CAACI,YAAY,GAAGA,YAAY;IAChC;AACR;AACA;AACA;AACA;AACA;IACQ,MAAME,YAAY,GAAGd,eAAe,CAACC,OAAO,CAAC;IAC7C,MAAMc,GAAG,GAAGlB,eAAe,CAACC,IAAI,EAAEC,aAAa,IAAI,EAAE,CAAC;IACtD,MAAMiB,gBAAgB,GAAGF,YAAY,CAACX,GAAG,CAACY,GAAG,CAAC;IAC9CC,gBAAgB,IAAIA,gBAAgB,CAACC,IAAI,CAAC,CAAC;IAC3C;AACR;AACA;AACA;IACQ,MAAMP,SAAS,GAAGlB,gBAAgB,CAACS,OAAO,EAAEH,IAAI,EAAEa,mBAAmB,EAAEZ,aAAa,CAAC;IACrFb,SAAS,CAAC,OAAO2B,UAAU,CAACK,IAAI,KAAK,QAAQ,EAAE,kGAAkG,CAAC;IAClJL,UAAU,GAAGnB,qBAAqB,CAACmB,UAAU,CAAC;IAC9C,IAAI,CAACJ,SAAS,GAAGhB,mBAAmB,CAACQ,OAAO,EAAEH,IAAI,EAAEY,SAAS,EAAEG,UAAU,EAAEd,aAAa,CAAC;IACzF,IAAIc,UAAU,CAACM,QAAQ,KAAK,KAAK,EAAE;MAC/B,IAAI,CAACV,SAAS,CAACW,KAAK,CAAC,CAAC;IAC1B;IACA,IAAI,CAACC,eAAe,GAAG,MAAMP,YAAY,CAACQ,MAAM,CAACP,GAAG,CAAC;IACrD,IAAI,CAACN,SAAS,CAACc,QAAQ,GAAG,MAAM;MAC5B,IAAI,CAACxB,aAAa,EAAE;QAChBT,KAAK,CAACe,GAAG,CAACJ,OAAO,EAAEH,IAAI,EAAEP,gBAAgB,CAACmB,SAAS,EAAEG,UAAU,CAAC,CAAC;MACrE,CAAC,MACI;QACD,IAAI,CAACW,YAAY,CAAC,CAAC;MACvB;MACA,IAAI,CAACC,MAAM,CAAC,CAAC;IACjB,CAAC;IACD;AACR;AACA;IACQX,YAAY,CAACT,GAAG,CAACU,GAAG,EAAE,IAAI,CAAC;EAC/B;EACAW,IAAIA,CAAA,EAAG;IACH,IAAI,CAACjB,SAAS,CAACiB,IAAI,CAAC,CAAC;EACzB;EACAN,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACX,SAAS,CAACW,KAAK,CAAC,CAAC;EAC1B;EACAO,QAAQA,CAAA,EAAG;IACP,IAAI,CAAClB,SAAS,CAACmB,MAAM,CAAC,CAAC;EAC3B;EACAH,MAAMA,CAAA,EAAG;IACL,IAAI;MACA,IAAI,CAAChB,SAAS,CAACgB,MAAM,CAAC,CAAC;IAC3B,CAAC,CACD,OAAOI,CAAC,EAAE,CAAE;IACZ,IAAI,CAACR,eAAe,CAAC,CAAC;EAC1B;EACAJ,IAAIA,CAAA,EAAG;IACH,MAAM;MAAEa;IAAM,CAAC,GAAG,IAAI;IACtB,IAAIA,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,UAAU,EAAE;MAC1C;IACJ;IACA,IAAI,CAACN,YAAY,CAAC,CAAC;IACnB,IAAI,CAACC,MAAM,CAAC,CAAC;EACjB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACID,YAAYA,CAAA,EAAG;IACX,IAAI,CAACf,SAAS,CAACe,YAAY,GAAG,CAAC;EACnC;EACA,IAAIO,QAAQA,CAAA,EAAG;IACXC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACxB,SAAS,CAACyB,MAAM,EAAEC,iBAAiB,CAAC,CAAC,CAAC;IACvD,MAAMJ,QAAQ,GAAG,IAAI,CAACtB,SAAS,CAACyB,MAAM,EAAEC,iBAAiB,CAAC,CAAC,CAACJ,QAAQ,IAAI,CAAC;IACzE,OAAO5C,qBAAqB,CAACiD,MAAM,CAACL,QAAQ,CAAC,CAAC;EAClD;EACA,IAAIM,IAAIA,CAAA,EAAG;IACP,OAAOlD,qBAAqB,CAACiD,MAAM,CAAC,IAAI,CAAC3B,SAAS,CAAC6B,WAAW,CAAC,IAAI,CAAC,CAAC;EACzE;EACA,IAAID,IAAIA,CAACE,OAAO,EAAE;IACd,IAAI,CAAC9B,SAAS,CAAC6B,WAAW,GAAGlD,qBAAqB,CAACmD,OAAO,CAAC;EAC/D;EACA;AACJ;AACA;AACA;EACI,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC/B,SAAS,CAACgC,YAAY;EACtC;EACA,IAAID,KAAKA,CAACE,QAAQ,EAAE;IAChB,IAAI,CAACjC,SAAS,CAACgC,YAAY,GAAGC,QAAQ;EAC1C;EACA,IAAIZ,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACrB,SAAS,CAACkC,SAAS;EACnC;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAOR,MAAM,CAAC,IAAI,CAAC3B,SAAS,CAACmC,SAAS,CAAC;EAC3C;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACpC,SAAS,CAACoC,QAAQ;EAClC;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAAClC,YAAY,EAAE;MACnB,IAAI,CAACH,SAAS,CAACyB,MAAM,EAAEa,YAAY,CAAC;QAAEC,MAAM,EAAE;MAAS,CAAC,CAAC;IAC7D;EACJ;EACA;AACJ;AACA;EACIC,cAAcA,CAACC,QAAQ,EAAE;IACrB,IAAI,CAACzC,SAAS,CAACyC,QAAQ,GAAGA,QAAQ;IAClC,IAAI,CAACzC,SAAS,CAACc,QAAQ,GAAG,IAAI;IAC9B,OAAOlC,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACI8D,IAAIA,CAACC,SAAS,EAAEC,QAAQ,EAAE;IACtB,OAAO,IAAI,CAACR,QAAQ,CAACM,IAAI,CAACC,SAAS,CAAC,CAACE,KAAK,CAACD,QAAQ,CAAC;EACxD;AACJ;AAEA,SAAS/C,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}