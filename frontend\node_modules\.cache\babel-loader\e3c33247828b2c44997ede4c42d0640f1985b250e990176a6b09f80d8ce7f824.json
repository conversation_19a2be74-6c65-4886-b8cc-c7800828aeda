{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport clamp from '@mui/utils/clamp';\nimport visuallyHidden from '@mui/utils/visuallyHidden';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport isFocusVisible from '@mui/utils/isFocusVisible';\nimport { capitalize, useForkRef, useControlled, unstable_useId as useId } from \"../utils/index.js\";\nimport Star from \"../internal/svg-icons/Star.js\";\nimport StarBorder from \"../internal/svg-icons/StarBorder.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport slotShouldForwardProp from \"../styles/slotShouldForwardProp.js\";\nimport ratingClasses, { getRatingUtilityClass } from \"./ratingClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { createElement as _createElement } from \"react\";\nfunction getDecimalPrecision(num) {\n  const decimalPart = num.toString().split('.')[1];\n  return decimalPart ? decimalPart.length : 0;\n}\nfunction roundValueToPrecision(value, precision) {\n  if (value == null) {\n    return value;\n  }\n  const nearest = Math.round(value / precision) * precision;\n  return Number(nearest.toFixed(getDecimalPrecision(precision)));\n}\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    size,\n    readOnly,\n    disabled,\n    emptyValueFocused,\n    focusVisible\n  } = ownerState;\n  const slots = {\n    root: ['root', `size${capitalize(size)}`, disabled && 'disabled', focusVisible && 'focusVisible', readOnly && 'readOnly'],\n    label: ['label', 'pristine'],\n    labelEmptyValue: [emptyValueFocused && 'labelEmptyValueActive'],\n    icon: ['icon'],\n    iconEmpty: ['iconEmpty'],\n    iconFilled: ['iconFilled'],\n    iconHover: ['iconHover'],\n    iconFocus: ['iconFocus'],\n    iconActive: ['iconActive'],\n    decimal: ['decimal'],\n    visuallyHidden: ['visuallyHidden']\n  };\n  return composeClasses(slots, getRatingUtilityClass, classes);\n};\nconst RatingRoot = styled('span', {\n  name: 'MuiRating',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [`& .${ratingClasses.visuallyHidden}`]: styles.visuallyHidden\n    }, styles.root, styles[`size${capitalize(ownerState.size)}`], ownerState.readOnly && styles.readOnly];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  display: 'inline-flex',\n  // Required to position the pristine input absolutely\n  position: 'relative',\n  fontSize: theme.typography.pxToRem(24),\n  color: '#faaf00',\n  cursor: 'pointer',\n  textAlign: 'left',\n  width: 'min-content',\n  WebkitTapHighlightColor: 'transparent',\n  [`&.${ratingClasses.disabled}`]: {\n    opacity: (theme.vars || theme).palette.action.disabledOpacity,\n    pointerEvents: 'none'\n  },\n  [`&.${ratingClasses.focusVisible} .${ratingClasses.iconActive}`]: {\n    outline: '1px solid #999'\n  },\n  [`& .${ratingClasses.visuallyHidden}`]: visuallyHidden,\n  variants: [{\n    props: {\n      size: 'small'\n    },\n    style: {\n      fontSize: theme.typography.pxToRem(18)\n    }\n  }, {\n    props: {\n      size: 'large'\n    },\n    style: {\n      fontSize: theme.typography.pxToRem(30)\n    }\n  }, {\n    // TODO v6: use the .Mui-readOnly global state class\n    props: ({\n      ownerState\n    }) => ownerState.readOnly,\n    style: {\n      pointerEvents: 'none'\n    }\n  }]\n})));\nconst RatingLabel = styled('label', {\n  name: 'MuiRating',\n  slot: 'Label',\n  overridesResolver: ({\n    ownerState\n  }, styles) => [styles.label, ownerState.emptyValueFocused && styles.labelEmptyValueActive]\n})({\n  cursor: 'inherit',\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.emptyValueFocused,\n    style: {\n      top: 0,\n      bottom: 0,\n      position: 'absolute',\n      outline: '1px solid #999',\n      width: '100%'\n    }\n  }]\n});\nconst RatingIcon = styled('span', {\n  name: 'MuiRating',\n  slot: 'Icon',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.icon, ownerState.iconEmpty && styles.iconEmpty, ownerState.iconFilled && styles.iconFilled, ownerState.iconHover && styles.iconHover, ownerState.iconFocus && styles.iconFocus, ownerState.iconActive && styles.iconActive];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  // Fit wrapper to actual icon size.\n  display: 'flex',\n  transition: theme.transitions.create('transform', {\n    duration: theme.transitions.duration.shortest\n  }),\n  // Fix mouseLeave issue.\n  // https://github.com/facebook/react/issues/4492\n  pointerEvents: 'none',\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.iconActive,\n    style: {\n      transform: 'scale(1.2)'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.iconEmpty,\n    style: {\n      color: (theme.vars || theme).palette.action.disabled\n    }\n  }]\n})));\nconst RatingDecimal = styled('span', {\n  name: 'MuiRating',\n  slot: 'Decimal',\n  shouldForwardProp: prop => slotShouldForwardProp(prop) && prop !== 'iconActive',\n  overridesResolver: (props, styles) => {\n    const {\n      iconActive\n    } = props;\n    return [styles.decimal, iconActive && styles.iconActive];\n  }\n})({\n  position: 'relative',\n  variants: [{\n    props: ({\n      iconActive\n    }) => iconActive,\n    style: {\n      transform: 'scale(1.2)'\n    }\n  }]\n});\nfunction IconContainer(props) {\n  const {\n    value,\n    ...other\n  } = props;\n  return /*#__PURE__*/_jsx(\"span\", {\n    ...other\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? IconContainer.propTypes = {\n  value: PropTypes.number.isRequired\n} : void 0;\nfunction RatingItem(props) {\n  const {\n    classes,\n    disabled,\n    emptyIcon,\n    focus,\n    getLabelText,\n    highlightSelectedOnly,\n    hover,\n    icon,\n    IconContainerComponent,\n    isActive,\n    itemValue,\n    labelProps,\n    name,\n    onBlur,\n    onChange,\n    onClick,\n    onFocus,\n    readOnly,\n    ownerState,\n    ratingValue,\n    ratingValueRounded,\n    slots = {},\n    slotProps = {}\n  } = props;\n  const isFilled = highlightSelectedOnly ? itemValue === ratingValue : itemValue <= ratingValue;\n  const isHovered = itemValue <= hover;\n  const isFocused = itemValue <= focus;\n  const isChecked = itemValue === ratingValueRounded;\n\n  // \"name\" ensures unique IDs across different Rating components in React 17,\n  // preventing one component from affecting another. React 18's useId already handles this.\n  // Update to const id = useId(); when React 17 support is dropped.\n  // More details: https://github.com/mui/material-ui/issues/40997\n  const id = `${name}-${useId()}`;\n  const externalForwardedProps = {\n    slots,\n    slotProps\n  };\n  const [IconSlot, iconSlotProps] = useSlot('icon', {\n    elementType: RatingIcon,\n    className: clsx(classes.icon, isFilled ? classes.iconFilled : classes.iconEmpty, isHovered && classes.iconHover, isFocused && classes.iconFocus, isActive && classes.iconActive),\n    externalForwardedProps,\n    ownerState: {\n      ...ownerState,\n      iconEmpty: !isFilled,\n      iconFilled: isFilled,\n      iconHover: isHovered,\n      iconFocus: isFocused,\n      iconActive: isActive\n    },\n    additionalProps: {\n      value: itemValue\n    },\n    internalForwardedProps: {\n      // TODO: remove this in v7 because `IconContainerComponent` is deprecated\n      // only forward if `slots.icon` is NOT provided\n      as: IconContainerComponent\n    }\n  });\n  const [LabelSlot, labelSlotProps] = useSlot('label', {\n    elementType: RatingLabel,\n    externalForwardedProps,\n    ownerState: {\n      ...ownerState,\n      emptyValueFocused: undefined\n    },\n    additionalProps: {\n      style: labelProps?.style,\n      htmlFor: id\n    }\n  });\n  const container = /*#__PURE__*/_jsx(IconSlot, {\n    ...iconSlotProps,\n    children: emptyIcon && !isFilled ? emptyIcon : icon\n  });\n  if (readOnly) {\n    return /*#__PURE__*/_jsx(\"span\", {\n      ...labelProps,\n      children: container\n    });\n  }\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsxs(LabelSlot, {\n      ...labelSlotProps,\n      children: [container, /*#__PURE__*/_jsx(\"span\", {\n        className: classes.visuallyHidden,\n        children: getLabelText(itemValue)\n      })]\n    }), /*#__PURE__*/_jsx(\"input\", {\n      className: classes.visuallyHidden,\n      onFocus: onFocus,\n      onBlur: onBlur,\n      onChange: onChange,\n      onClick: onClick,\n      disabled: disabled,\n      value: itemValue,\n      id: id,\n      type: \"radio\",\n      name: name,\n      checked: isChecked\n    })]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? RatingItem.propTypes = {\n  classes: PropTypes.object.isRequired,\n  disabled: PropTypes.bool.isRequired,\n  emptyIcon: PropTypes.node,\n  focus: PropTypes.number.isRequired,\n  getLabelText: PropTypes.func.isRequired,\n  highlightSelectedOnly: PropTypes.bool.isRequired,\n  hover: PropTypes.number.isRequired,\n  icon: PropTypes.node,\n  IconContainerComponent: PropTypes.elementType.isRequired,\n  isActive: PropTypes.bool.isRequired,\n  itemValue: PropTypes.number.isRequired,\n  labelProps: PropTypes.object,\n  name: PropTypes.string,\n  onBlur: PropTypes.func.isRequired,\n  onChange: PropTypes.func.isRequired,\n  onClick: PropTypes.func.isRequired,\n  onFocus: PropTypes.func.isRequired,\n  ownerState: PropTypes.object.isRequired,\n  ratingValue: PropTypes.number,\n  ratingValueRounded: PropTypes.number,\n  readOnly: PropTypes.bool.isRequired,\n  slotProps: PropTypes.object,\n  slots: PropTypes.object\n} : void 0;\nconst defaultIcon = /*#__PURE__*/_jsx(Star, {\n  fontSize: \"inherit\"\n});\nconst defaultEmptyIcon = /*#__PURE__*/_jsx(StarBorder, {\n  fontSize: \"inherit\"\n});\nfunction defaultLabelText(value) {\n  return `${value || '0'} Star${value !== 1 ? 's' : ''}`;\n}\nconst Rating = /*#__PURE__*/React.forwardRef(function Rating(inProps, ref) {\n  const props = useDefaultProps({\n    name: 'MuiRating',\n    props: inProps\n  });\n  const {\n    component = 'span',\n    className,\n    defaultValue = null,\n    disabled = false,\n    emptyIcon = defaultEmptyIcon,\n    emptyLabelText = 'Empty',\n    getLabelText = defaultLabelText,\n    highlightSelectedOnly = false,\n    icon = defaultIcon,\n    IconContainerComponent = IconContainer,\n    max = 5,\n    name: nameProp,\n    onChange,\n    onChangeActive,\n    onMouseLeave,\n    onMouseMove,\n    precision = 1,\n    readOnly = false,\n    size = 'medium',\n    value: valueProp,\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const name = useId(nameProp);\n  const [valueDerived, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: 'Rating'\n  });\n  const valueRounded = roundValueToPrecision(valueDerived, precision);\n  const isRtl = useRtl();\n  const [{\n    hover,\n    focus\n  }, setState] = React.useState({\n    hover: -1,\n    focus: -1\n  });\n  let value = valueRounded;\n  if (hover !== -1) {\n    value = hover;\n  }\n  if (focus !== -1) {\n    value = focus;\n  }\n  const [focusVisible, setFocusVisible] = React.useState(false);\n  const rootRef = React.useRef();\n  const handleRef = useForkRef(rootRef, ref);\n  const handleMouseMove = event => {\n    if (onMouseMove) {\n      onMouseMove(event);\n    }\n    const rootNode = rootRef.current;\n    const {\n      right,\n      left,\n      width: containerWidth\n    } = rootNode.getBoundingClientRect();\n    let percent;\n    if (isRtl) {\n      percent = (right - event.clientX) / containerWidth;\n    } else {\n      percent = (event.clientX - left) / containerWidth;\n    }\n    let newHover = roundValueToPrecision(max * percent + precision / 2, precision);\n    newHover = clamp(newHover, precision, max);\n    setState(prev => prev.hover === newHover && prev.focus === newHover ? prev : {\n      hover: newHover,\n      focus: newHover\n    });\n    setFocusVisible(false);\n    if (onChangeActive && hover !== newHover) {\n      onChangeActive(event, newHover);\n    }\n  };\n  const handleMouseLeave = event => {\n    if (onMouseLeave) {\n      onMouseLeave(event);\n    }\n    const newHover = -1;\n    setState({\n      hover: newHover,\n      focus: newHover\n    });\n    if (onChangeActive && hover !== newHover) {\n      onChangeActive(event, newHover);\n    }\n  };\n  const handleChange = event => {\n    let newValue = event.target.value === '' ? null : parseFloat(event.target.value);\n\n    // Give mouse priority over keyboard\n    // Fix https://github.com/mui/material-ui/issues/22827\n    if (hover !== -1) {\n      newValue = hover;\n    }\n    setValueState(newValue);\n    if (onChange) {\n      onChange(event, newValue);\n    }\n  };\n  const handleClear = event => {\n    // Ignore keyboard events\n    // https://github.com/facebook/react/issues/7407\n    if (event.clientX === 0 && event.clientY === 0) {\n      return;\n    }\n    setState({\n      hover: -1,\n      focus: -1\n    });\n    setValueState(null);\n    if (onChange && parseFloat(event.target.value) === valueRounded) {\n      onChange(event, null);\n    }\n  };\n  const handleFocus = event => {\n    if (isFocusVisible(event.target)) {\n      setFocusVisible(true);\n    }\n    const newFocus = parseFloat(event.target.value);\n    setState(prev => ({\n      hover: prev.hover,\n      focus: newFocus\n    }));\n  };\n  const handleBlur = event => {\n    if (hover !== -1) {\n      return;\n    }\n    if (!isFocusVisible(event.target)) {\n      setFocusVisible(false);\n    }\n    const newFocus = -1;\n    setState(prev => ({\n      hover: prev.hover,\n      focus: newFocus\n    }));\n  };\n  const [emptyValueFocused, setEmptyValueFocused] = React.useState(false);\n  const ownerState = {\n    ...props,\n    component,\n    defaultValue,\n    disabled,\n    emptyIcon,\n    emptyLabelText,\n    emptyValueFocused,\n    focusVisible,\n    getLabelText,\n    icon,\n    IconContainerComponent,\n    max,\n    precision,\n    readOnly,\n    size\n  };\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = {\n    slots,\n    slotProps\n  };\n  const [RootSlot, rootSlotProps] = useSlot('root', {\n    ref: handleRef,\n    className: clsx(classes.root, className),\n    elementType: RatingRoot,\n    externalForwardedProps: {\n      ...externalForwardedProps,\n      ...other,\n      component\n    },\n    getSlotProps: handlers => ({\n      ...handlers,\n      onMouseMove: event => {\n        handleMouseMove(event);\n        handlers.onMouseMove?.(event);\n      },\n      onMouseLeave: event => {\n        handleMouseLeave(event);\n        handlers.onMouseLeave?.(event);\n      }\n    }),\n    ownerState,\n    additionalProps: {\n      role: readOnly ? 'img' : null,\n      'aria-label': readOnly ? getLabelText(value) : null\n    }\n  });\n  const [LabelSlot, labelSlotProps] = useSlot('label', {\n    className: clsx(classes.label, classes.labelEmptyValue),\n    elementType: RatingLabel,\n    externalForwardedProps,\n    ownerState\n  });\n  const [DecimalSlot, decimalSlotProps] = useSlot('decimal', {\n    className: classes.decimal,\n    elementType: RatingDecimal,\n    externalForwardedProps,\n    ownerState\n  });\n  return /*#__PURE__*/_jsxs(RootSlot, {\n    ...rootSlotProps,\n    children: [Array.from(new Array(max)).map((_, index) => {\n      const itemValue = index + 1;\n      const ratingItemProps = {\n        classes,\n        disabled,\n        emptyIcon,\n        focus,\n        getLabelText,\n        highlightSelectedOnly,\n        hover,\n        icon,\n        IconContainerComponent,\n        name,\n        onBlur: handleBlur,\n        onChange: handleChange,\n        onClick: handleClear,\n        onFocus: handleFocus,\n        ratingValue: value,\n        ratingValueRounded: valueRounded,\n        readOnly,\n        ownerState,\n        slots,\n        slotProps\n      };\n      const isActive = itemValue === Math.ceil(value) && (hover !== -1 || focus !== -1);\n      if (precision < 1) {\n        const items = Array.from(new Array(1 / precision));\n        return /*#__PURE__*/_createElement(DecimalSlot, {\n          ...decimalSlotProps,\n          key: itemValue,\n          className: clsx(decimalSlotProps.className, isActive && classes.iconActive),\n          iconActive: isActive\n        }, items.map(($, indexDecimal) => {\n          const itemDecimalValue = roundValueToPrecision(itemValue - 1 + (indexDecimal + 1) * precision, precision);\n          return /*#__PURE__*/_jsx(RatingItem, {\n            ...ratingItemProps,\n            // The icon is already displayed as active\n            isActive: false,\n            itemValue: itemDecimalValue,\n            labelProps: {\n              style: items.length - 1 === indexDecimal ? {} : {\n                width: itemDecimalValue === value ? `${(indexDecimal + 1) * precision * 100}%` : '0%',\n                overflow: 'hidden',\n                position: 'absolute'\n              }\n            }\n          }, itemDecimalValue);\n        }));\n      }\n      return /*#__PURE__*/_jsx(RatingItem, {\n        ...ratingItemProps,\n        isActive: isActive,\n        itemValue: itemValue\n      }, itemValue);\n    }), !readOnly && !disabled && /*#__PURE__*/_jsxs(LabelSlot, {\n      ...labelSlotProps,\n      children: [/*#__PURE__*/_jsx(\"input\", {\n        className: classes.visuallyHidden,\n        value: \"\",\n        id: `${name}-empty`,\n        type: \"radio\",\n        name: name,\n        checked: valueRounded == null,\n        onFocus: () => setEmptyValueFocused(true),\n        onBlur: () => setEmptyValueFocused(false),\n        onChange: handleChange\n      }), /*#__PURE__*/_jsx(\"span\", {\n        className: classes.visuallyHidden,\n        children: emptyLabelText\n      })]\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Rating.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The default value. Use when the component is not controlled.\n   * @default null\n   */\n  defaultValue: PropTypes.number,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * The icon to display when empty.\n   * @default <StarBorder fontSize=\"inherit\" />\n   */\n  emptyIcon: PropTypes.node,\n  /**\n   * The label read when the rating input is empty.\n   * @default 'Empty'\n   */\n  emptyLabelText: PropTypes.node,\n  /**\n   * Accepts a function which returns a string value that provides a user-friendly name for the current value of the rating.\n   * This is important for screen reader users.\n   *\n   * For localization purposes, you can use the provided [translations](https://mui.com/material-ui/guides/localization/).\n   * @param {number} value The rating label's value to format.\n   * @returns {string}\n   * @default function defaultLabelText(value) {\n   *   return `${value || '0'} Star${value !== 1 ? 's' : ''}`;\n   * }\n   */\n  getLabelText: PropTypes.func,\n  /**\n   * If `true`, only the selected icon will be highlighted.\n   * @default false\n   */\n  highlightSelectedOnly: PropTypes.bool,\n  /**\n   * The icon to display.\n   * @default <Star fontSize=\"inherit\" />\n   */\n  icon: PropTypes.node,\n  /**\n   * The component containing the icon.\n   * @deprecated Use `slotProps.icon.component` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default function IconContainer(props) {\n   *   const { value, ...other } = props;\n   *   return <span {...other} />;\n   * }\n   */\n  IconContainerComponent: PropTypes.elementType,\n  /**\n   * Maximum rating.\n   * @default 5\n   */\n  max: PropTypes.number,\n  /**\n   * The name attribute of the radio `input` elements.\n   * This input `name` should be unique within the page.\n   * Being unique within a form is insufficient since the `name` is used to generate IDs.\n   */\n  name: PropTypes.string,\n  /**\n   * Callback fired when the value changes.\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {number|null} value The new value.\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback function that is fired when the hover state changes.\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {number} value The new value.\n   */\n  onChangeActive: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onMouseLeave: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onMouseMove: PropTypes.func,\n  /**\n   * The minimum increment value change allowed.\n   * @default 1\n   */\n  precision: chainPropTypes(PropTypes.number, props => {\n    if (props.precision < 0.1) {\n      return new Error(['MUI: The prop `precision` should be above 0.1.', 'A value below this limit has an imperceptible impact.'].join('\\n'));\n    }\n    return null;\n  }),\n  /**\n   * Removes all hover effects and pointer events.\n   * @default false\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * The size of the component.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['small', 'medium', 'large']), PropTypes.string]),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    decimal: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    icon: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    label: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    decimal: PropTypes.elementType,\n    icon: PropTypes.elementType,\n    label: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The rating value.\n   */\n  value: PropTypes.number\n} : void 0;\nexport default Rating;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "clamp", "visuallyHidden", "chainPropTypes", "composeClasses", "useRtl", "isFocusVisible", "capitalize", "useForkRef", "useControlled", "unstable_useId", "useId", "Star", "StarBorder", "styled", "memoTheme", "useDefaultProps", "slotShouldForwardProp", "ratingClasses", "getRatingUtilityClass", "useSlot", "jsx", "_jsx", "jsxs", "_jsxs", "createElement", "_createElement", "getDecimalPrecision", "num", "decimalPart", "toString", "split", "length", "roundValueToPrecision", "value", "precision", "nearest", "Math", "round", "Number", "toFixed", "useUtilityClasses", "ownerState", "classes", "size", "readOnly", "disabled", "emptyValueFocused", "focusVisible", "slots", "root", "label", "labelEmptyValue", "icon", "iconEmpty", "iconFilled", "iconHover", "iconFocus", "iconActive", "decimal", "RatingRoot", "name", "slot", "overridesResolver", "props", "styles", "theme", "display", "position", "fontSize", "typography", "pxToRem", "color", "cursor", "textAlign", "width", "WebkitTapHighlightColor", "opacity", "vars", "palette", "action", "disabledOpacity", "pointerEvents", "outline", "variants", "style", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "labelEmptyValueActive", "top", "bottom", "RatingIcon", "transition", "transitions", "create", "duration", "shortest", "transform", "RatingDecimal", "shouldForwardProp", "prop", "IconContainer", "other", "process", "env", "NODE_ENV", "propTypes", "number", "isRequired", "RatingItem", "emptyIcon", "focus", "getLabelText", "highlightSelectedOnly", "hover", "IconContainerComponent", "isActive", "itemValue", "labelProps", "onBlur", "onChange", "onClick", "onFocus", "ratingValue", "ratingValueRounded", "slotProps", "isFilled", "isHovered", "isFocused", "isChecked", "id", "externalForwardedProps", "IconSlot", "iconSlotProps", "elementType", "className", "additionalProps", "internalForwardedProps", "as", "LabelSlot", "labelSlotProps", "undefined", "htmlFor", "container", "children", "Fragment", "type", "checked", "object", "bool", "node", "func", "string", "defaultIcon", "defaultEmptyIcon", "defaultLabelText", "Rating", "forwardRef", "inProps", "ref", "component", "defaultValue", "emptyLabelText", "max", "nameProp", "onChangeActive", "onMouseLeave", "onMouseMove", "valueProp", "valueDerived", "setValueState", "controlled", "default", "valueRounded", "isRtl", "setState", "useState", "setFocusVisible", "rootRef", "useRef", "handleRef", "handleMouseMove", "event", "rootNode", "current", "right", "left", "containerWidth", "getBoundingClientRect", "percent", "clientX", "newHover", "prev", "handleMouseLeave", "handleChange", "newValue", "target", "parseFloat", "handleClear", "clientY", "handleFocus", "newFocus", "handleBlur", "setEmptyValueFocused", "RootSlot", "rootSlotProps", "getSlotProps", "handlers", "role", "DecimalSlot", "decimalSlotProps", "Array", "from", "map", "_", "index", "ratingItemProps", "ceil", "items", "key", "$", "indexDecimal", "itemDecimalValue", "overflow", "Error", "join", "oneOfType", "oneOf", "shape", "sx", "arrayOf"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/@mui/material/esm/Rating/Rating.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport clamp from '@mui/utils/clamp';\nimport visuallyHidden from '@mui/utils/visuallyHidden';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport isFocusVisible from '@mui/utils/isFocusVisible';\nimport { capitalize, useForkRef, useControlled, unstable_useId as useId } from \"../utils/index.js\";\nimport Star from \"../internal/svg-icons/Star.js\";\nimport StarBorder from \"../internal/svg-icons/StarBorder.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport slotShouldForwardProp from \"../styles/slotShouldForwardProp.js\";\nimport ratingClasses, { getRatingUtilityClass } from \"./ratingClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { createElement as _createElement } from \"react\";\nfunction getDecimalPrecision(num) {\n  const decimalPart = num.toString().split('.')[1];\n  return decimalPart ? decimalPart.length : 0;\n}\nfunction roundValueToPrecision(value, precision) {\n  if (value == null) {\n    return value;\n  }\n  const nearest = Math.round(value / precision) * precision;\n  return Number(nearest.toFixed(getDecimalPrecision(precision)));\n}\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    size,\n    readOnly,\n    disabled,\n    emptyValueFocused,\n    focusVisible\n  } = ownerState;\n  const slots = {\n    root: ['root', `size${capitalize(size)}`, disabled && 'disabled', focusVisible && 'focusVisible', readOnly && 'readOnly'],\n    label: ['label', 'pristine'],\n    labelEmptyValue: [emptyValueFocused && 'labelEmptyValueActive'],\n    icon: ['icon'],\n    iconEmpty: ['iconEmpty'],\n    iconFilled: ['iconFilled'],\n    iconHover: ['iconHover'],\n    iconFocus: ['iconFocus'],\n    iconActive: ['iconActive'],\n    decimal: ['decimal'],\n    visuallyHidden: ['visuallyHidden']\n  };\n  return composeClasses(slots, getRatingUtilityClass, classes);\n};\nconst RatingRoot = styled('span', {\n  name: 'MuiRating',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [`& .${ratingClasses.visuallyHidden}`]: styles.visuallyHidden\n    }, styles.root, styles[`size${capitalize(ownerState.size)}`], ownerState.readOnly && styles.readOnly];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  display: 'inline-flex',\n  // Required to position the pristine input absolutely\n  position: 'relative',\n  fontSize: theme.typography.pxToRem(24),\n  color: '#faaf00',\n  cursor: 'pointer',\n  textAlign: 'left',\n  width: 'min-content',\n  WebkitTapHighlightColor: 'transparent',\n  [`&.${ratingClasses.disabled}`]: {\n    opacity: (theme.vars || theme).palette.action.disabledOpacity,\n    pointerEvents: 'none'\n  },\n  [`&.${ratingClasses.focusVisible} .${ratingClasses.iconActive}`]: {\n    outline: '1px solid #999'\n  },\n  [`& .${ratingClasses.visuallyHidden}`]: visuallyHidden,\n  variants: [{\n    props: {\n      size: 'small'\n    },\n    style: {\n      fontSize: theme.typography.pxToRem(18)\n    }\n  }, {\n    props: {\n      size: 'large'\n    },\n    style: {\n      fontSize: theme.typography.pxToRem(30)\n    }\n  }, {\n    // TODO v6: use the .Mui-readOnly global state class\n    props: ({\n      ownerState\n    }) => ownerState.readOnly,\n    style: {\n      pointerEvents: 'none'\n    }\n  }]\n})));\nconst RatingLabel = styled('label', {\n  name: 'MuiRating',\n  slot: 'Label',\n  overridesResolver: ({\n    ownerState\n  }, styles) => [styles.label, ownerState.emptyValueFocused && styles.labelEmptyValueActive]\n})({\n  cursor: 'inherit',\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.emptyValueFocused,\n    style: {\n      top: 0,\n      bottom: 0,\n      position: 'absolute',\n      outline: '1px solid #999',\n      width: '100%'\n    }\n  }]\n});\nconst RatingIcon = styled('span', {\n  name: 'MuiRating',\n  slot: 'Icon',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.icon, ownerState.iconEmpty && styles.iconEmpty, ownerState.iconFilled && styles.iconFilled, ownerState.iconHover && styles.iconHover, ownerState.iconFocus && styles.iconFocus, ownerState.iconActive && styles.iconActive];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  // Fit wrapper to actual icon size.\n  display: 'flex',\n  transition: theme.transitions.create('transform', {\n    duration: theme.transitions.duration.shortest\n  }),\n  // Fix mouseLeave issue.\n  // https://github.com/facebook/react/issues/4492\n  pointerEvents: 'none',\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.iconActive,\n    style: {\n      transform: 'scale(1.2)'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.iconEmpty,\n    style: {\n      color: (theme.vars || theme).palette.action.disabled\n    }\n  }]\n})));\nconst RatingDecimal = styled('span', {\n  name: 'MuiRating',\n  slot: 'Decimal',\n  shouldForwardProp: prop => slotShouldForwardProp(prop) && prop !== 'iconActive',\n  overridesResolver: (props, styles) => {\n    const {\n      iconActive\n    } = props;\n    return [styles.decimal, iconActive && styles.iconActive];\n  }\n})({\n  position: 'relative',\n  variants: [{\n    props: ({\n      iconActive\n    }) => iconActive,\n    style: {\n      transform: 'scale(1.2)'\n    }\n  }]\n});\nfunction IconContainer(props) {\n  const {\n    value,\n    ...other\n  } = props;\n  return /*#__PURE__*/_jsx(\"span\", {\n    ...other\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? IconContainer.propTypes = {\n  value: PropTypes.number.isRequired\n} : void 0;\nfunction RatingItem(props) {\n  const {\n    classes,\n    disabled,\n    emptyIcon,\n    focus,\n    getLabelText,\n    highlightSelectedOnly,\n    hover,\n    icon,\n    IconContainerComponent,\n    isActive,\n    itemValue,\n    labelProps,\n    name,\n    onBlur,\n    onChange,\n    onClick,\n    onFocus,\n    readOnly,\n    ownerState,\n    ratingValue,\n    ratingValueRounded,\n    slots = {},\n    slotProps = {}\n  } = props;\n  const isFilled = highlightSelectedOnly ? itemValue === ratingValue : itemValue <= ratingValue;\n  const isHovered = itemValue <= hover;\n  const isFocused = itemValue <= focus;\n  const isChecked = itemValue === ratingValueRounded;\n\n  // \"name\" ensures unique IDs across different Rating components in React 17,\n  // preventing one component from affecting another. React 18's useId already handles this.\n  // Update to const id = useId(); when React 17 support is dropped.\n  // More details: https://github.com/mui/material-ui/issues/40997\n  const id = `${name}-${useId()}`;\n  const externalForwardedProps = {\n    slots,\n    slotProps\n  };\n  const [IconSlot, iconSlotProps] = useSlot('icon', {\n    elementType: RatingIcon,\n    className: clsx(classes.icon, isFilled ? classes.iconFilled : classes.iconEmpty, isHovered && classes.iconHover, isFocused && classes.iconFocus, isActive && classes.iconActive),\n    externalForwardedProps,\n    ownerState: {\n      ...ownerState,\n      iconEmpty: !isFilled,\n      iconFilled: isFilled,\n      iconHover: isHovered,\n      iconFocus: isFocused,\n      iconActive: isActive\n    },\n    additionalProps: {\n      value: itemValue\n    },\n    internalForwardedProps: {\n      // TODO: remove this in v7 because `IconContainerComponent` is deprecated\n      // only forward if `slots.icon` is NOT provided\n      as: IconContainerComponent\n    }\n  });\n  const [LabelSlot, labelSlotProps] = useSlot('label', {\n    elementType: RatingLabel,\n    externalForwardedProps,\n    ownerState: {\n      ...ownerState,\n      emptyValueFocused: undefined\n    },\n    additionalProps: {\n      style: labelProps?.style,\n      htmlFor: id\n    }\n  });\n  const container = /*#__PURE__*/_jsx(IconSlot, {\n    ...iconSlotProps,\n    children: emptyIcon && !isFilled ? emptyIcon : icon\n  });\n  if (readOnly) {\n    return /*#__PURE__*/_jsx(\"span\", {\n      ...labelProps,\n      children: container\n    });\n  }\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsxs(LabelSlot, {\n      ...labelSlotProps,\n      children: [container, /*#__PURE__*/_jsx(\"span\", {\n        className: classes.visuallyHidden,\n        children: getLabelText(itemValue)\n      })]\n    }), /*#__PURE__*/_jsx(\"input\", {\n      className: classes.visuallyHidden,\n      onFocus: onFocus,\n      onBlur: onBlur,\n      onChange: onChange,\n      onClick: onClick,\n      disabled: disabled,\n      value: itemValue,\n      id: id,\n      type: \"radio\",\n      name: name,\n      checked: isChecked\n    })]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? RatingItem.propTypes = {\n  classes: PropTypes.object.isRequired,\n  disabled: PropTypes.bool.isRequired,\n  emptyIcon: PropTypes.node,\n  focus: PropTypes.number.isRequired,\n  getLabelText: PropTypes.func.isRequired,\n  highlightSelectedOnly: PropTypes.bool.isRequired,\n  hover: PropTypes.number.isRequired,\n  icon: PropTypes.node,\n  IconContainerComponent: PropTypes.elementType.isRequired,\n  isActive: PropTypes.bool.isRequired,\n  itemValue: PropTypes.number.isRequired,\n  labelProps: PropTypes.object,\n  name: PropTypes.string,\n  onBlur: PropTypes.func.isRequired,\n  onChange: PropTypes.func.isRequired,\n  onClick: PropTypes.func.isRequired,\n  onFocus: PropTypes.func.isRequired,\n  ownerState: PropTypes.object.isRequired,\n  ratingValue: PropTypes.number,\n  ratingValueRounded: PropTypes.number,\n  readOnly: PropTypes.bool.isRequired,\n  slotProps: PropTypes.object,\n  slots: PropTypes.object\n} : void 0;\nconst defaultIcon = /*#__PURE__*/_jsx(Star, {\n  fontSize: \"inherit\"\n});\nconst defaultEmptyIcon = /*#__PURE__*/_jsx(StarBorder, {\n  fontSize: \"inherit\"\n});\nfunction defaultLabelText(value) {\n  return `${value || '0'} Star${value !== 1 ? 's' : ''}`;\n}\nconst Rating = /*#__PURE__*/React.forwardRef(function Rating(inProps, ref) {\n  const props = useDefaultProps({\n    name: 'MuiRating',\n    props: inProps\n  });\n  const {\n    component = 'span',\n    className,\n    defaultValue = null,\n    disabled = false,\n    emptyIcon = defaultEmptyIcon,\n    emptyLabelText = 'Empty',\n    getLabelText = defaultLabelText,\n    highlightSelectedOnly = false,\n    icon = defaultIcon,\n    IconContainerComponent = IconContainer,\n    max = 5,\n    name: nameProp,\n    onChange,\n    onChangeActive,\n    onMouseLeave,\n    onMouseMove,\n    precision = 1,\n    readOnly = false,\n    size = 'medium',\n    value: valueProp,\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const name = useId(nameProp);\n  const [valueDerived, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: 'Rating'\n  });\n  const valueRounded = roundValueToPrecision(valueDerived, precision);\n  const isRtl = useRtl();\n  const [{\n    hover,\n    focus\n  }, setState] = React.useState({\n    hover: -1,\n    focus: -1\n  });\n  let value = valueRounded;\n  if (hover !== -1) {\n    value = hover;\n  }\n  if (focus !== -1) {\n    value = focus;\n  }\n  const [focusVisible, setFocusVisible] = React.useState(false);\n  const rootRef = React.useRef();\n  const handleRef = useForkRef(rootRef, ref);\n  const handleMouseMove = event => {\n    if (onMouseMove) {\n      onMouseMove(event);\n    }\n    const rootNode = rootRef.current;\n    const {\n      right,\n      left,\n      width: containerWidth\n    } = rootNode.getBoundingClientRect();\n    let percent;\n    if (isRtl) {\n      percent = (right - event.clientX) / containerWidth;\n    } else {\n      percent = (event.clientX - left) / containerWidth;\n    }\n    let newHover = roundValueToPrecision(max * percent + precision / 2, precision);\n    newHover = clamp(newHover, precision, max);\n    setState(prev => prev.hover === newHover && prev.focus === newHover ? prev : {\n      hover: newHover,\n      focus: newHover\n    });\n    setFocusVisible(false);\n    if (onChangeActive && hover !== newHover) {\n      onChangeActive(event, newHover);\n    }\n  };\n  const handleMouseLeave = event => {\n    if (onMouseLeave) {\n      onMouseLeave(event);\n    }\n    const newHover = -1;\n    setState({\n      hover: newHover,\n      focus: newHover\n    });\n    if (onChangeActive && hover !== newHover) {\n      onChangeActive(event, newHover);\n    }\n  };\n  const handleChange = event => {\n    let newValue = event.target.value === '' ? null : parseFloat(event.target.value);\n\n    // Give mouse priority over keyboard\n    // Fix https://github.com/mui/material-ui/issues/22827\n    if (hover !== -1) {\n      newValue = hover;\n    }\n    setValueState(newValue);\n    if (onChange) {\n      onChange(event, newValue);\n    }\n  };\n  const handleClear = event => {\n    // Ignore keyboard events\n    // https://github.com/facebook/react/issues/7407\n    if (event.clientX === 0 && event.clientY === 0) {\n      return;\n    }\n    setState({\n      hover: -1,\n      focus: -1\n    });\n    setValueState(null);\n    if (onChange && parseFloat(event.target.value) === valueRounded) {\n      onChange(event, null);\n    }\n  };\n  const handleFocus = event => {\n    if (isFocusVisible(event.target)) {\n      setFocusVisible(true);\n    }\n    const newFocus = parseFloat(event.target.value);\n    setState(prev => ({\n      hover: prev.hover,\n      focus: newFocus\n    }));\n  };\n  const handleBlur = event => {\n    if (hover !== -1) {\n      return;\n    }\n    if (!isFocusVisible(event.target)) {\n      setFocusVisible(false);\n    }\n    const newFocus = -1;\n    setState(prev => ({\n      hover: prev.hover,\n      focus: newFocus\n    }));\n  };\n  const [emptyValueFocused, setEmptyValueFocused] = React.useState(false);\n  const ownerState = {\n    ...props,\n    component,\n    defaultValue,\n    disabled,\n    emptyIcon,\n    emptyLabelText,\n    emptyValueFocused,\n    focusVisible,\n    getLabelText,\n    icon,\n    IconContainerComponent,\n    max,\n    precision,\n    readOnly,\n    size\n  };\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = {\n    slots,\n    slotProps\n  };\n  const [RootSlot, rootSlotProps] = useSlot('root', {\n    ref: handleRef,\n    className: clsx(classes.root, className),\n    elementType: RatingRoot,\n    externalForwardedProps: {\n      ...externalForwardedProps,\n      ...other,\n      component\n    },\n    getSlotProps: handlers => ({\n      ...handlers,\n      onMouseMove: event => {\n        handleMouseMove(event);\n        handlers.onMouseMove?.(event);\n      },\n      onMouseLeave: event => {\n        handleMouseLeave(event);\n        handlers.onMouseLeave?.(event);\n      }\n    }),\n    ownerState,\n    additionalProps: {\n      role: readOnly ? 'img' : null,\n      'aria-label': readOnly ? getLabelText(value) : null\n    }\n  });\n  const [LabelSlot, labelSlotProps] = useSlot('label', {\n    className: clsx(classes.label, classes.labelEmptyValue),\n    elementType: RatingLabel,\n    externalForwardedProps,\n    ownerState\n  });\n  const [DecimalSlot, decimalSlotProps] = useSlot('decimal', {\n    className: classes.decimal,\n    elementType: RatingDecimal,\n    externalForwardedProps,\n    ownerState\n  });\n  return /*#__PURE__*/_jsxs(RootSlot, {\n    ...rootSlotProps,\n    children: [Array.from(new Array(max)).map((_, index) => {\n      const itemValue = index + 1;\n      const ratingItemProps = {\n        classes,\n        disabled,\n        emptyIcon,\n        focus,\n        getLabelText,\n        highlightSelectedOnly,\n        hover,\n        icon,\n        IconContainerComponent,\n        name,\n        onBlur: handleBlur,\n        onChange: handleChange,\n        onClick: handleClear,\n        onFocus: handleFocus,\n        ratingValue: value,\n        ratingValueRounded: valueRounded,\n        readOnly,\n        ownerState,\n        slots,\n        slotProps\n      };\n      const isActive = itemValue === Math.ceil(value) && (hover !== -1 || focus !== -1);\n      if (precision < 1) {\n        const items = Array.from(new Array(1 / precision));\n        return /*#__PURE__*/_createElement(DecimalSlot, {\n          ...decimalSlotProps,\n          key: itemValue,\n          className: clsx(decimalSlotProps.className, isActive && classes.iconActive),\n          iconActive: isActive\n        }, items.map(($, indexDecimal) => {\n          const itemDecimalValue = roundValueToPrecision(itemValue - 1 + (indexDecimal + 1) * precision, precision);\n          return /*#__PURE__*/_jsx(RatingItem, {\n            ...ratingItemProps,\n            // The icon is already displayed as active\n            isActive: false,\n            itemValue: itemDecimalValue,\n            labelProps: {\n              style: items.length - 1 === indexDecimal ? {} : {\n                width: itemDecimalValue === value ? `${(indexDecimal + 1) * precision * 100}%` : '0%',\n                overflow: 'hidden',\n                position: 'absolute'\n              }\n            }\n          }, itemDecimalValue);\n        }));\n      }\n      return /*#__PURE__*/_jsx(RatingItem, {\n        ...ratingItemProps,\n        isActive: isActive,\n        itemValue: itemValue\n      }, itemValue);\n    }), !readOnly && !disabled && /*#__PURE__*/_jsxs(LabelSlot, {\n      ...labelSlotProps,\n      children: [/*#__PURE__*/_jsx(\"input\", {\n        className: classes.visuallyHidden,\n        value: \"\",\n        id: `${name}-empty`,\n        type: \"radio\",\n        name: name,\n        checked: valueRounded == null,\n        onFocus: () => setEmptyValueFocused(true),\n        onBlur: () => setEmptyValueFocused(false),\n        onChange: handleChange\n      }), /*#__PURE__*/_jsx(\"span\", {\n        className: classes.visuallyHidden,\n        children: emptyLabelText\n      })]\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Rating.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The default value. Use when the component is not controlled.\n   * @default null\n   */\n  defaultValue: PropTypes.number,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * The icon to display when empty.\n   * @default <StarBorder fontSize=\"inherit\" />\n   */\n  emptyIcon: PropTypes.node,\n  /**\n   * The label read when the rating input is empty.\n   * @default 'Empty'\n   */\n  emptyLabelText: PropTypes.node,\n  /**\n   * Accepts a function which returns a string value that provides a user-friendly name for the current value of the rating.\n   * This is important for screen reader users.\n   *\n   * For localization purposes, you can use the provided [translations](https://mui.com/material-ui/guides/localization/).\n   * @param {number} value The rating label's value to format.\n   * @returns {string}\n   * @default function defaultLabelText(value) {\n   *   return `${value || '0'} Star${value !== 1 ? 's' : ''}`;\n   * }\n   */\n  getLabelText: PropTypes.func,\n  /**\n   * If `true`, only the selected icon will be highlighted.\n   * @default false\n   */\n  highlightSelectedOnly: PropTypes.bool,\n  /**\n   * The icon to display.\n   * @default <Star fontSize=\"inherit\" />\n   */\n  icon: PropTypes.node,\n  /**\n   * The component containing the icon.\n   * @deprecated Use `slotProps.icon.component` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default function IconContainer(props) {\n   *   const { value, ...other } = props;\n   *   return <span {...other} />;\n   * }\n   */\n  IconContainerComponent: PropTypes.elementType,\n  /**\n   * Maximum rating.\n   * @default 5\n   */\n  max: PropTypes.number,\n  /**\n   * The name attribute of the radio `input` elements.\n   * This input `name` should be unique within the page.\n   * Being unique within a form is insufficient since the `name` is used to generate IDs.\n   */\n  name: PropTypes.string,\n  /**\n   * Callback fired when the value changes.\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {number|null} value The new value.\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback function that is fired when the hover state changes.\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {number} value The new value.\n   */\n  onChangeActive: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onMouseLeave: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onMouseMove: PropTypes.func,\n  /**\n   * The minimum increment value change allowed.\n   * @default 1\n   */\n  precision: chainPropTypes(PropTypes.number, props => {\n    if (props.precision < 0.1) {\n      return new Error(['MUI: The prop `precision` should be above 0.1.', 'A value below this limit has an imperceptible impact.'].join('\\n'));\n    }\n    return null;\n  }),\n  /**\n   * Removes all hover effects and pointer events.\n   * @default false\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * The size of the component.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['small', 'medium', 'large']), PropTypes.string]),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    decimal: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    icon: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    label: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    decimal: PropTypes.elementType,\n    icon: PropTypes.elementType,\n    label: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The rating value.\n   */\n  value: PropTypes.number\n} : void 0;\nexport default Rating;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,UAAU,EAAEC,UAAU,EAAEC,aAAa,EAAEC,cAAc,IAAIC,KAAK,QAAQ,mBAAmB;AAClG,OAAOC,IAAI,MAAM,+BAA+B;AAChD,OAAOC,UAAU,MAAM,qCAAqC;AAC5D,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,SAASC,eAAe,QAAQ,kCAAkC;AAClE,OAAOC,qBAAqB,MAAM,oCAAoC;AACtE,OAAOC,aAAa,IAAIC,qBAAqB,QAAQ,oBAAoB;AACzE,OAAOC,OAAO,MAAM,qBAAqB;AACzC,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,SAASC,aAAa,IAAIC,cAAc,QAAQ,OAAO;AACvD,SAASC,mBAAmBA,CAACC,GAAG,EAAE;EAChC,MAAMC,WAAW,GAAGD,GAAG,CAACE,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAChD,OAAOF,WAAW,GAAGA,WAAW,CAACG,MAAM,GAAG,CAAC;AAC7C;AACA,SAASC,qBAAqBA,CAACC,KAAK,EAAEC,SAAS,EAAE;EAC/C,IAAID,KAAK,IAAI,IAAI,EAAE;IACjB,OAAOA,KAAK;EACd;EACA,MAAME,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACJ,KAAK,GAAGC,SAAS,CAAC,GAAGA,SAAS;EACzD,OAAOI,MAAM,CAACH,OAAO,CAACI,OAAO,CAACb,mBAAmB,CAACQ,SAAS,CAAC,CAAC,CAAC;AAChE;AACA,MAAMM,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,IAAI;IACJC,QAAQ;IACRC,QAAQ;IACRC,iBAAiB;IACjBC;EACF,CAAC,GAAGN,UAAU;EACd,MAAMO,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO3C,UAAU,CAACqC,IAAI,CAAC,EAAE,EAAEE,QAAQ,IAAI,UAAU,EAAEE,YAAY,IAAI,cAAc,EAAEH,QAAQ,IAAI,UAAU,CAAC;IACzHM,KAAK,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC;IAC5BC,eAAe,EAAE,CAACL,iBAAiB,IAAI,uBAAuB,CAAC;IAC/DM,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,SAAS,EAAE,CAAC,WAAW,CAAC;IACxBC,UAAU,EAAE,CAAC,YAAY,CAAC;IAC1BC,SAAS,EAAE,CAAC,WAAW,CAAC;IACxBC,SAAS,EAAE,CAAC,WAAW,CAAC;IACxBC,UAAU,EAAE,CAAC,YAAY,CAAC;IAC1BC,OAAO,EAAE,CAAC,SAAS,CAAC;IACpBzD,cAAc,EAAE,CAAC,gBAAgB;EACnC,CAAC;EACD,OAAOE,cAAc,CAAC6C,KAAK,EAAE9B,qBAAqB,EAAEwB,OAAO,CAAC;AAC9D,CAAC;AACD,MAAMiB,UAAU,GAAG9C,MAAM,CAAC,MAAM,EAAE;EAChC+C,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJvB;IACF,CAAC,GAAGsB,KAAK;IACT,OAAO,CAAC;MACN,CAAC,MAAM9C,aAAa,CAAChB,cAAc,EAAE,GAAG+D,MAAM,CAAC/D;IACjD,CAAC,EAAE+D,MAAM,CAACf,IAAI,EAAEe,MAAM,CAAC,OAAO1D,UAAU,CAACmC,UAAU,CAACE,IAAI,CAAC,EAAE,CAAC,EAAEF,UAAU,CAACG,QAAQ,IAAIoB,MAAM,CAACpB,QAAQ,CAAC;EACvG;AACF,CAAC,CAAC,CAAC9B,SAAS,CAAC,CAAC;EACZmD;AACF,CAAC,MAAM;EACLC,OAAO,EAAE,aAAa;EACtB;EACAC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAEH,KAAK,CAACI,UAAU,CAACC,OAAO,CAAC,EAAE,CAAC;EACtCC,KAAK,EAAE,SAAS;EAChBC,MAAM,EAAE,SAAS;EACjBC,SAAS,EAAE,MAAM;EACjBC,KAAK,EAAE,aAAa;EACpBC,uBAAuB,EAAE,aAAa;EACtC,CAAC,KAAK1D,aAAa,CAAC4B,QAAQ,EAAE,GAAG;IAC/B+B,OAAO,EAAE,CAACX,KAAK,CAACY,IAAI,IAAIZ,KAAK,EAAEa,OAAO,CAACC,MAAM,CAACC,eAAe;IAC7DC,aAAa,EAAE;EACjB,CAAC;EACD,CAAC,KAAKhE,aAAa,CAAC8B,YAAY,KAAK9B,aAAa,CAACwC,UAAU,EAAE,GAAG;IAChEyB,OAAO,EAAE;EACX,CAAC;EACD,CAAC,MAAMjE,aAAa,CAAChB,cAAc,EAAE,GAAGA,cAAc;EACtDkF,QAAQ,EAAE,CAAC;IACTpB,KAAK,EAAE;MACLpB,IAAI,EAAE;IACR,CAAC;IACDyC,KAAK,EAAE;MACLhB,QAAQ,EAAEH,KAAK,CAACI,UAAU,CAACC,OAAO,CAAC,EAAE;IACvC;EACF,CAAC,EAAE;IACDP,KAAK,EAAE;MACLpB,IAAI,EAAE;IACR,CAAC;IACDyC,KAAK,EAAE;MACLhB,QAAQ,EAAEH,KAAK,CAACI,UAAU,CAACC,OAAO,CAAC,EAAE;IACvC;EACF,CAAC,EAAE;IACD;IACAP,KAAK,EAAEA,CAAC;MACNtB;IACF,CAAC,KAAKA,UAAU,CAACG,QAAQ;IACzBwC,KAAK,EAAE;MACLH,aAAa,EAAE;IACjB;EACF,CAAC;AACH,CAAC,CAAC,CAAC,CAAC;AACJ,MAAMI,WAAW,GAAGxE,MAAM,CAAC,OAAO,EAAE;EAClC+C,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,OAAO;EACbC,iBAAiB,EAAEA,CAAC;IAClBrB;EACF,CAAC,EAAEuB,MAAM,KAAK,CAACA,MAAM,CAACd,KAAK,EAAET,UAAU,CAACK,iBAAiB,IAAIkB,MAAM,CAACsB,qBAAqB;AAC3F,CAAC,CAAC,CAAC;EACDd,MAAM,EAAE,SAAS;EACjBW,QAAQ,EAAE,CAAC;IACTpB,KAAK,EAAEA,CAAC;MACNtB;IACF,CAAC,KAAKA,UAAU,CAACK,iBAAiB;IAClCsC,KAAK,EAAE;MACLG,GAAG,EAAE,CAAC;MACNC,MAAM,EAAE,CAAC;MACTrB,QAAQ,EAAE,UAAU;MACpBe,OAAO,EAAE,gBAAgB;MACzBR,KAAK,EAAE;IACT;EACF,CAAC;AACH,CAAC,CAAC;AACF,MAAMe,UAAU,GAAG5E,MAAM,CAAC,MAAM,EAAE;EAChC+C,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJvB;IACF,CAAC,GAAGsB,KAAK;IACT,OAAO,CAACC,MAAM,CAACZ,IAAI,EAAEX,UAAU,CAACY,SAAS,IAAIW,MAAM,CAACX,SAAS,EAAEZ,UAAU,CAACa,UAAU,IAAIU,MAAM,CAACV,UAAU,EAAEb,UAAU,CAACc,SAAS,IAAIS,MAAM,CAACT,SAAS,EAAEd,UAAU,CAACe,SAAS,IAAIQ,MAAM,CAACR,SAAS,EAAEf,UAAU,CAACgB,UAAU,IAAIO,MAAM,CAACP,UAAU,CAAC;EAC5O;AACF,CAAC,CAAC,CAAC3C,SAAS,CAAC,CAAC;EACZmD;AACF,CAAC,MAAM;EACL;EACAC,OAAO,EAAE,MAAM;EACfwB,UAAU,EAAEzB,KAAK,CAAC0B,WAAW,CAACC,MAAM,CAAC,WAAW,EAAE;IAChDC,QAAQ,EAAE5B,KAAK,CAAC0B,WAAW,CAACE,QAAQ,CAACC;EACvC,CAAC,CAAC;EACF;EACA;EACAb,aAAa,EAAE,MAAM;EACrBE,QAAQ,EAAE,CAAC;IACTpB,KAAK,EAAEA,CAAC;MACNtB;IACF,CAAC,KAAKA,UAAU,CAACgB,UAAU;IAC3B2B,KAAK,EAAE;MACLW,SAAS,EAAE;IACb;EACF,CAAC,EAAE;IACDhC,KAAK,EAAEA,CAAC;MACNtB;IACF,CAAC,KAAKA,UAAU,CAACY,SAAS;IAC1B+B,KAAK,EAAE;MACLb,KAAK,EAAE,CAACN,KAAK,CAACY,IAAI,IAAIZ,KAAK,EAAEa,OAAO,CAACC,MAAM,CAAClC;IAC9C;EACF,CAAC;AACH,CAAC,CAAC,CAAC,CAAC;AACJ,MAAMmD,aAAa,GAAGnF,MAAM,CAAC,MAAM,EAAE;EACnC+C,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,SAAS;EACfoC,iBAAiB,EAAEC,IAAI,IAAIlF,qBAAqB,CAACkF,IAAI,CAAC,IAAIA,IAAI,KAAK,YAAY;EAC/EpC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJP;IACF,CAAC,GAAGM,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,OAAO,EAAED,UAAU,IAAIO,MAAM,CAACP,UAAU,CAAC;EAC1D;AACF,CAAC,CAAC,CAAC;EACDU,QAAQ,EAAE,UAAU;EACpBgB,QAAQ,EAAE,CAAC;IACTpB,KAAK,EAAEA,CAAC;MACNN;IACF,CAAC,KAAKA,UAAU;IAChB2B,KAAK,EAAE;MACLW,SAAS,EAAE;IACb;EACF,CAAC;AACH,CAAC,CAAC;AACF,SAASI,aAAaA,CAACpC,KAAK,EAAE;EAC5B,MAAM;IACJ9B,KAAK;IACL,GAAGmE;EACL,CAAC,GAAGrC,KAAK;EACT,OAAO,aAAa1C,IAAI,CAAC,MAAM,EAAE;IAC/B,GAAG+E;EACL,CAAC,CAAC;AACJ;AACAC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGJ,aAAa,CAACK,SAAS,GAAG;EAChEvE,KAAK,EAAEnC,SAAS,CAAC2G,MAAM,CAACC;AAC1B,CAAC,GAAG,KAAK,CAAC;AACV,SAASC,UAAUA,CAAC5C,KAAK,EAAE;EACzB,MAAM;IACJrB,OAAO;IACPG,QAAQ;IACR+D,SAAS;IACTC,KAAK;IACLC,YAAY;IACZC,qBAAqB;IACrBC,KAAK;IACL5D,IAAI;IACJ6D,sBAAsB;IACtBC,QAAQ;IACRC,SAAS;IACTC,UAAU;IACVxD,IAAI;IACJyD,MAAM;IACNC,QAAQ;IACRC,OAAO;IACPC,OAAO;IACP5E,QAAQ;IACRH,UAAU;IACVgF,WAAW;IACXC,kBAAkB;IAClB1E,KAAK,GAAG,CAAC,CAAC;IACV2E,SAAS,GAAG,CAAC;EACf,CAAC,GAAG5D,KAAK;EACT,MAAM6D,QAAQ,GAAGb,qBAAqB,GAAGI,SAAS,KAAKM,WAAW,GAAGN,SAAS,IAAIM,WAAW;EAC7F,MAAMI,SAAS,GAAGV,SAAS,IAAIH,KAAK;EACpC,MAAMc,SAAS,GAAGX,SAAS,IAAIN,KAAK;EACpC,MAAMkB,SAAS,GAAGZ,SAAS,KAAKO,kBAAkB;;EAElD;EACA;EACA;EACA;EACA,MAAMM,EAAE,GAAG,GAAGpE,IAAI,IAAIlD,KAAK,CAAC,CAAC,EAAE;EAC/B,MAAMuH,sBAAsB,GAAG;IAC7BjF,KAAK;IACL2E;EACF,CAAC;EACD,MAAM,CAACO,QAAQ,EAAEC,aAAa,CAAC,GAAGhH,OAAO,CAAC,MAAM,EAAE;IAChDiH,WAAW,EAAE3C,UAAU;IACvB4C,SAAS,EAAEtI,IAAI,CAAC2C,OAAO,CAACU,IAAI,EAAEwE,QAAQ,GAAGlF,OAAO,CAACY,UAAU,GAAGZ,OAAO,CAACW,SAAS,EAAEwE,SAAS,IAAInF,OAAO,CAACa,SAAS,EAAEuE,SAAS,IAAIpF,OAAO,CAACc,SAAS,EAAE0D,QAAQ,IAAIxE,OAAO,CAACe,UAAU,CAAC;IAChLwE,sBAAsB;IACtBxF,UAAU,EAAE;MACV,GAAGA,UAAU;MACbY,SAAS,EAAE,CAACuE,QAAQ;MACpBtE,UAAU,EAAEsE,QAAQ;MACpBrE,SAAS,EAAEsE,SAAS;MACpBrE,SAAS,EAAEsE,SAAS;MACpBrE,UAAU,EAAEyD;IACd,CAAC;IACDoB,eAAe,EAAE;MACfrG,KAAK,EAAEkF;IACT,CAAC;IACDoB,sBAAsB,EAAE;MACtB;MACA;MACAC,EAAE,EAAEvB;IACN;EACF,CAAC,CAAC;EACF,MAAM,CAACwB,SAAS,EAAEC,cAAc,CAAC,GAAGvH,OAAO,CAAC,OAAO,EAAE;IACnDiH,WAAW,EAAE/C,WAAW;IACxB4C,sBAAsB;IACtBxF,UAAU,EAAE;MACV,GAAGA,UAAU;MACbK,iBAAiB,EAAE6F;IACrB,CAAC;IACDL,eAAe,EAAE;MACflD,KAAK,EAAEgC,UAAU,EAAEhC,KAAK;MACxBwD,OAAO,EAAEZ;IACX;EACF,CAAC,CAAC;EACF,MAAMa,SAAS,GAAG,aAAaxH,IAAI,CAAC6G,QAAQ,EAAE;IAC5C,GAAGC,aAAa;IAChBW,QAAQ,EAAElC,SAAS,IAAI,CAACgB,QAAQ,GAAGhB,SAAS,GAAGxD;EACjD,CAAC,CAAC;EACF,IAAIR,QAAQ,EAAE;IACZ,OAAO,aAAavB,IAAI,CAAC,MAAM,EAAE;MAC/B,GAAG+F,UAAU;MACb0B,QAAQ,EAAED;IACZ,CAAC,CAAC;EACJ;EACA,OAAO,aAAatH,KAAK,CAAC1B,KAAK,CAACkJ,QAAQ,EAAE;IACxCD,QAAQ,EAAE,CAAC,aAAavH,KAAK,CAACkH,SAAS,EAAE;MACvC,GAAGC,cAAc;MACjBI,QAAQ,EAAE,CAACD,SAAS,EAAE,aAAaxH,IAAI,CAAC,MAAM,EAAE;QAC9CgH,SAAS,EAAE3F,OAAO,CAACzC,cAAc;QACjC6I,QAAQ,EAAEhC,YAAY,CAACK,SAAS;MAClC,CAAC,CAAC;IACJ,CAAC,CAAC,EAAE,aAAa9F,IAAI,CAAC,OAAO,EAAE;MAC7BgH,SAAS,EAAE3F,OAAO,CAACzC,cAAc;MACjCuH,OAAO,EAAEA,OAAO;MAChBH,MAAM,EAAEA,MAAM;MACdC,QAAQ,EAAEA,QAAQ;MAClBC,OAAO,EAAEA,OAAO;MAChB1E,QAAQ,EAAEA,QAAQ;MAClBZ,KAAK,EAAEkF,SAAS;MAChBa,EAAE,EAAEA,EAAE;MACNgB,IAAI,EAAE,OAAO;MACbpF,IAAI,EAAEA,IAAI;MACVqF,OAAO,EAAElB;IACX,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACA1B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGI,UAAU,CAACH,SAAS,GAAG;EAC7D9D,OAAO,EAAE5C,SAAS,CAACoJ,MAAM,CAACxC,UAAU;EACpC7D,QAAQ,EAAE/C,SAAS,CAACqJ,IAAI,CAACzC,UAAU;EACnCE,SAAS,EAAE9G,SAAS,CAACsJ,IAAI;EACzBvC,KAAK,EAAE/G,SAAS,CAAC2G,MAAM,CAACC,UAAU;EAClCI,YAAY,EAAEhH,SAAS,CAACuJ,IAAI,CAAC3C,UAAU;EACvCK,qBAAqB,EAAEjH,SAAS,CAACqJ,IAAI,CAACzC,UAAU;EAChDM,KAAK,EAAElH,SAAS,CAAC2G,MAAM,CAACC,UAAU;EAClCtD,IAAI,EAAEtD,SAAS,CAACsJ,IAAI;EACpBnC,sBAAsB,EAAEnH,SAAS,CAACsI,WAAW,CAAC1B,UAAU;EACxDQ,QAAQ,EAAEpH,SAAS,CAACqJ,IAAI,CAACzC,UAAU;EACnCS,SAAS,EAAErH,SAAS,CAAC2G,MAAM,CAACC,UAAU;EACtCU,UAAU,EAAEtH,SAAS,CAACoJ,MAAM;EAC5BtF,IAAI,EAAE9D,SAAS,CAACwJ,MAAM;EACtBjC,MAAM,EAAEvH,SAAS,CAACuJ,IAAI,CAAC3C,UAAU;EACjCY,QAAQ,EAAExH,SAAS,CAACuJ,IAAI,CAAC3C,UAAU;EACnCa,OAAO,EAAEzH,SAAS,CAACuJ,IAAI,CAAC3C,UAAU;EAClCc,OAAO,EAAE1H,SAAS,CAACuJ,IAAI,CAAC3C,UAAU;EAClCjE,UAAU,EAAE3C,SAAS,CAACoJ,MAAM,CAACxC,UAAU;EACvCe,WAAW,EAAE3H,SAAS,CAAC2G,MAAM;EAC7BiB,kBAAkB,EAAE5H,SAAS,CAAC2G,MAAM;EACpC7D,QAAQ,EAAE9C,SAAS,CAACqJ,IAAI,CAACzC,UAAU;EACnCiB,SAAS,EAAE7H,SAAS,CAACoJ,MAAM;EAC3BlG,KAAK,EAAElD,SAAS,CAACoJ;AACnB,CAAC,GAAG,KAAK,CAAC;AACV,MAAMK,WAAW,GAAG,aAAalI,IAAI,CAACV,IAAI,EAAE;EAC1CyD,QAAQ,EAAE;AACZ,CAAC,CAAC;AACF,MAAMoF,gBAAgB,GAAG,aAAanI,IAAI,CAACT,UAAU,EAAE;EACrDwD,QAAQ,EAAE;AACZ,CAAC,CAAC;AACF,SAASqF,gBAAgBA,CAACxH,KAAK,EAAE;EAC/B,OAAO,GAAGA,KAAK,IAAI,GAAG,QAAQA,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAE;AACxD;AACA,MAAMyH,MAAM,GAAG,aAAa7J,KAAK,CAAC8J,UAAU,CAAC,SAASD,MAAMA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACzE,MAAM9F,KAAK,GAAGhD,eAAe,CAAC;IAC5B6C,IAAI,EAAE,WAAW;IACjBG,KAAK,EAAE6F;EACT,CAAC,CAAC;EACF,MAAM;IACJE,SAAS,GAAG,MAAM;IAClBzB,SAAS;IACT0B,YAAY,GAAG,IAAI;IACnBlH,QAAQ,GAAG,KAAK;IAChB+D,SAAS,GAAG4C,gBAAgB;IAC5BQ,cAAc,GAAG,OAAO;IACxBlD,YAAY,GAAG2C,gBAAgB;IAC/B1C,qBAAqB,GAAG,KAAK;IAC7B3D,IAAI,GAAGmG,WAAW;IAClBtC,sBAAsB,GAAGd,aAAa;IACtC8D,GAAG,GAAG,CAAC;IACPrG,IAAI,EAAEsG,QAAQ;IACd5C,QAAQ;IACR6C,cAAc;IACdC,YAAY;IACZC,WAAW;IACXnI,SAAS,GAAG,CAAC;IACbU,QAAQ,GAAG,KAAK;IAChBD,IAAI,GAAG,QAAQ;IACfV,KAAK,EAAEqI,SAAS;IAChBtH,KAAK,GAAG,CAAC,CAAC;IACV2E,SAAS,GAAG,CAAC,CAAC;IACd,GAAGvB;EACL,CAAC,GAAGrC,KAAK;EACT,MAAMH,IAAI,GAAGlD,KAAK,CAACwJ,QAAQ,CAAC;EAC5B,MAAM,CAACK,YAAY,EAAEC,aAAa,CAAC,GAAGhK,aAAa,CAAC;IAClDiK,UAAU,EAAEH,SAAS;IACrBI,OAAO,EAAEX,YAAY;IACrBnG,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM+G,YAAY,GAAG3I,qBAAqB,CAACuI,YAAY,EAAErI,SAAS,CAAC;EACnE,MAAM0I,KAAK,GAAGxK,MAAM,CAAC,CAAC;EACtB,MAAM,CAAC;IACL4G,KAAK;IACLH;EACF,CAAC,EAAEgE,QAAQ,CAAC,GAAGhL,KAAK,CAACiL,QAAQ,CAAC;IAC5B9D,KAAK,EAAE,CAAC,CAAC;IACTH,KAAK,EAAE,CAAC;EACV,CAAC,CAAC;EACF,IAAI5E,KAAK,GAAG0I,YAAY;EACxB,IAAI3D,KAAK,KAAK,CAAC,CAAC,EAAE;IAChB/E,KAAK,GAAG+E,KAAK;EACf;EACA,IAAIH,KAAK,KAAK,CAAC,CAAC,EAAE;IAChB5E,KAAK,GAAG4E,KAAK;EACf;EACA,MAAM,CAAC9D,YAAY,EAAEgI,eAAe,CAAC,GAAGlL,KAAK,CAACiL,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAME,OAAO,GAAGnL,KAAK,CAACoL,MAAM,CAAC,CAAC;EAC9B,MAAMC,SAAS,GAAG3K,UAAU,CAACyK,OAAO,EAAEnB,GAAG,CAAC;EAC1C,MAAMsB,eAAe,GAAGC,KAAK,IAAI;IAC/B,IAAIf,WAAW,EAAE;MACfA,WAAW,CAACe,KAAK,CAAC;IACpB;IACA,MAAMC,QAAQ,GAAGL,OAAO,CAACM,OAAO;IAChC,MAAM;MACJC,KAAK;MACLC,IAAI;MACJ9G,KAAK,EAAE+G;IACT,CAAC,GAAGJ,QAAQ,CAACK,qBAAqB,CAAC,CAAC;IACpC,IAAIC,OAAO;IACX,IAAIf,KAAK,EAAE;MACTe,OAAO,GAAG,CAACJ,KAAK,GAAGH,KAAK,CAACQ,OAAO,IAAIH,cAAc;IACpD,CAAC,MAAM;MACLE,OAAO,GAAG,CAACP,KAAK,CAACQ,OAAO,GAAGJ,IAAI,IAAIC,cAAc;IACnD;IACA,IAAII,QAAQ,GAAG7J,qBAAqB,CAACiI,GAAG,GAAG0B,OAAO,GAAGzJ,SAAS,GAAG,CAAC,EAAEA,SAAS,CAAC;IAC9E2J,QAAQ,GAAG7L,KAAK,CAAC6L,QAAQ,EAAE3J,SAAS,EAAE+H,GAAG,CAAC;IAC1CY,QAAQ,CAACiB,IAAI,IAAIA,IAAI,CAAC9E,KAAK,KAAK6E,QAAQ,IAAIC,IAAI,CAACjF,KAAK,KAAKgF,QAAQ,GAAGC,IAAI,GAAG;MAC3E9E,KAAK,EAAE6E,QAAQ;MACfhF,KAAK,EAAEgF;IACT,CAAC,CAAC;IACFd,eAAe,CAAC,KAAK,CAAC;IACtB,IAAIZ,cAAc,IAAInD,KAAK,KAAK6E,QAAQ,EAAE;MACxC1B,cAAc,CAACiB,KAAK,EAAES,QAAQ,CAAC;IACjC;EACF,CAAC;EACD,MAAME,gBAAgB,GAAGX,KAAK,IAAI;IAChC,IAAIhB,YAAY,EAAE;MAChBA,YAAY,CAACgB,KAAK,CAAC;IACrB;IACA,MAAMS,QAAQ,GAAG,CAAC,CAAC;IACnBhB,QAAQ,CAAC;MACP7D,KAAK,EAAE6E,QAAQ;MACfhF,KAAK,EAAEgF;IACT,CAAC,CAAC;IACF,IAAI1B,cAAc,IAAInD,KAAK,KAAK6E,QAAQ,EAAE;MACxC1B,cAAc,CAACiB,KAAK,EAAES,QAAQ,CAAC;IACjC;EACF,CAAC;EACD,MAAMG,YAAY,GAAGZ,KAAK,IAAI;IAC5B,IAAIa,QAAQ,GAAGb,KAAK,CAACc,MAAM,CAACjK,KAAK,KAAK,EAAE,GAAG,IAAI,GAAGkK,UAAU,CAACf,KAAK,CAACc,MAAM,CAACjK,KAAK,CAAC;;IAEhF;IACA;IACA,IAAI+E,KAAK,KAAK,CAAC,CAAC,EAAE;MAChBiF,QAAQ,GAAGjF,KAAK;IAClB;IACAwD,aAAa,CAACyB,QAAQ,CAAC;IACvB,IAAI3E,QAAQ,EAAE;MACZA,QAAQ,CAAC8D,KAAK,EAAEa,QAAQ,CAAC;IAC3B;EACF,CAAC;EACD,MAAMG,WAAW,GAAGhB,KAAK,IAAI;IAC3B;IACA;IACA,IAAIA,KAAK,CAACQ,OAAO,KAAK,CAAC,IAAIR,KAAK,CAACiB,OAAO,KAAK,CAAC,EAAE;MAC9C;IACF;IACAxB,QAAQ,CAAC;MACP7D,KAAK,EAAE,CAAC,CAAC;MACTH,KAAK,EAAE,CAAC;IACV,CAAC,CAAC;IACF2D,aAAa,CAAC,IAAI,CAAC;IACnB,IAAIlD,QAAQ,IAAI6E,UAAU,CAACf,KAAK,CAACc,MAAM,CAACjK,KAAK,CAAC,KAAK0I,YAAY,EAAE;MAC/DrD,QAAQ,CAAC8D,KAAK,EAAE,IAAI,CAAC;IACvB;EACF,CAAC;EACD,MAAMkB,WAAW,GAAGlB,KAAK,IAAI;IAC3B,IAAI/K,cAAc,CAAC+K,KAAK,CAACc,MAAM,CAAC,EAAE;MAChCnB,eAAe,CAAC,IAAI,CAAC;IACvB;IACA,MAAMwB,QAAQ,GAAGJ,UAAU,CAACf,KAAK,CAACc,MAAM,CAACjK,KAAK,CAAC;IAC/C4I,QAAQ,CAACiB,IAAI,KAAK;MAChB9E,KAAK,EAAE8E,IAAI,CAAC9E,KAAK;MACjBH,KAAK,EAAE0F;IACT,CAAC,CAAC,CAAC;EACL,CAAC;EACD,MAAMC,UAAU,GAAGpB,KAAK,IAAI;IAC1B,IAAIpE,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB;IACF;IACA,IAAI,CAAC3G,cAAc,CAAC+K,KAAK,CAACc,MAAM,CAAC,EAAE;MACjCnB,eAAe,CAAC,KAAK,CAAC;IACxB;IACA,MAAMwB,QAAQ,GAAG,CAAC,CAAC;IACnB1B,QAAQ,CAACiB,IAAI,KAAK;MAChB9E,KAAK,EAAE8E,IAAI,CAAC9E,KAAK;MACjBH,KAAK,EAAE0F;IACT,CAAC,CAAC,CAAC;EACL,CAAC;EACD,MAAM,CAACzJ,iBAAiB,EAAE2J,oBAAoB,CAAC,GAAG5M,KAAK,CAACiL,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAMrI,UAAU,GAAG;IACjB,GAAGsB,KAAK;IACR+F,SAAS;IACTC,YAAY;IACZlH,QAAQ;IACR+D,SAAS;IACToD,cAAc;IACdlH,iBAAiB;IACjBC,YAAY;IACZ+D,YAAY;IACZ1D,IAAI;IACJ6D,sBAAsB;IACtBgD,GAAG;IACH/H,SAAS;IACTU,QAAQ;IACRD;EACF,CAAC;EACD,MAAMD,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMwF,sBAAsB,GAAG;IAC7BjF,KAAK;IACL2E;EACF,CAAC;EACD,MAAM,CAAC+E,QAAQ,EAAEC,aAAa,CAAC,GAAGxL,OAAO,CAAC,MAAM,EAAE;IAChD0I,GAAG,EAAEqB,SAAS;IACd7C,SAAS,EAAEtI,IAAI,CAAC2C,OAAO,CAACO,IAAI,EAAEoF,SAAS,CAAC;IACxCD,WAAW,EAAEzE,UAAU;IACvBsE,sBAAsB,EAAE;MACtB,GAAGA,sBAAsB;MACzB,GAAG7B,KAAK;MACR0D;IACF,CAAC;IACD8C,YAAY,EAAEC,QAAQ,KAAK;MACzB,GAAGA,QAAQ;MACXxC,WAAW,EAAEe,KAAK,IAAI;QACpBD,eAAe,CAACC,KAAK,CAAC;QACtByB,QAAQ,CAACxC,WAAW,GAAGe,KAAK,CAAC;MAC/B,CAAC;MACDhB,YAAY,EAAEgB,KAAK,IAAI;QACrBW,gBAAgB,CAACX,KAAK,CAAC;QACvByB,QAAQ,CAACzC,YAAY,GAAGgB,KAAK,CAAC;MAChC;IACF,CAAC,CAAC;IACF3I,UAAU;IACV6F,eAAe,EAAE;MACfwE,IAAI,EAAElK,QAAQ,GAAG,KAAK,GAAG,IAAI;MAC7B,YAAY,EAAEA,QAAQ,GAAGkE,YAAY,CAAC7E,KAAK,CAAC,GAAG;IACjD;EACF,CAAC,CAAC;EACF,MAAM,CAACwG,SAAS,EAAEC,cAAc,CAAC,GAAGvH,OAAO,CAAC,OAAO,EAAE;IACnDkH,SAAS,EAAEtI,IAAI,CAAC2C,OAAO,CAACQ,KAAK,EAAER,OAAO,CAACS,eAAe,CAAC;IACvDiF,WAAW,EAAE/C,WAAW;IACxB4C,sBAAsB;IACtBxF;EACF,CAAC,CAAC;EACF,MAAM,CAACsK,WAAW,EAAEC,gBAAgB,CAAC,GAAG7L,OAAO,CAAC,SAAS,EAAE;IACzDkH,SAAS,EAAE3F,OAAO,CAACgB,OAAO;IAC1B0E,WAAW,EAAEpC,aAAa;IAC1BiC,sBAAsB;IACtBxF;EACF,CAAC,CAAC;EACF,OAAO,aAAalB,KAAK,CAACmL,QAAQ,EAAE;IAClC,GAAGC,aAAa;IAChB7D,QAAQ,EAAE,CAACmE,KAAK,CAACC,IAAI,CAAC,IAAID,KAAK,CAAChD,GAAG,CAAC,CAAC,CAACkD,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAK;MACtD,MAAMlG,SAAS,GAAGkG,KAAK,GAAG,CAAC;MAC3B,MAAMC,eAAe,GAAG;QACtB5K,OAAO;QACPG,QAAQ;QACR+D,SAAS;QACTC,KAAK;QACLC,YAAY;QACZC,qBAAqB;QACrBC,KAAK;QACL5D,IAAI;QACJ6D,sBAAsB;QACtBrD,IAAI;QACJyD,MAAM,EAAEmF,UAAU;QAClBlF,QAAQ,EAAE0E,YAAY;QACtBzE,OAAO,EAAE6E,WAAW;QACpB5E,OAAO,EAAE8E,WAAW;QACpB7E,WAAW,EAAExF,KAAK;QAClByF,kBAAkB,EAAEiD,YAAY;QAChC/H,QAAQ;QACRH,UAAU;QACVO,KAAK;QACL2E;MACF,CAAC;MACD,MAAMT,QAAQ,GAAGC,SAAS,KAAK/E,IAAI,CAACmL,IAAI,CAACtL,KAAK,CAAC,KAAK+E,KAAK,KAAK,CAAC,CAAC,IAAIH,KAAK,KAAK,CAAC,CAAC,CAAC;MACjF,IAAI3E,SAAS,GAAG,CAAC,EAAE;QACjB,MAAMsL,KAAK,GAAGP,KAAK,CAACC,IAAI,CAAC,IAAID,KAAK,CAAC,CAAC,GAAG/K,SAAS,CAAC,CAAC;QAClD,OAAO,aAAaT,cAAc,CAACsL,WAAW,EAAE;UAC9C,GAAGC,gBAAgB;UACnBS,GAAG,EAAEtG,SAAS;UACdkB,SAAS,EAAEtI,IAAI,CAACiN,gBAAgB,CAAC3E,SAAS,EAAEnB,QAAQ,IAAIxE,OAAO,CAACe,UAAU,CAAC;UAC3EA,UAAU,EAAEyD;QACd,CAAC,EAAEsG,KAAK,CAACL,GAAG,CAAC,CAACO,CAAC,EAAEC,YAAY,KAAK;UAChC,MAAMC,gBAAgB,GAAG5L,qBAAqB,CAACmF,SAAS,GAAG,CAAC,GAAG,CAACwG,YAAY,GAAG,CAAC,IAAIzL,SAAS,EAAEA,SAAS,CAAC;UACzG,OAAO,aAAab,IAAI,CAACsF,UAAU,EAAE;YACnC,GAAG2G,eAAe;YAClB;YACApG,QAAQ,EAAE,KAAK;YACfC,SAAS,EAAEyG,gBAAgB;YAC3BxG,UAAU,EAAE;cACVhC,KAAK,EAAEoI,KAAK,CAACzL,MAAM,GAAG,CAAC,KAAK4L,YAAY,GAAG,CAAC,CAAC,GAAG;gBAC9CjJ,KAAK,EAAEkJ,gBAAgB,KAAK3L,KAAK,GAAG,GAAG,CAAC0L,YAAY,GAAG,CAAC,IAAIzL,SAAS,GAAG,GAAG,GAAG,GAAG,IAAI;gBACrF2L,QAAQ,EAAE,QAAQ;gBAClB1J,QAAQ,EAAE;cACZ;YACF;UACF,CAAC,EAAEyJ,gBAAgB,CAAC;QACtB,CAAC,CAAC,CAAC;MACL;MACA,OAAO,aAAavM,IAAI,CAACsF,UAAU,EAAE;QACnC,GAAG2G,eAAe;QAClBpG,QAAQ,EAAEA,QAAQ;QAClBC,SAAS,EAAEA;MACb,CAAC,EAAEA,SAAS,CAAC;IACf,CAAC,CAAC,EAAE,CAACvE,QAAQ,IAAI,CAACC,QAAQ,IAAI,aAAatB,KAAK,CAACkH,SAAS,EAAE;MAC1D,GAAGC,cAAc;MACjBI,QAAQ,EAAE,CAAC,aAAazH,IAAI,CAAC,OAAO,EAAE;QACpCgH,SAAS,EAAE3F,OAAO,CAACzC,cAAc;QACjCgC,KAAK,EAAE,EAAE;QACT+F,EAAE,EAAE,GAAGpE,IAAI,QAAQ;QACnBoF,IAAI,EAAE,OAAO;QACbpF,IAAI,EAAEA,IAAI;QACVqF,OAAO,EAAE0B,YAAY,IAAI,IAAI;QAC7BnD,OAAO,EAAEA,CAAA,KAAMiF,oBAAoB,CAAC,IAAI,CAAC;QACzCpF,MAAM,EAAEA,CAAA,KAAMoF,oBAAoB,CAAC,KAAK,CAAC;QACzCnF,QAAQ,EAAE0E;MACZ,CAAC,CAAC,EAAE,aAAa3K,IAAI,CAAC,MAAM,EAAE;QAC5BgH,SAAS,EAAE3F,OAAO,CAACzC,cAAc;QACjC6I,QAAQ,EAAEkB;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACF3D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGmD,MAAM,CAAClD,SAAS,CAAC,yBAAyB;EAChF;EACA;EACA;EACA;EACA;AACF;AACA;EACEsC,QAAQ,EAAEhJ,SAAS,CAACsJ,IAAI;EACxB;AACF;AACA;EACE1G,OAAO,EAAE5C,SAAS,CAACoJ,MAAM;EACzB;AACF;AACA;EACEb,SAAS,EAAEvI,SAAS,CAACwJ,MAAM;EAC3B;AACF;AACA;AACA;EACEQ,SAAS,EAAEhK,SAAS,CAACsI,WAAW;EAChC;AACF;AACA;AACA;EACE2B,YAAY,EAAEjK,SAAS,CAAC2G,MAAM;EAC9B;AACF;AACA;AACA;EACE5D,QAAQ,EAAE/C,SAAS,CAACqJ,IAAI;EACxB;AACF;AACA;AACA;EACEvC,SAAS,EAAE9G,SAAS,CAACsJ,IAAI;EACzB;AACF;AACA;AACA;EACEY,cAAc,EAAElK,SAAS,CAACsJ,IAAI;EAC9B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEtC,YAAY,EAAEhH,SAAS,CAACuJ,IAAI;EAC5B;AACF;AACA;AACA;EACEtC,qBAAqB,EAAEjH,SAAS,CAACqJ,IAAI;EACrC;AACF;AACA;AACA;EACE/F,IAAI,EAAEtD,SAAS,CAACsJ,IAAI;EACpB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEnC,sBAAsB,EAAEnH,SAAS,CAACsI,WAAW;EAC7C;AACF;AACA;AACA;EACE6B,GAAG,EAAEnK,SAAS,CAAC2G,MAAM;EACrB;AACF;AACA;AACA;AACA;EACE7C,IAAI,EAAE9D,SAAS,CAACwJ,MAAM;EACtB;AACF;AACA;AACA;AACA;EACEhC,QAAQ,EAAExH,SAAS,CAACuJ,IAAI;EACxB;AACF;AACA;AACA;AACA;EACEc,cAAc,EAAErK,SAAS,CAACuJ,IAAI;EAC9B;AACF;AACA;EACEe,YAAY,EAAEtK,SAAS,CAACuJ,IAAI;EAC5B;AACF;AACA;EACEgB,WAAW,EAAEvK,SAAS,CAACuJ,IAAI;EAC3B;AACF;AACA;AACA;EACEnH,SAAS,EAAEhC,cAAc,CAACJ,SAAS,CAAC2G,MAAM,EAAE1C,KAAK,IAAI;IACnD,IAAIA,KAAK,CAAC7B,SAAS,GAAG,GAAG,EAAE;MACzB,OAAO,IAAI4L,KAAK,CAAC,CAAC,gDAAgD,EAAE,uDAAuD,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1I;IACA,OAAO,IAAI;EACb,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEnL,QAAQ,EAAE9C,SAAS,CAACqJ,IAAI;EACxB;AACF;AACA;AACA;EACExG,IAAI,EAAE7C,SAAS,CAAC,sCAAsCkO,SAAS,CAAC,CAAClO,SAAS,CAACmO,KAAK,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC,EAAEnO,SAAS,CAACwJ,MAAM,CAAC,CAAC;EAClI;AACF;AACA;AACA;EACE3B,SAAS,EAAE7H,SAAS,CAACoO,KAAK,CAAC;IACzBxK,OAAO,EAAE5D,SAAS,CAACkO,SAAS,CAAC,CAAClO,SAAS,CAACuJ,IAAI,EAAEvJ,SAAS,CAACoJ,MAAM,CAAC,CAAC;IAChE9F,IAAI,EAAEtD,SAAS,CAACkO,SAAS,CAAC,CAAClO,SAAS,CAACuJ,IAAI,EAAEvJ,SAAS,CAACoJ,MAAM,CAAC,CAAC;IAC7DhG,KAAK,EAAEpD,SAAS,CAACkO,SAAS,CAAC,CAAClO,SAAS,CAACuJ,IAAI,EAAEvJ,SAAS,CAACoJ,MAAM,CAAC,CAAC;IAC9DjG,IAAI,EAAEnD,SAAS,CAACkO,SAAS,CAAC,CAAClO,SAAS,CAACuJ,IAAI,EAAEvJ,SAAS,CAACoJ,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;EACElG,KAAK,EAAElD,SAAS,CAACoO,KAAK,CAAC;IACrBxK,OAAO,EAAE5D,SAAS,CAACsI,WAAW;IAC9BhF,IAAI,EAAEtD,SAAS,CAACsI,WAAW;IAC3BlF,KAAK,EAAEpD,SAAS,CAACsI,WAAW;IAC5BnF,IAAI,EAAEnD,SAAS,CAACsI;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACE+F,EAAE,EAAErO,SAAS,CAACkO,SAAS,CAAC,CAAClO,SAAS,CAACsO,OAAO,CAACtO,SAAS,CAACkO,SAAS,CAAC,CAAClO,SAAS,CAACuJ,IAAI,EAAEvJ,SAAS,CAACoJ,MAAM,EAAEpJ,SAAS,CAACqJ,IAAI,CAAC,CAAC,CAAC,EAAErJ,SAAS,CAACuJ,IAAI,EAAEvJ,SAAS,CAACoJ,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;EACEjH,KAAK,EAAEnC,SAAS,CAAC2G;AACnB,CAAC,GAAG,KAAK,CAAC;AACV,eAAeiD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}