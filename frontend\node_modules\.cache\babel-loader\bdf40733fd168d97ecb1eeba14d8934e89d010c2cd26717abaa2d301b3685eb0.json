{"ast": null, "code": "function attachTimeline(animation, timeline) {\n  animation.timeline = timeline;\n  animation.onfinish = null;\n}\nexport { attachTimeline };", "map": {"version": 3, "names": ["attachTimeline", "animation", "timeline", "onfinish"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/motion-dom/dist/es/animation/waapi/utils/attach-timeline.mjs"], "sourcesContent": ["function attachTimeline(animation, timeline) {\n    animation.timeline = timeline;\n    animation.onfinish = null;\n}\n\nexport { attachTimeline };\n"], "mappings": "AAAA,SAASA,cAAcA,CAACC,SAAS,EAAEC,QAAQ,EAAE;EACzCD,SAAS,CAACC,QAAQ,GAAGA,QAAQ;EAC7BD,SAAS,CAACE,QAAQ,GAAG,IAAI;AAC7B;AAEA,SAASH,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}