{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport SvgIcon from \"../SvgIcon/index.js\";\n\n/**\n * Private module reserved for @mui packages.\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function createSvgIcon(path, displayName) {\n  function Component(props, ref) {\n    return /*#__PURE__*/_jsx(SvgIcon, {\n      \"data-testid\": process.env.NODE_ENV !== 'production' ? `${displayName}Icon` : undefined,\n      ref: ref,\n      ...props,\n      children: path\n    });\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    // Need to set `displayName` on the inner component for React.memo.\n    // React prior to 16.14 ignores `displayName` on the wrapper.\n    Component.displayName = `${displayName}Icon`;\n  }\n  Component.muiName = SvgIcon.muiName;\n  return /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(Component));\n}", "map": {"version": 3, "names": ["React", "SvgIcon", "jsx", "_jsx", "createSvgIcon", "path", "displayName", "Component", "props", "ref", "process", "env", "NODE_ENV", "undefined", "children", "mui<PERSON><PERSON>", "memo", "forwardRef"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/@mui/material/esm/utils/createSvgIcon.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport SvgIcon from \"../SvgIcon/index.js\";\n\n/**\n * Private module reserved for @mui packages.\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function createSvgIcon(path, displayName) {\n  function Component(props, ref) {\n    return /*#__PURE__*/_jsx(SvgIcon, {\n      \"data-testid\": process.env.NODE_ENV !== 'production' ? `${displayName}Icon` : undefined,\n      ref: ref,\n      ...props,\n      children: path\n    });\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    // Need to set `displayName` on the inner component for React.memo.\n    // React prior to 16.14 ignores `displayName` on the wrapper.\n    Component.displayName = `${displayName}Icon`;\n  }\n  Component.muiName = SvgIcon.muiName;\n  return /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(Component));\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,qBAAqB;;AAEzC;AACA;AACA;AACA,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAe,SAASC,aAAaA,CAACC,IAAI,EAAEC,WAAW,EAAE;EACvD,SAASC,SAASA,CAACC,KAAK,EAAEC,GAAG,EAAE;IAC7B,OAAO,aAAaN,IAAI,CAACF,OAAO,EAAE;MAChC,aAAa,EAAES,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG,GAAGN,WAAW,MAAM,GAAGO,SAAS;MACvFJ,GAAG,EAAEA,GAAG;MACR,GAAGD,KAAK;MACRM,QAAQ,EAAET;IACZ,CAAC,CAAC;EACJ;EACA,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACA;IACAL,SAAS,CAACD,WAAW,GAAG,GAAGA,WAAW,MAAM;EAC9C;EACAC,SAAS,CAACQ,OAAO,GAAGd,OAAO,CAACc,OAAO;EACnC,OAAO,aAAaf,KAAK,CAACgB,IAAI,CAAC,aAAahB,KAAK,CAACiB,UAAU,CAACV,SAAS,CAAC,CAAC;AAC1E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}