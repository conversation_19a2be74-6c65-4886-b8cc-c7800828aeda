{"ast": null, "code": "// eslint-disable-next-line import/prefer-default-export\nexport { unstable_ClassNameGenerator } from '@mui/utils';", "map": {"version": 3, "names": ["unstable_ClassNameGenerator"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/@mui/material/esm/className/index.js"], "sourcesContent": ["// eslint-disable-next-line import/prefer-default-export\nexport { unstable_ClassNameGenerator } from '@mui/utils';"], "mappings": "AAAA;AACA,SAASA,2BAA2B,QAAQ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}