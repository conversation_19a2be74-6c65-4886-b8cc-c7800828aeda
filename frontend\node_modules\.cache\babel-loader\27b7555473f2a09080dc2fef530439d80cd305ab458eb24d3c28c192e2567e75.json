{"ast": null, "code": "var _jsxFileName = \"D:\\\\Desktop\\\\projects\\\\port1\\\\port\\\\frontend\\\\src\\\\components\\\\Hero.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { TypeAnimation } from \"react-type-animation\";\nimport { Modal, Box, IconButton, Button } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport DownloadIcon from \"@mui/icons-material/Download\";\nimport resumeImage from \"../assets/resume.jpg\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst avatarUrl = process.env.PUBLIC_URL + \"/logo2.png\";\n\n// Responsive breakpoints\nconst useResponsive = () => {\n  _s();\n  const [isMobile, setIsMobile] = React.useState(window.innerWidth <= 768);\n  const [isTablet, setIsTablet] = React.useState(window.innerWidth > 768 && window.innerWidth <= 1024);\n  React.useEffect(() => {\n    const handleResize = () => {\n      setIsMobile(window.innerWidth <= 768);\n      setIsTablet(window.innerWidth > 768 && window.innerWidth <= 1024);\n    };\n    window.addEventListener(\"resize\", handleResize);\n    return () => window.removeEventListener(\"resize\", handleResize);\n  }, []);\n  return {\n    isMobile,\n    isTablet\n  };\n};\n_s(useResponsive, \"F4WheOdlzMN19HQQ5aQkLkav7bY=\");\nexport default function Hero() {\n  _s2();\n  const {\n    isMobile,\n    isTablet\n  } = useResponsive();\n  const [resumeModalOpen, setResumeModalOpen] = useState(false);\n  const handleOpenResumeModal = () => {\n    setResumeModalOpen(true);\n  };\n  const handleCloseResumeModal = () => {\n    setResumeModalOpen(false);\n  };\n  const handleDownload = () => {\n    const link = document.createElement(\"a\");\n    link.href = \"/Abhishek_DS_Resume.pdf\";\n    link.download = \"Abhishek_DS_Resume.pdf\";\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"hero\" // 🔥 This makes your logo link scroll to here\n    ,\n    style: {\n      minHeight: \"100vh\",\n      width: \"100vw\",\n      display: \"flex\",\n      alignItems: \"center\",\n      justifyContent: \"space-between\",\n      backgroundColor: \"#000\",\n      color: \"#fff\",\n      paddingTop: isMobile ? \"60px\" : \"80px\",\n      padding: isMobile ? \"60px 20px 20px 20px\" : \"80px 0 0 0\",\n      boxSizing: \"border-box\",\n      overflow: \"hidden\",\n      flexDirection: isMobile ? \"column\" : \"row\",\n      flexWrap: \"wrap\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: \"1 1 50%\",\n        display: \"flex\",\n        alignItems: \"flex-start\",\n        justifyContent: \"flex-start\",\n        paddingLeft: \"0\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: avatarUrl,\n        alt: \"Avatar\",\n        style: {\n          height: \"90vh\",\n          width: \"auto\",\n          objectFit: \"contain\",\n          filter: \"grayscale(100%) drop-shadow(0 0 35px #e7e6e254)\",\n          borderRadius: \"20px\",\n          transition: \"all 0.3s ease-in-out\",\n          cursor: \"default\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: \"1 1 50%\",\n        display: \"flex\",\n        flexDirection: \"column\",\n        justifyContent: \"center\",\n        alignItems: \"flex-end\",\n        paddingRight: \"5vw\",\n        // aligns text cleanly to the right\n        boxSizing: \"border-box\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: \"right\",\n          maxWidth: \"100%\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            fontSize: \"3.2rem\",\n            fontWeight: \"bold\",\n            lineHeight: \"1.2\",\n            marginBottom: \"1rem\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              display: \"block\"\n            },\n            children: \"Hi,\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"I'm \", /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: \"#FFD700\"\n              },\n              children: \"Abhishek D S\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TypeAnimation, {\n          sequence: [\"Frontend Developer\", 2000, \"React Enthusiast\", 2000, \"UI/UX Explorer\", 2000],\n          speed: 50,\n          wrapper: \"span\",\n          repeat: Infinity,\n          style: {\n            fontSize: \"1.6rem\",\n            color: \"#fff\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: \"2rem\"\n          },\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleOpenResumeModal,\n            style: {\n              fontFamily: \"'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif\",\n              padding: \"14px 28px\",\n              fontSize: \"1.1rem\",\n              fontWeight: 600,\n              letterSpacing: \"-0.01em\",\n              color: \"#000\",\n              backgroundColor: \"#FFD700\",\n              border: \"2px solid #FFD700\",\n              borderRadius: \"12px\",\n              cursor: \"pointer\",\n              transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n              textDecoration: \"none\",\n              display: \"inline-block\",\n              boxShadow: \"0 4px 14px 0 rgba(255, 215, 0, 0.3)\"\n            },\n            onMouseEnter: e => {\n              e.target.style.backgroundColor = \"transparent\";\n              e.target.style.color = \"#FFD700\";\n              e.target.style.transform = \"translateY(-2px) scale(1.02)\";\n              e.target.style.boxShadow = \"0 8px 25px 0 rgba(255, 215, 0, 0.4)\";\n            },\n            onMouseLeave: e => {\n              e.target.style.backgroundColor = \"#FFD700\";\n              e.target.style.color = \"#000\";\n              e.target.style.transform = \"translateY(0px) scale(1)\";\n              e.target.style.boxShadow = \"0 4px 14px 0 rgba(255, 215, 0, 0.3)\";\n            },\n            children: \"View Resume\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      open: resumeModalOpen,\n      onClose: handleCloseResumeModal,\n      sx: {\n        display: \"flex\",\n        alignItems: \"center\",\n        justifyContent: \"center\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: \"relative\",\n          width: \"95vw\",\n          height: \"95vh\",\n          maxWidth: \"1000px\",\n          maxHeight: \"900px\",\n          bgcolor: \"#fff\",\n          borderRadius: \"12px\",\n          boxShadow: \"0 25px 50px -12px rgba(0, 0, 0, 0.8)\",\n          overflow: \"hidden\",\n          display: \"flex\",\n          flexDirection: \"column\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleCloseResumeModal,\n          sx: {\n            position: \"absolute\",\n            top: 8,\n            left: 8,\n            zIndex: 1000,\n            color: \"#000\",\n            bgcolor: \"rgba(255, 255, 255, 0.9)\",\n            \"&:hover\": {\n              bgcolor: \"rgba(255, 255, 255, 1)\",\n              transform: \"scale(1.1)\"\n            },\n            transition: \"all 0.2s ease\"\n          },\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: \"100%\",\n            height: \"calc(100% - 80px)\",\n            backgroundColor: \"#fff\",\n            display: \"flex\",\n            justifyContent: \"center\",\n            alignItems: \"flex-start\",\n            overflow: \"auto\",\n            padding: \"10px\"\n          },\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: resumeImage,\n            alt: \"Abhishek DS Resume\",\n            style: {\n              maxWidth: \"100%\",\n              height: \"auto\",\n              boxShadow: \"0 4px 8px rgba(0,0,0,0.1)\",\n              borderRadius: \"4px\"\n            },\n            onLoad: () => console.log(\"Resume image loaded successfully\"),\n            onError: e => {\n              console.log(\"Resume image failed to load\");\n              e.target.style.display = \"none\";\n              const fallback = document.createElement(\"div\");\n              fallback.style.textAlign = \"center\";\n              fallback.style.color = \"#666\";\n              fallback.style.padding = \"20px\";\n              fallback.innerHTML = `\n                  <h3 style=\"color: #f1c40f; margin-bottom: 20px;\">Resume Preview</h3>\n                  <p>Resume image could not be loaded.</p>\n                  <p><a href=\"/resume.jpg\" target=\"_blank\" style=\"color: #f1c40f; text-decoration: underline;\">Click here to view resume</a></p>\n                `;\n              e.target.parentNode.appendChild(fallback);\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            height: \"80px\",\n            display: \"flex\",\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            bgcolor: \"#f5f5f5\",\n            borderTop: \"1px solid #e0e0e0\"\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            onClick: handleDownload,\n            startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 26\n            }, this),\n            sx: {\n              fontFamily: \"'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif\",\n              fontSize: \"1rem\",\n              fontWeight: 600,\n              letterSpacing: \"-0.01em\",\n              px: 4,\n              py: 1.5,\n              bgcolor: \"#FFD700\",\n              color: \"#000\",\n              borderRadius: \"8px\",\n              textTransform: \"none\",\n              boxShadow: \"0 2px 8px 0 rgba(255, 215, 0, 0.3)\",\n              \"&:hover\": {\n                bgcolor: \"#FFC700\",\n                transform: \"translateY(-1px)\",\n                boxShadow: \"0 4px 12px 0 rgba(255, 215, 0, 0.4)\"\n              },\n              transition: \"all 0.2s ease\"\n            },\n            children: \"Download Resume\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 5\n  }, this);\n}\n_s2(Hero, \"+TWjhlsYPAsXcWhiWKWVImmriDQ=\", false, function () {\n  return [useResponsive];\n});\n_c = Hero;\nvar _c;\n$RefreshReg$(_c, \"Hero\");", "map": {"version": 3, "names": ["React", "useState", "TypeAnimation", "Modal", "Box", "IconButton", "<PERSON><PERSON>", "CloseIcon", "DownloadIcon", "resumeImage", "jsxDEV", "_jsxDEV", "avatarUrl", "process", "env", "PUBLIC_URL", "useResponsive", "_s", "isMobile", "setIsMobile", "window", "innerWidth", "isTablet", "setIsTablet", "useEffect", "handleResize", "addEventListener", "removeEventListener", "Hero", "_s2", "resumeModalOpen", "setResumeModalOpen", "handleOpenResumeModal", "handleCloseResumeModal", "handleDownload", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "id", "style", "minHeight", "width", "display", "alignItems", "justifyContent", "backgroundColor", "color", "paddingTop", "padding", "boxSizing", "overflow", "flexDirection", "flexWrap", "children", "flex", "paddingLeft", "src", "alt", "height", "objectFit", "filter", "borderRadius", "transition", "cursor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "paddingRight", "textAlign", "max<PERSON><PERSON><PERSON>", "fontSize", "fontWeight", "lineHeight", "marginBottom", "sequence", "speed", "wrapper", "repeat", "Infinity", "marginTop", "onClick", "fontFamily", "letterSpacing", "border", "textDecoration", "boxShadow", "onMouseEnter", "e", "target", "transform", "onMouseLeave", "open", "onClose", "sx", "position", "maxHeight", "bgcolor", "top", "left", "zIndex", "onLoad", "console", "log", "onError", "fallback", "innerHTML", "parentNode", "borderTop", "startIcon", "px", "py", "textTransform", "_c", "$RefreshReg$"], "sources": ["D:/Desktop/projects/port1/port/frontend/src/components/Hero.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { TypeAnimation } from \"react-type-animation\";\r\nimport { Modal, Box, IconButton, Button } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport DownloadIcon from \"@mui/icons-material/Download\";\r\nimport resumeImage from \"../assets/resume.jpg\";\r\n\r\nconst avatarUrl = process.env.PUBLIC_URL + \"/logo2.png\";\r\n\r\n// Responsive breakpoints\r\nconst useResponsive = () => {\r\n  const [isMobile, setIsMobile] = React.useState(window.innerWidth <= 768);\r\n  const [isTablet, setIsTablet] = React.useState(\r\n    window.innerWidth > 768 && window.innerWidth <= 1024\r\n  );\r\n\r\n  React.useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsMobile(window.innerWidth <= 768);\r\n      setIsTablet(window.innerWidth > 768 && window.innerWidth <= 1024);\r\n    };\r\n\r\n    window.addEventListener(\"resize\", handleResize);\r\n    return () => window.removeEventListener(\"resize\", handleResize);\r\n  }, []);\r\n\r\n  return { isMobile, isTablet };\r\n};\r\n\r\nexport default function Hero() {\r\n  const { isMobile, isTablet } = useResponsive();\r\n  const [resumeModalOpen, setResumeModalOpen] = useState(false);\r\n\r\n  const handleOpenResumeModal = () => {\r\n    setResumeModalOpen(true);\r\n  };\r\n\r\n  const handleCloseResumeModal = () => {\r\n    setResumeModalOpen(false);\r\n  };\r\n\r\n  const handleDownload = () => {\r\n    const link = document.createElement(\"a\");\r\n    link.href = \"/Abhishek_DS_Resume.pdf\";\r\n    link.download = \"Abhishek_DS_Resume.pdf\";\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n  };\r\n\r\n  return (\r\n    <section\r\n      id=\"hero\" // 🔥 This makes your logo link scroll to here\r\n      style={{\r\n        minHeight: \"100vh\",\r\n        width: \"100vw\",\r\n        display: \"flex\",\r\n        alignItems: \"center\",\r\n        justifyContent: \"space-between\",\r\n        backgroundColor: \"#000\",\r\n        color: \"#fff\",\r\n        paddingTop: isMobile ? \"60px\" : \"80px\",\r\n        padding: isMobile ? \"60px 20px 20px 20px\" : \"80px 0 0 0\",\r\n        boxSizing: \"border-box\",\r\n        overflow: \"hidden\",\r\n        flexDirection: isMobile ? \"column\" : \"row\",\r\n        flexWrap: \"wrap\",\r\n      }}\r\n    >\r\n      {/* Avatar Section */}\r\n      <div\r\n        style={{\r\n          flex: \"1 1 50%\",\r\n          display: \"flex\",\r\n          alignItems: \"flex-start\",\r\n          justifyContent: \"flex-start\",\r\n          paddingLeft: \"0\",\r\n        }}\r\n      >\r\n        <img\r\n          src={avatarUrl}\r\n          alt=\"Avatar\"\r\n          style={{\r\n            height: \"90vh\",\r\n            width: \"auto\",\r\n            objectFit: \"contain\",\r\n            filter: \"grayscale(100%) drop-shadow(0 0 35px #e7e6e254)\",\r\n            borderRadius: \"20px\",\r\n            transition: \"all 0.3s ease-in-out\",\r\n            cursor: \"default\",\r\n          }}\r\n        />\r\n      </div>\r\n\r\n      {/* Text Section */}\r\n      <div\r\n        style={{\r\n          flex: \"1 1 50%\",\r\n          display: \"flex\",\r\n          flexDirection: \"column\",\r\n          justifyContent: \"center\",\r\n          alignItems: \"flex-end\",\r\n          paddingRight: \"5vw\", // aligns text cleanly to the right\r\n          boxSizing: \"border-box\",\r\n        }}\r\n      >\r\n        <div style={{ textAlign: \"right\", maxWidth: \"100%\" }}>\r\n          <h1\r\n            style={{\r\n              fontSize: \"3.2rem\",\r\n              fontWeight: \"bold\",\r\n              lineHeight: \"1.2\",\r\n              marginBottom: \"1rem\",\r\n            }}\r\n          >\r\n            <span style={{ display: \"block\" }}>Hi,</span>\r\n            <span>\r\n              I'm <span style={{ color: \"#FFD700\" }}>Abhishek D S</span>\r\n            </span>\r\n          </h1>\r\n\r\n          <TypeAnimation\r\n            sequence={[\r\n              \"Frontend Developer\",\r\n              2000,\r\n              \"React Enthusiast\",\r\n              2000,\r\n              \"UI/UX Explorer\",\r\n              2000,\r\n            ]}\r\n            speed={50}\r\n            wrapper=\"span\"\r\n            repeat={Infinity}\r\n            style={{\r\n              fontSize: \"1.6rem\",\r\n              color: \"#fff\",\r\n            }}\r\n          />\r\n\r\n          {/* View Resume Button */}\r\n          <div style={{ marginTop: \"2rem\" }}>\r\n            <button\r\n              onClick={handleOpenResumeModal}\r\n              style={{\r\n                fontFamily:\r\n                  \"'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif\",\r\n                padding: \"14px 28px\",\r\n                fontSize: \"1.1rem\",\r\n                fontWeight: 600,\r\n                letterSpacing: \"-0.01em\",\r\n                color: \"#000\",\r\n                backgroundColor: \"#FFD700\",\r\n                border: \"2px solid #FFD700\",\r\n                borderRadius: \"12px\",\r\n                cursor: \"pointer\",\r\n                transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\r\n                textDecoration: \"none\",\r\n                display: \"inline-block\",\r\n                boxShadow: \"0 4px 14px 0 rgba(255, 215, 0, 0.3)\",\r\n              }}\r\n              onMouseEnter={(e) => {\r\n                e.target.style.backgroundColor = \"transparent\";\r\n                e.target.style.color = \"#FFD700\";\r\n                e.target.style.transform = \"translateY(-2px) scale(1.02)\";\r\n                e.target.style.boxShadow =\r\n                  \"0 8px 25px 0 rgba(255, 215, 0, 0.4)\";\r\n              }}\r\n              onMouseLeave={(e) => {\r\n                e.target.style.backgroundColor = \"#FFD700\";\r\n                e.target.style.color = \"#000\";\r\n                e.target.style.transform = \"translateY(0px) scale(1)\";\r\n                e.target.style.boxShadow =\r\n                  \"0 4px 14px 0 rgba(255, 215, 0, 0.3)\";\r\n              }}\r\n            >\r\n              View Resume\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Resume Modal */}\r\n      <Modal\r\n        open={resumeModalOpen}\r\n        onClose={handleCloseResumeModal}\r\n        sx={{\r\n          display: \"flex\",\r\n          alignItems: \"center\",\r\n          justifyContent: \"center\",\r\n        }}\r\n      >\r\n        <Box\r\n          sx={{\r\n            position: \"relative\",\r\n            width: \"95vw\",\r\n            height: \"95vh\",\r\n            maxWidth: \"1000px\",\r\n            maxHeight: \"900px\",\r\n            bgcolor: \"#fff\",\r\n            borderRadius: \"12px\",\r\n            boxShadow: \"0 25px 50px -12px rgba(0, 0, 0, 0.8)\",\r\n            overflow: \"hidden\",\r\n            display: \"flex\",\r\n            flexDirection: \"column\",\r\n          }}\r\n        >\r\n          {/* Close Button */}\r\n          <IconButton\r\n            onClick={handleCloseResumeModal}\r\n            sx={{\r\n              position: \"absolute\",\r\n              top: 8,\r\n              left: 8,\r\n              zIndex: 1000,\r\n              color: \"#000\",\r\n              bgcolor: \"rgba(255, 255, 255, 0.9)\",\r\n              \"&:hover\": {\r\n                bgcolor: \"rgba(255, 255, 255, 1)\",\r\n                transform: \"scale(1.1)\",\r\n              },\r\n              transition: \"all 0.2s ease\",\r\n            }}\r\n          >\r\n            <CloseIcon />\r\n          </IconButton>\r\n\r\n          {/* Resume Image Viewer */}\r\n          <div\r\n            style={{\r\n              width: \"100%\",\r\n              height: \"calc(100% - 80px)\",\r\n              backgroundColor: \"#fff\",\r\n              display: \"flex\",\r\n              justifyContent: \"center\",\r\n              alignItems: \"flex-start\",\r\n              overflow: \"auto\",\r\n              padding: \"10px\",\r\n            }}\r\n          >\r\n            <img\r\n              src={resumeImage}\r\n              alt=\"Abhishek DS Resume\"\r\n              style={{\r\n                maxWidth: \"100%\",\r\n                height: \"auto\",\r\n                boxShadow: \"0 4px 8px rgba(0,0,0,0.1)\",\r\n                borderRadius: \"4px\",\r\n              }}\r\n              onLoad={() => console.log(\"Resume image loaded successfully\")}\r\n              onError={(e) => {\r\n                console.log(\"Resume image failed to load\");\r\n                e.target.style.display = \"none\";\r\n                const fallback = document.createElement(\"div\");\r\n                fallback.style.textAlign = \"center\";\r\n                fallback.style.color = \"#666\";\r\n                fallback.style.padding = \"20px\";\r\n                fallback.innerHTML = `\r\n                  <h3 style=\"color: #f1c40f; margin-bottom: 20px;\">Resume Preview</h3>\r\n                  <p>Resume image could not be loaded.</p>\r\n                  <p><a href=\"/resume.jpg\" target=\"_blank\" style=\"color: #f1c40f; text-decoration: underline;\">Click here to view resume</a></p>\r\n                `;\r\n                e.target.parentNode.appendChild(fallback);\r\n              }}\r\n            />\r\n          </div>\r\n\r\n          {/* Download Button */}\r\n          <Box\r\n            sx={{\r\n              height: \"80px\",\r\n              display: \"flex\",\r\n              justifyContent: \"center\",\r\n              alignItems: \"center\",\r\n              bgcolor: \"#f5f5f5\",\r\n              borderTop: \"1px solid #e0e0e0\",\r\n            }}\r\n          >\r\n            <Button\r\n              onClick={handleDownload}\r\n              startIcon={<DownloadIcon />}\r\n              sx={{\r\n                fontFamily:\r\n                  \"'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif\",\r\n                fontSize: \"1rem\",\r\n                fontWeight: 600,\r\n                letterSpacing: \"-0.01em\",\r\n                px: 4,\r\n                py: 1.5,\r\n                bgcolor: \"#FFD700\",\r\n                color: \"#000\",\r\n                borderRadius: \"8px\",\r\n                textTransform: \"none\",\r\n                boxShadow: \"0 2px 8px 0 rgba(255, 215, 0, 0.3)\",\r\n                \"&:hover\": {\r\n                  bgcolor: \"#FFC700\",\r\n                  transform: \"translateY(-1px)\",\r\n                  boxShadow: \"0 4px 12px 0 rgba(255, 215, 0, 0.4)\",\r\n                },\r\n                transition: \"all 0.2s ease\",\r\n              }}\r\n            >\r\n              Download Resume\r\n            </Button>\r\n          </Box>\r\n        </Box>\r\n      </Modal>\r\n    </section>\r\n  );\r\n}\r\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,KAAK,EAAEC,GAAG,EAAEC,UAAU,EAAEC,MAAM,QAAQ,eAAe;AAC9D,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,WAAW,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,GAAG,YAAY;;AAEvD;AACA,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,KAAK,CAACC,QAAQ,CAACmB,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;EACxE,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,KAAK,CAACC,QAAQ,CAC5CmB,MAAM,CAACC,UAAU,GAAG,GAAG,IAAID,MAAM,CAACC,UAAU,IAAI,IAClD,CAAC;EAEDrB,KAAK,CAACwB,SAAS,CAAC,MAAM;IACpB,MAAMC,YAAY,GAAGA,CAAA,KAAM;MACzBN,WAAW,CAACC,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;MACrCE,WAAW,CAACH,MAAM,CAACC,UAAU,GAAG,GAAG,IAAID,MAAM,CAACC,UAAU,IAAI,IAAI,CAAC;IACnE,CAAC;IAEDD,MAAM,CAACM,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAML,MAAM,CAACO,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IAAEP,QAAQ;IAAEI;EAAS,CAAC;AAC/B,CAAC;AAACL,EAAA,CAjBID,aAAa;AAmBnB,eAAe,SAASY,IAAIA,CAAA,EAAG;EAAAC,GAAA;EAC7B,MAAM;IAAEX,QAAQ;IAAEI;EAAS,CAAC,GAAGN,aAAa,CAAC,CAAC;EAC9C,MAAM,CAACc,eAAe,EAAEC,kBAAkB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAE7D,MAAM+B,qBAAqB,GAAGA,CAAA,KAAM;IAClCD,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAME,sBAAsB,GAAGA,CAAA,KAAM;IACnCF,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;EAED,MAAMG,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAG,yBAAyB;IACrCH,IAAI,CAACI,QAAQ,GAAG,wBAAwB;IACxCH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;IAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;IACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;EACjC,CAAC;EAED,oBACExB,OAAA;IACEiC,EAAE,EAAC,MAAM,CAAC;IAAA;IACVC,KAAK,EAAE;MACLC,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,OAAO;MACdC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,eAAe;MAC/BC,eAAe,EAAE,MAAM;MACvBC,KAAK,EAAE,MAAM;MACbC,UAAU,EAAEnC,QAAQ,GAAG,MAAM,GAAG,MAAM;MACtCoC,OAAO,EAAEpC,QAAQ,GAAG,qBAAqB,GAAG,YAAY;MACxDqC,SAAS,EAAE,YAAY;MACvBC,QAAQ,EAAE,QAAQ;MAClBC,aAAa,EAAEvC,QAAQ,GAAG,QAAQ,GAAG,KAAK;MAC1CwC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,gBAGFhD,OAAA;MACEkC,KAAK,EAAE;QACLe,IAAI,EAAE,SAAS;QACfZ,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,YAAY;QACxBC,cAAc,EAAE,YAAY;QAC5BW,WAAW,EAAE;MACf,CAAE;MAAAF,QAAA,eAEFhD,OAAA;QACEmD,GAAG,EAAElD,SAAU;QACfmD,GAAG,EAAC,QAAQ;QACZlB,KAAK,EAAE;UACLmB,MAAM,EAAE,MAAM;UACdjB,KAAK,EAAE,MAAM;UACbkB,SAAS,EAAE,SAAS;UACpBC,MAAM,EAAE,iDAAiD;UACzDC,YAAY,EAAE,MAAM;UACpBC,UAAU,EAAE,sBAAsB;UAClCC,MAAM,EAAE;QACV;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGN9D,OAAA;MACEkC,KAAK,EAAE;QACLe,IAAI,EAAE,SAAS;QACfZ,OAAO,EAAE,MAAM;QACfS,aAAa,EAAE,QAAQ;QACvBP,cAAc,EAAE,QAAQ;QACxBD,UAAU,EAAE,UAAU;QACtByB,YAAY,EAAE,KAAK;QAAE;QACrBnB,SAAS,EAAE;MACb,CAAE;MAAAI,QAAA,eAEFhD,OAAA;QAAKkC,KAAK,EAAE;UAAE8B,SAAS,EAAE,OAAO;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAjB,QAAA,gBACnDhD,OAAA;UACEkC,KAAK,EAAE;YACLgC,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE,MAAM;YAClBC,UAAU,EAAE,KAAK;YACjBC,YAAY,EAAE;UAChB,CAAE;UAAArB,QAAA,gBAEFhD,OAAA;YAAMkC,KAAK,EAAE;cAAEG,OAAO,EAAE;YAAQ,CAAE;YAAAW,QAAA,EAAC;UAAG;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7C9D,OAAA;YAAAgD,QAAA,GAAM,MACA,eAAAhD,OAAA;cAAMkC,KAAK,EAAE;gBAAEO,KAAK,EAAE;cAAU,CAAE;cAAAO,QAAA,EAAC;YAAY;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEL9D,OAAA,CAACT,aAAa;UACZ+E,QAAQ,EAAE,CACR,oBAAoB,EACpB,IAAI,EACJ,kBAAkB,EAClB,IAAI,EACJ,gBAAgB,EAChB,IAAI,CACJ;UACFC,KAAK,EAAE,EAAG;UACVC,OAAO,EAAC,MAAM;UACdC,MAAM,EAAEC,QAAS;UACjBxC,KAAK,EAAE;YACLgC,QAAQ,EAAE,QAAQ;YAClBzB,KAAK,EAAE;UACT;QAAE;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGF9D,OAAA;UAAKkC,KAAK,EAAE;YAAEyC,SAAS,EAAE;UAAO,CAAE;UAAA3B,QAAA,eAChChD,OAAA;YACE4E,OAAO,EAAEvD,qBAAsB;YAC/Ba,KAAK,EAAE;cACL2C,UAAU,EACR,sFAAsF;cACxFlC,OAAO,EAAE,WAAW;cACpBuB,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE,GAAG;cACfW,aAAa,EAAE,SAAS;cACxBrC,KAAK,EAAE,MAAM;cACbD,eAAe,EAAE,SAAS;cAC1BuC,MAAM,EAAE,mBAAmB;cAC3BvB,YAAY,EAAE,MAAM;cACpBE,MAAM,EAAE,SAAS;cACjBD,UAAU,EAAE,uCAAuC;cACnDuB,cAAc,EAAE,MAAM;cACtB3C,OAAO,EAAE,cAAc;cACvB4C,SAAS,EAAE;YACb,CAAE;YACFC,YAAY,EAAGC,CAAC,IAAK;cACnBA,CAAC,CAACC,MAAM,CAAClD,KAAK,CAACM,eAAe,GAAG,aAAa;cAC9C2C,CAAC,CAACC,MAAM,CAAClD,KAAK,CAACO,KAAK,GAAG,SAAS;cAChC0C,CAAC,CAACC,MAAM,CAAClD,KAAK,CAACmD,SAAS,GAAG,8BAA8B;cACzDF,CAAC,CAACC,MAAM,CAAClD,KAAK,CAAC+C,SAAS,GACtB,qCAAqC;YACzC,CAAE;YACFK,YAAY,EAAGH,CAAC,IAAK;cACnBA,CAAC,CAACC,MAAM,CAAClD,KAAK,CAACM,eAAe,GAAG,SAAS;cAC1C2C,CAAC,CAACC,MAAM,CAAClD,KAAK,CAACO,KAAK,GAAG,MAAM;cAC7B0C,CAAC,CAACC,MAAM,CAAClD,KAAK,CAACmD,SAAS,GAAG,0BAA0B;cACrDF,CAAC,CAACC,MAAM,CAAClD,KAAK,CAAC+C,SAAS,GACtB,qCAAqC;YACzC,CAAE;YAAAjC,QAAA,EACH;UAED;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9D,OAAA,CAACR,KAAK;MACJ+F,IAAI,EAAEpE,eAAgB;MACtBqE,OAAO,EAAElE,sBAAuB;MAChCmE,EAAE,EAAE;QACFpD,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE;MAClB,CAAE;MAAAS,QAAA,eAEFhD,OAAA,CAACP,GAAG;QACFgG,EAAE,EAAE;UACFC,QAAQ,EAAE,UAAU;UACpBtD,KAAK,EAAE,MAAM;UACbiB,MAAM,EAAE,MAAM;UACdY,QAAQ,EAAE,QAAQ;UAClB0B,SAAS,EAAE,OAAO;UAClBC,OAAO,EAAE,MAAM;UACfpC,YAAY,EAAE,MAAM;UACpByB,SAAS,EAAE,sCAAsC;UACjDpC,QAAQ,EAAE,QAAQ;UAClBR,OAAO,EAAE,MAAM;UACfS,aAAa,EAAE;QACjB,CAAE;QAAAE,QAAA,gBAGFhD,OAAA,CAACN,UAAU;UACTkF,OAAO,EAAEtD,sBAAuB;UAChCmE,EAAE,EAAE;YACFC,QAAQ,EAAE,UAAU;YACpBG,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,MAAM,EAAE,IAAI;YACZtD,KAAK,EAAE,MAAM;YACbmD,OAAO,EAAE,0BAA0B;YACnC,SAAS,EAAE;cACTA,OAAO,EAAE,wBAAwB;cACjCP,SAAS,EAAE;YACb,CAAC;YACD5B,UAAU,EAAE;UACd,CAAE;UAAAT,QAAA,eAEFhD,OAAA,CAACJ,SAAS;YAAA+D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGb9D,OAAA;UACEkC,KAAK,EAAE;YACLE,KAAK,EAAE,MAAM;YACbiB,MAAM,EAAE,mBAAmB;YAC3Bb,eAAe,EAAE,MAAM;YACvBH,OAAO,EAAE,MAAM;YACfE,cAAc,EAAE,QAAQ;YACxBD,UAAU,EAAE,YAAY;YACxBO,QAAQ,EAAE,MAAM;YAChBF,OAAO,EAAE;UACX,CAAE;UAAAK,QAAA,eAEFhD,OAAA;YACEmD,GAAG,EAAErD,WAAY;YACjBsD,GAAG,EAAC,oBAAoB;YACxBlB,KAAK,EAAE;cACL+B,QAAQ,EAAE,MAAM;cAChBZ,MAAM,EAAE,MAAM;cACd4B,SAAS,EAAE,2BAA2B;cACtCzB,YAAY,EAAE;YAChB,CAAE;YACFwC,MAAM,EAAEA,CAAA,KAAMC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAE;YAC9DC,OAAO,EAAGhB,CAAC,IAAK;cACdc,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;cAC1Cf,CAAC,CAACC,MAAM,CAAClD,KAAK,CAACG,OAAO,GAAG,MAAM;cAC/B,MAAM+D,QAAQ,GAAG3E,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;cAC9C0E,QAAQ,CAAClE,KAAK,CAAC8B,SAAS,GAAG,QAAQ;cACnCoC,QAAQ,CAAClE,KAAK,CAACO,KAAK,GAAG,MAAM;cAC7B2D,QAAQ,CAAClE,KAAK,CAACS,OAAO,GAAG,MAAM;cAC/ByD,QAAQ,CAACC,SAAS,GAAG;AACrC;AACA;AACA;AACA,iBAAiB;cACDlB,CAAC,CAACC,MAAM,CAACkB,UAAU,CAACxE,WAAW,CAACsE,QAAQ,CAAC;YAC3C;UAAE;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN9D,OAAA,CAACP,GAAG;UACFgG,EAAE,EAAE;YACFpC,MAAM,EAAE,MAAM;YACdhB,OAAO,EAAE,MAAM;YACfE,cAAc,EAAE,QAAQ;YACxBD,UAAU,EAAE,QAAQ;YACpBsD,OAAO,EAAE,SAAS;YAClBW,SAAS,EAAE;UACb,CAAE;UAAAvD,QAAA,eAEFhD,OAAA,CAACL,MAAM;YACLiF,OAAO,EAAErD,cAAe;YACxBiF,SAAS,eAAExG,OAAA,CAACH,YAAY;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5B2B,EAAE,EAAE;cACFZ,UAAU,EACR,0EAA0E;cAC5EX,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE,GAAG;cACfW,aAAa,EAAE,SAAS;cACxB2B,EAAE,EAAE,CAAC;cACLC,EAAE,EAAE,GAAG;cACPd,OAAO,EAAE,SAAS;cAClBnD,KAAK,EAAE,MAAM;cACbe,YAAY,EAAE,KAAK;cACnBmD,aAAa,EAAE,MAAM;cACrB1B,SAAS,EAAE,oCAAoC;cAC/C,SAAS,EAAE;gBACTW,OAAO,EAAE,SAAS;gBAClBP,SAAS,EAAE,kBAAkB;gBAC7BJ,SAAS,EAAE;cACb,CAAC;cACDxB,UAAU,EAAE;YACd,CAAE;YAAAT,QAAA,EACH;UAED;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd;AAAC5C,GAAA,CAvRuBD,IAAI;EAAA,QACKZ,aAAa;AAAA;AAAAuG,EAAA,GADtB3F,IAAI;AAAA,IAAA2F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}