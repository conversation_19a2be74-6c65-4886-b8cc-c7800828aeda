{"ast": null, "code": "import { frame } from 'motion-dom';\nimport { makeUseVisualState } from '../../motion/utils/use-visual-state.mjs';\nimport { transformProps } from '../html/utils/keys-transform.mjs';\nimport { buildSVGAttrs } from './utils/build-attrs.mjs';\nimport { createSvgRenderState } from './utils/create-render-state.mjs';\nimport { isSVGTag } from './utils/is-svg-tag.mjs';\nimport { updateSVGDimensions } from './utils/measure.mjs';\nimport { renderSVG } from './utils/render.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\nconst layoutProps = [\"x\", \"y\", \"width\", \"height\", \"cx\", \"cy\", \"r\"];\nconst svgMotionConfig = {\n  useVisualState: makeUseVisualState({\n    scrapeMotionValuesFromProps: scrapeMotionValuesFromProps,\n    createRenderState: createSvgRenderState,\n    onUpdate: ({\n      props,\n      prevProps,\n      current,\n      renderState,\n      latestValues\n    }) => {\n      if (!current) return;\n      let hasTransform = !!props.drag;\n      if (!hasTransform) {\n        for (const key in latestValues) {\n          if (transformProps.has(key)) {\n            hasTransform = true;\n            break;\n          }\n        }\n      }\n      if (!hasTransform) return;\n      let needsMeasure = !prevProps;\n      if (prevProps) {\n        /**\n         * Check the layout props for changes, if any are found we need to\n         * measure the element again.\n         */\n        for (let i = 0; i < layoutProps.length; i++) {\n          const key = layoutProps[i];\n          if (props[key] !== prevProps[key]) {\n            needsMeasure = true;\n          }\n        }\n      }\n      if (!needsMeasure) return;\n      frame.read(() => {\n        updateSVGDimensions(current, renderState);\n        frame.render(() => {\n          buildSVGAttrs(renderState, latestValues, isSVGTag(current.tagName), props.transformTemplate);\n          renderSVG(current, renderState);\n        });\n      });\n    }\n  })\n};\nexport { svgMotionConfig };", "map": {"version": 3, "names": ["frame", "makeUseVisualState", "transformProps", "buildSVGAttrs", "createSvgRenderState", "isSVGTag", "updateSVGDimensions", "renderSVG", "scrapeMotionValuesFromProps", "layoutProps", "svgMotionConfig", "useVisualState", "createRenderState", "onUpdate", "props", "prevProps", "current", "renderState", "latestValues", "hasTransform", "drag", "key", "has", "needsMeasure", "i", "length", "read", "render", "tagName", "transformTemplate"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/framer-motion/dist/es/render/svg/config-motion.mjs"], "sourcesContent": ["import { frame } from 'motion-dom';\nimport { makeUseVisualState } from '../../motion/utils/use-visual-state.mjs';\nimport { transformProps } from '../html/utils/keys-transform.mjs';\nimport { buildSVGAttrs } from './utils/build-attrs.mjs';\nimport { createSvgRenderState } from './utils/create-render-state.mjs';\nimport { isSVGTag } from './utils/is-svg-tag.mjs';\nimport { updateSVGDimensions } from './utils/measure.mjs';\nimport { renderSVG } from './utils/render.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\n\nconst layoutProps = [\"x\", \"y\", \"width\", \"height\", \"cx\", \"cy\", \"r\"];\nconst svgMotionConfig = {\n    useVisualState: makeUseVisualState({\n        scrapeMotionValuesFromProps: scrapeMotionValuesFromProps,\n        createRenderState: createSvgRenderState,\n        onUpdate: ({ props, prevProps, current, renderState, latestValues, }) => {\n            if (!current)\n                return;\n            let hasTransform = !!props.drag;\n            if (!hasTransform) {\n                for (const key in latestValues) {\n                    if (transformProps.has(key)) {\n                        hasTransform = true;\n                        break;\n                    }\n                }\n            }\n            if (!hasTransform)\n                return;\n            let needsMeasure = !prevProps;\n            if (prevProps) {\n                /**\n                 * Check the layout props for changes, if any are found we need to\n                 * measure the element again.\n                 */\n                for (let i = 0; i < layoutProps.length; i++) {\n                    const key = layoutProps[i];\n                    if (props[key] !==\n                        prevProps[key]) {\n                        needsMeasure = true;\n                    }\n                }\n            }\n            if (!needsMeasure)\n                return;\n            frame.read(() => {\n                updateSVGDimensions(current, renderState);\n                frame.render(() => {\n                    buildSVGAttrs(renderState, latestValues, isSVGTag(current.tagName), props.transformTemplate);\n                    renderSVG(current, renderState);\n                });\n            });\n        },\n    }),\n};\n\nexport { svgMotionConfig };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,YAAY;AAClC,SAASC,kBAAkB,QAAQ,yCAAyC;AAC5E,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,oBAAoB,QAAQ,iCAAiC;AACtE,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,mBAAmB,QAAQ,qBAAqB;AACzD,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,2BAA2B,QAAQ,kCAAkC;AAE9E,MAAMC,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC;AAClE,MAAMC,eAAe,GAAG;EACpBC,cAAc,EAAEV,kBAAkB,CAAC;IAC/BO,2BAA2B,EAAEA,2BAA2B;IACxDI,iBAAiB,EAAER,oBAAoB;IACvCS,QAAQ,EAAEA,CAAC;MAAEC,KAAK;MAAEC,SAAS;MAAEC,OAAO;MAAEC,WAAW;MAAEC;IAAc,CAAC,KAAK;MACrE,IAAI,CAACF,OAAO,EACR;MACJ,IAAIG,YAAY,GAAG,CAAC,CAACL,KAAK,CAACM,IAAI;MAC/B,IAAI,CAACD,YAAY,EAAE;QACf,KAAK,MAAME,GAAG,IAAIH,YAAY,EAAE;UAC5B,IAAIhB,cAAc,CAACoB,GAAG,CAACD,GAAG,CAAC,EAAE;YACzBF,YAAY,GAAG,IAAI;YACnB;UACJ;QACJ;MACJ;MACA,IAAI,CAACA,YAAY,EACb;MACJ,IAAII,YAAY,GAAG,CAACR,SAAS;MAC7B,IAAIA,SAAS,EAAE;QACX;AAChB;AACA;AACA;QACgB,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,WAAW,CAACgB,MAAM,EAAED,CAAC,EAAE,EAAE;UACzC,MAAMH,GAAG,GAAGZ,WAAW,CAACe,CAAC,CAAC;UAC1B,IAAIV,KAAK,CAACO,GAAG,CAAC,KACVN,SAAS,CAACM,GAAG,CAAC,EAAE;YAChBE,YAAY,GAAG,IAAI;UACvB;QACJ;MACJ;MACA,IAAI,CAACA,YAAY,EACb;MACJvB,KAAK,CAAC0B,IAAI,CAAC,MAAM;QACbpB,mBAAmB,CAACU,OAAO,EAAEC,WAAW,CAAC;QACzCjB,KAAK,CAAC2B,MAAM,CAAC,MAAM;UACfxB,aAAa,CAACc,WAAW,EAAEC,YAAY,EAAEb,QAAQ,CAACW,OAAO,CAACY,OAAO,CAAC,EAAEd,KAAK,CAACe,iBAAiB,CAAC;UAC5FtB,SAAS,CAACS,OAAO,EAAEC,WAAW,CAAC;QACnC,CAAC,CAAC;MACN,CAAC,CAAC;IACN;EACJ,CAAC;AACL,CAAC;AAED,SAASP,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}