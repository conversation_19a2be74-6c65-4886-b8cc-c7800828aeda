[{"D:\\Desktop\\projects\\port1\\port\\frontend\\src\\index.js": "1", "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\reportWebVitals.js": "2", "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\App.jsx": "3", "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\About.jsx": "4", "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\Navbar.jsx": "5", "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\Projects.jsx": "6", "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\Hero.jsx": "7", "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\Contact.jsx": "8", "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\ProjectModal.jsx": "9"}, {"size": 535, "mtime": 1744110037188, "results": "10", "hashOfConfig": "11"}, {"size": 362, "mtime": 1744110037285, "results": "12", "hashOfConfig": "11"}, {"size": 637, "mtime": 1744563115636, "results": "13", "hashOfConfig": "11"}, {"size": 4150, "mtime": 1744640494828, "results": "14", "hashOfConfig": "11"}, {"size": 4705, "mtime": 1744702952221, "results": "15", "hashOfConfig": "11"}, {"size": 11056, "mtime": 1744612962988, "results": "16", "hashOfConfig": "11"}, {"size": 8578, "mtime": 1752075344499, "results": "17", "hashOfConfig": "11"}, {"size": 12888, "mtime": 1744701675849, "results": "18", "hashOfConfig": "11"}, {"size": 6115, "mtime": 1744613065417, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1o041ub", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\index.js", [], [], "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\reportWebVitals.js", [], [], "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\App.jsx", [], [], "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\About.jsx", [], [], "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\Navbar.jsx", [], [], "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\Projects.jsx", ["47"], [], "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\Hero.jsx", [], [], "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\Contact.jsx", ["48"], [], "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\ProjectModal.jsx", ["49"], [], {"ruleId": "50", "severity": 1, "message": "51", "line": 115, "column": 6, "nodeType": "52", "endLine": 115, "endColumn": 8, "suggestions": "53"}, {"ruleId": "54", "severity": 1, "message": "55", "line": 20, "column": 9, "nodeType": "56", "messageId": "57", "endLine": 20, "endColumn": 17}, {"ruleId": "50", "severity": 1, "message": "58", "line": 40, "column": 6, "nodeType": "52", "endLine": 40, "endColumn": 27, "suggestions": "59"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'next'. Either include it or remove the dependency array.", "ArrayExpression", ["60"], "no-unused-vars", "'isMobile' is assigned a value but never used.", "Identifier", "unusedVar", "React Hook useEffect has a missing dependency: 'clearAndRestartInterval'. Either include it or remove the dependency array.", ["61"], {"desc": "62", "fix": "63"}, {"desc": "64", "fix": "65"}, "Update the dependencies array to be: [next]", {"range": "66", "text": "67"}, "Update the dependencies array to be: [open, images.length, clearAndRestartInterval]", {"range": "68", "text": "69"}, [3557, 3559], "[next]", [1369, 1390], "[open, images.length, clearAndRestartInterval]"]