[{"D:\\Desktop\\projects\\port1\\port\\frontend\\src\\index.js": "1", "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\reportWebVitals.js": "2", "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\App.jsx": "3", "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\About.jsx": "4", "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\Navbar.jsx": "5", "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\Projects.jsx": "6", "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\Hero.jsx": "7", "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\Contact.jsx": "8", "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\ProjectModal.jsx": "9", "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\ResumeModal.jsx": "10"}, {"size": 535, "mtime": 1744110037188, "results": "11", "hashOfConfig": "12"}, {"size": 362, "mtime": 1744110037285, "results": "13", "hashOfConfig": "12"}, {"size": 637, "mtime": 1744563115636, "results": "14", "hashOfConfig": "12"}, {"size": 4150, "mtime": 1744640494828, "results": "15", "hashOfConfig": "12"}, {"size": 4705, "mtime": 1744702952221, "results": "16", "hashOfConfig": "12"}, {"size": 11056, "mtime": 1744612962988, "results": "17", "hashOfConfig": "12"}, {"size": 9050, "mtime": 1752074510007, "results": "18", "hashOfConfig": "12"}, {"size": 12888, "mtime": 1744701675849, "results": "19", "hashOfConfig": "12"}, {"size": 6115, "mtime": 1744613065417, "results": "20", "hashOfConfig": "12"}, {"size": 5462, "mtime": 1752074314461, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1o041ub", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\index.js", [], [], "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\reportWebVitals.js", [], [], "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\App.jsx", [], [], "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\About.jsx", [], [], "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\Navbar.jsx", [], [], "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\Projects.jsx", ["52"], [], "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\Hero.jsx", [], [], "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\Contact.jsx", ["53"], [], "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\ProjectModal.jsx", ["54"], [], "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\ResumeModal.jsx", [], [], {"ruleId": "55", "severity": 1, "message": "56", "line": 115, "column": 6, "nodeType": "57", "endLine": 115, "endColumn": 8, "suggestions": "58"}, {"ruleId": "59", "severity": 1, "message": "60", "line": 20, "column": 9, "nodeType": "61", "messageId": "62", "endLine": 20, "endColumn": 17}, {"ruleId": "55", "severity": 1, "message": "63", "line": 40, "column": 6, "nodeType": "57", "endLine": 40, "endColumn": 27, "suggestions": "64"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'next'. Either include it or remove the dependency array.", "ArrayExpression", ["65"], "no-unused-vars", "'isMobile' is assigned a value but never used.", "Identifier", "unusedVar", "React Hook useEffect has a missing dependency: 'clearAndRestartInterval'. Either include it or remove the dependency array.", ["66"], {"desc": "67", "fix": "68"}, {"desc": "69", "fix": "70"}, "Update the dependencies array to be: [next]", {"range": "71", "text": "72"}, "Update the dependencies array to be: [open, images.length, clearAndRestartInterval]", {"range": "73", "text": "74"}, [3557, 3559], "[next]", [1369, 1390], "[open, images.length, clearAndRestartInterval]"]