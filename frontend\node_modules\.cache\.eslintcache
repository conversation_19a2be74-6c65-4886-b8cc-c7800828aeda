[{"D:\\Desktop\\projects\\port1\\port\\frontend\\src\\index.js": "1", "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\reportWebVitals.js": "2", "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\App.jsx": "3", "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\About.jsx": "4", "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\Navbar.jsx": "5", "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\Projects.jsx": "6", "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\Hero.jsx": "7", "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\Contact.jsx": "8", "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\ProjectModal.jsx": "9"}, {"size": 535, "mtime": 1744110037188, "results": "10", "hashOfConfig": "11"}, {"size": 362, "mtime": 1744110037285, "results": "12", "hashOfConfig": "11"}, {"size": 637, "mtime": 1744563115636, "results": "13", "hashOfConfig": "11"}, {"size": 4150, "mtime": 1744640494828, "results": "14", "hashOfConfig": "11"}, {"size": 4705, "mtime": 1744702952221, "results": "15", "hashOfConfig": "11"}, {"size": 11056, "mtime": 1744612962988, "results": "16", "hashOfConfig": "11"}, {"size": 9454, "mtime": 1752075272255, "results": "17", "hashOfConfig": "11"}, {"size": 12888, "mtime": 1744701675849, "results": "18", "hashOfConfig": "11"}, {"size": 6115, "mtime": 1744613065417, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1o041ub", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\index.js", [], [], "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\reportWebVitals.js", [], [], "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\App.jsx", [], [], "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\About.jsx", [], [], "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\Navbar.jsx", [], [], "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\Projects.jsx", ["47"], [], "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\Hero.jsx", ["48"], [], "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\Contact.jsx", ["49"], [], "D:\\Desktop\\projects\\port1\\port\\frontend\\src\\components\\ProjectModal.jsx", ["50"], [], {"ruleId": "51", "severity": 1, "message": "52", "line": 115, "column": 6, "nodeType": "53", "endLine": 115, "endColumn": 8, "suggestions": "54"}, {"ruleId": "55", "severity": 1, "message": "56", "line": 217, "column": 13, "nodeType": "57", "endLine": 247, "endColumn": 15}, {"ruleId": "58", "severity": 1, "message": "59", "line": 20, "column": 9, "nodeType": "60", "messageId": "61", "endLine": 20, "endColumn": 17}, {"ruleId": "51", "severity": 1, "message": "62", "line": 40, "column": 6, "nodeType": "53", "endLine": 40, "endColumn": 27, "suggestions": "63"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'next'. Either include it or remove the dependency array.", "ArrayExpression", ["64"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "no-unused-vars", "'isMobile' is assigned a value but never used.", "Identifier", "unusedVar", "React Hook useEffect has a missing dependency: 'clearAndRestartInterval'. Either include it or remove the dependency array.", ["65"], {"desc": "66", "fix": "67"}, {"desc": "68", "fix": "69"}, "Update the dependencies array to be: [next]", {"range": "70", "text": "71"}, "Update the dependencies array to be: [open, images.length, clearAndRestartInterval]", {"range": "72", "text": "73"}, [3557, 3559], "[next]", [1369, 1390], "[open, images.length, clearAndRestartInterval]"]