{"ast": null, "code": "/**\n * Add keys, values of `defaultProps` that does not exist in `props`\n * @param defaultProps\n * @param props\n * @returns resolved props\n */\nexport default function resolveProps(defaultProps, props) {\n  const output = {\n    ...props\n  };\n  for (const key in defaultProps) {\n    if (Object.prototype.hasOwnProperty.call(defaultProps, key)) {\n      const propName = key;\n      if (propName === 'components' || propName === 'slots') {\n        output[propName] = {\n          ...defaultProps[propName],\n          ...output[propName]\n        };\n      } else if (propName === 'componentsProps' || propName === 'slotProps') {\n        const defaultSlotProps = defaultProps[propName];\n        const slotProps = props[propName];\n        if (!slotProps) {\n          output[propName] = defaultSlotProps || {};\n        } else if (!defaultSlotProps) {\n          output[propName] = slotProps;\n        } else {\n          output[propName] = {\n            ...slotProps\n          };\n          for (const slotKey in defaultSlotProps) {\n            if (Object.prototype.hasOwnProperty.call(defaultSlotProps, slotKey)) {\n              const slotPropName = slotKey;\n              output[propName][slotPropName] = resolveProps(defaultSlotProps[slotPropName], slotProps[slotPropName]);\n            }\n          }\n        }\n      } else if (output[propName] === undefined) {\n        output[propName] = defaultProps[propName];\n      }\n    }\n  }\n  return output;\n}", "map": {"version": 3, "names": ["resolveProps", "defaultProps", "props", "output", "key", "Object", "prototype", "hasOwnProperty", "call", "propName", "defaultSlotProps", "slotProps", "<PERSON><PERSON><PERSON>", "slotPropName", "undefined"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/@mui/utils/esm/resolveProps/resolveProps.js"], "sourcesContent": ["/**\n * Add keys, values of `defaultProps` that does not exist in `props`\n * @param defaultProps\n * @param props\n * @returns resolved props\n */\nexport default function resolveProps(defaultProps, props) {\n  const output = {\n    ...props\n  };\n  for (const key in defaultProps) {\n    if (Object.prototype.hasOwnProperty.call(defaultProps, key)) {\n      const propName = key;\n      if (propName === 'components' || propName === 'slots') {\n        output[propName] = {\n          ...defaultProps[propName],\n          ...output[propName]\n        };\n      } else if (propName === 'componentsProps' || propName === 'slotProps') {\n        const defaultSlotProps = defaultProps[propName];\n        const slotProps = props[propName];\n        if (!slotProps) {\n          output[propName] = defaultSlotProps || {};\n        } else if (!defaultSlotProps) {\n          output[propName] = slotProps;\n        } else {\n          output[propName] = {\n            ...slotProps\n          };\n          for (const slotKey in defaultSlotProps) {\n            if (Object.prototype.hasOwnProperty.call(defaultSlotProps, slotKey)) {\n              const slotPropName = slotKey;\n              output[propName][slotPropName] = resolveProps(defaultSlotProps[slotPropName], slotProps[slotPropName]);\n            }\n          }\n        }\n      } else if (output[propName] === undefined) {\n        output[propName] = defaultProps[propName];\n      }\n    }\n  }\n  return output;\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,YAAYA,CAACC,YAAY,EAAEC,KAAK,EAAE;EACxD,MAAMC,MAAM,GAAG;IACb,GAAGD;EACL,CAAC;EACD,KAAK,MAAME,GAAG,IAAIH,YAAY,EAAE;IAC9B,IAAII,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACP,YAAY,EAAEG,GAAG,CAAC,EAAE;MAC3D,MAAMK,QAAQ,GAAGL,GAAG;MACpB,IAAIK,QAAQ,KAAK,YAAY,IAAIA,QAAQ,KAAK,OAAO,EAAE;QACrDN,MAAM,CAACM,QAAQ,CAAC,GAAG;UACjB,GAAGR,YAAY,CAACQ,QAAQ,CAAC;UACzB,GAAGN,MAAM,CAACM,QAAQ;QACpB,CAAC;MACH,CAAC,MAAM,IAAIA,QAAQ,KAAK,iBAAiB,IAAIA,QAAQ,KAAK,WAAW,EAAE;QACrE,MAAMC,gBAAgB,GAAGT,YAAY,CAACQ,QAAQ,CAAC;QAC/C,MAAME,SAAS,GAAGT,KAAK,CAACO,QAAQ,CAAC;QACjC,IAAI,CAACE,SAAS,EAAE;UACdR,MAAM,CAACM,QAAQ,CAAC,GAAGC,gBAAgB,IAAI,CAAC,CAAC;QAC3C,CAAC,MAAM,IAAI,CAACA,gBAAgB,EAAE;UAC5BP,MAAM,CAACM,QAAQ,CAAC,GAAGE,SAAS;QAC9B,CAAC,MAAM;UACLR,MAAM,CAACM,QAAQ,CAAC,GAAG;YACjB,GAAGE;UACL,CAAC;UACD,KAAK,MAAMC,OAAO,IAAIF,gBAAgB,EAAE;YACtC,IAAIL,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACE,gBAAgB,EAAEE,OAAO,CAAC,EAAE;cACnE,MAAMC,YAAY,GAAGD,OAAO;cAC5BT,MAAM,CAACM,QAAQ,CAAC,CAACI,YAAY,CAAC,GAAGb,YAAY,CAACU,gBAAgB,CAACG,YAAY,CAAC,EAAEF,SAAS,CAACE,YAAY,CAAC,CAAC;YACxG;UACF;QACF;MACF,CAAC,MAAM,IAAIV,MAAM,CAACM,QAAQ,CAAC,KAAKK,SAAS,EAAE;QACzCX,MAAM,CAACM,QAAQ,CAAC,GAAGR,YAAY,CAACQ,QAAQ,CAAC;MAC3C;IACF;EACF;EACA,OAAON,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}