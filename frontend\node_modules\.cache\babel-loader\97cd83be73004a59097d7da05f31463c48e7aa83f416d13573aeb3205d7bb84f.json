{"ast": null, "code": "import { frame } from 'motion-dom';\nimport { createBox } from '../../projection/geometry/models.mjs';\nimport { DOMVisualElement } from '../dom/DOMVisualElement.mjs';\nimport { camelToDash } from '../dom/utils/camel-to-dash.mjs';\nimport { getDefaultValueType } from '../dom/value-types/defaults.mjs';\nimport { transformProps } from '../html/utils/keys-transform.mjs';\nimport { buildSVGAttrs } from './utils/build-attrs.mjs';\nimport { camelCaseAttributes } from './utils/camel-case-attrs.mjs';\nimport { isSVGTag } from './utils/is-svg-tag.mjs';\nimport { updateSVGDimensions } from './utils/measure.mjs';\nimport { renderSVG } from './utils/render.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\nclass SVGVisualElement extends DOMVisualElement {\n  constructor() {\n    super(...arguments);\n    this.type = \"svg\";\n    this.isSVGTag = false;\n    this.measureInstanceViewportBox = createBox;\n    this.updateDimensions = () => {\n      if (this.current && !this.renderState.dimensions) {\n        updateSVGDimensions(this.current, this.renderState);\n      }\n    };\n  }\n  getBaseTargetFromProps(props, key) {\n    return props[key];\n  }\n  readValueFromInstance(instance, key) {\n    if (transformProps.has(key)) {\n      const defaultType = getDefaultValueType(key);\n      return defaultType ? defaultType.default || 0 : 0;\n    }\n    key = !camelCaseAttributes.has(key) ? camelToDash(key) : key;\n    return instance.getAttribute(key);\n  }\n  scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n    return scrapeMotionValuesFromProps(props, prevProps, visualElement);\n  }\n  onBindTransform() {\n    if (this.current && !this.renderState.dimensions) {\n      frame.postRender(this.updateDimensions);\n    }\n  }\n  build(renderState, latestValues, props) {\n    buildSVGAttrs(renderState, latestValues, this.isSVGTag, props.transformTemplate);\n  }\n  renderInstance(instance, renderState, styleProp, projection) {\n    renderSVG(instance, renderState, styleProp, projection);\n  }\n  mount(instance) {\n    this.isSVGTag = isSVGTag(instance.tagName);\n    super.mount(instance);\n  }\n}\nexport { SVGVisualElement };", "map": {"version": 3, "names": ["frame", "createBox", "DOMVisualElement", "camelToDash", "getDefaultValueType", "transformProps", "buildSVGAttrs", "camelCaseAttributes", "isSVGTag", "updateSVGDimensions", "renderSVG", "scrapeMotionValuesFromProps", "SVGVisualElement", "constructor", "arguments", "type", "measureInstanceViewportBox", "updateDimensions", "current", "renderState", "dimensions", "getBaseTargetFromProps", "props", "key", "readValueFromInstance", "instance", "has", "defaultType", "default", "getAttribute", "prevProps", "visualElement", "onBindTransform", "postRender", "build", "latestValues", "transformTemplate", "renderInstance", "styleProp", "projection", "mount", "tagName"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/framer-motion/dist/es/render/svg/SVGVisualElement.mjs"], "sourcesContent": ["import { frame } from 'motion-dom';\nimport { createBox } from '../../projection/geometry/models.mjs';\nimport { DOMVisualElement } from '../dom/DOMVisualElement.mjs';\nimport { camelToDash } from '../dom/utils/camel-to-dash.mjs';\nimport { getDefaultValueType } from '../dom/value-types/defaults.mjs';\nimport { transformProps } from '../html/utils/keys-transform.mjs';\nimport { buildSVGAttrs } from './utils/build-attrs.mjs';\nimport { camelCaseAttributes } from './utils/camel-case-attrs.mjs';\nimport { isSVGTag } from './utils/is-svg-tag.mjs';\nimport { updateSVGDimensions } from './utils/measure.mjs';\nimport { renderSVG } from './utils/render.mjs';\nimport { scrapeMotionValuesFromProps } from './utils/scrape-motion-values.mjs';\n\nclass SVGVisualElement extends DOMVisualElement {\n    constructor() {\n        super(...arguments);\n        this.type = \"svg\";\n        this.isSVGTag = false;\n        this.measureInstanceViewportBox = createBox;\n        this.updateDimensions = () => {\n            if (this.current && !this.renderState.dimensions) {\n                updateSVGDimensions(this.current, this.renderState);\n            }\n        };\n    }\n    getBaseTargetFromProps(props, key) {\n        return props[key];\n    }\n    readValueFromInstance(instance, key) {\n        if (transformProps.has(key)) {\n            const defaultType = getDefaultValueType(key);\n            return defaultType ? defaultType.default || 0 : 0;\n        }\n        key = !camelCaseAttributes.has(key) ? camelToDash(key) : key;\n        return instance.getAttribute(key);\n    }\n    scrapeMotionValuesFromProps(props, prevProps, visualElement) {\n        return scrapeMotionValuesFromProps(props, prevProps, visualElement);\n    }\n    onBindTransform() {\n        if (this.current && !this.renderState.dimensions) {\n            frame.postRender(this.updateDimensions);\n        }\n    }\n    build(renderState, latestValues, props) {\n        buildSVGAttrs(renderState, latestValues, this.isSVGTag, props.transformTemplate);\n    }\n    renderInstance(instance, renderState, styleProp, projection) {\n        renderSVG(instance, renderState, styleProp, projection);\n    }\n    mount(instance) {\n        this.isSVGTag = isSVGTag(instance.tagName);\n        super.mount(instance);\n    }\n}\n\nexport { SVGVisualElement };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,YAAY;AAClC,SAASC,SAAS,QAAQ,sCAAsC;AAChE,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,WAAW,QAAQ,gCAAgC;AAC5D,SAASC,mBAAmB,QAAQ,iCAAiC;AACrE,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,mBAAmB,QAAQ,qBAAqB;AACzD,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,2BAA2B,QAAQ,kCAAkC;AAE9E,MAAMC,gBAAgB,SAASV,gBAAgB,CAAC;EAC5CW,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,IAAI,GAAG,KAAK;IACjB,IAAI,CAACP,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACQ,0BAA0B,GAAGf,SAAS;IAC3C,IAAI,CAACgB,gBAAgB,GAAG,MAAM;MAC1B,IAAI,IAAI,CAACC,OAAO,IAAI,CAAC,IAAI,CAACC,WAAW,CAACC,UAAU,EAAE;QAC9CX,mBAAmB,CAAC,IAAI,CAACS,OAAO,EAAE,IAAI,CAACC,WAAW,CAAC;MACvD;IACJ,CAAC;EACL;EACAE,sBAAsBA,CAACC,KAAK,EAAEC,GAAG,EAAE;IAC/B,OAAOD,KAAK,CAACC,GAAG,CAAC;EACrB;EACAC,qBAAqBA,CAACC,QAAQ,EAAEF,GAAG,EAAE;IACjC,IAAIlB,cAAc,CAACqB,GAAG,CAACH,GAAG,CAAC,EAAE;MACzB,MAAMI,WAAW,GAAGvB,mBAAmB,CAACmB,GAAG,CAAC;MAC5C,OAAOI,WAAW,GAAGA,WAAW,CAACC,OAAO,IAAI,CAAC,GAAG,CAAC;IACrD;IACAL,GAAG,GAAG,CAAChB,mBAAmB,CAACmB,GAAG,CAACH,GAAG,CAAC,GAAGpB,WAAW,CAACoB,GAAG,CAAC,GAAGA,GAAG;IAC5D,OAAOE,QAAQ,CAACI,YAAY,CAACN,GAAG,CAAC;EACrC;EACAZ,2BAA2BA,CAACW,KAAK,EAAEQ,SAAS,EAAEC,aAAa,EAAE;IACzD,OAAOpB,2BAA2B,CAACW,KAAK,EAAEQ,SAAS,EAAEC,aAAa,CAAC;EACvE;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACd,OAAO,IAAI,CAAC,IAAI,CAACC,WAAW,CAACC,UAAU,EAAE;MAC9CpB,KAAK,CAACiC,UAAU,CAAC,IAAI,CAAChB,gBAAgB,CAAC;IAC3C;EACJ;EACAiB,KAAKA,CAACf,WAAW,EAAEgB,YAAY,EAAEb,KAAK,EAAE;IACpChB,aAAa,CAACa,WAAW,EAAEgB,YAAY,EAAE,IAAI,CAAC3B,QAAQ,EAAEc,KAAK,CAACc,iBAAiB,CAAC;EACpF;EACAC,cAAcA,CAACZ,QAAQ,EAAEN,WAAW,EAAEmB,SAAS,EAAEC,UAAU,EAAE;IACzD7B,SAAS,CAACe,QAAQ,EAAEN,WAAW,EAAEmB,SAAS,EAAEC,UAAU,CAAC;EAC3D;EACAC,KAAKA,CAACf,QAAQ,EAAE;IACZ,IAAI,CAACjB,QAAQ,GAAGA,QAAQ,CAACiB,QAAQ,CAACgB,OAAO,CAAC;IAC1C,KAAK,CAACD,KAAK,CAACf,QAAQ,CAAC;EACzB;AACJ;AAEA,SAASb,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}