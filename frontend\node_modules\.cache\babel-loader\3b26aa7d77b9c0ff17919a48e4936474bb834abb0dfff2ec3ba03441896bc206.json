{"ast": null, "code": "/* eslint-disable import/prefer-default-export */\nimport { isPlainObject } from '@mui/utils/deepmerge';\nfunction isSerializable(val) {\n  return isPlainObject(val) || typeof val === 'undefined' || typeof val === 'string' || typeof val === 'boolean' || typeof val === 'number' || Array.isArray(val);\n}\n\n/**\n * `baseTheme` usually comes from `createTheme()` or `extendTheme()`.\n *\n * This function is intended to be used with zero-runtime CSS-in-JS like Pigment CSS\n * For example, in a Next.js project:\n *\n * ```js\n * // next.config.js\n * const { extendTheme } = require('@mui/material/styles');\n *\n * const theme = extendTheme();\n * // `.toRuntimeSource` is Pigment CSS specific to create a theme that is available at runtime.\n * theme.toRuntimeSource = stringifyTheme;\n *\n * module.exports = withPigment({\n *  theme,\n * });\n * ```\n */\nexport function stringifyTheme(baseTheme = {}) {\n  const serializableTheme = {\n    ...baseTheme\n  };\n  function serializeTheme(object) {\n    const array = Object.entries(object);\n    // eslint-disable-next-line no-plusplus\n    for (let index = 0; index < array.length; index++) {\n      const [key, value] = array[index];\n      if (!isSerializable(value) || key.startsWith('unstable_')) {\n        delete object[key];\n      } else if (isPlainObject(value)) {\n        object[key] = {\n          ...value\n        };\n        serializeTheme(object[key]);\n      }\n    }\n  }\n  serializeTheme(serializableTheme);\n  return `import { unstable_createBreakpoints as createBreakpoints, createTransitions } from '@mui/material/styles';\n\nconst theme = ${JSON.stringify(serializableTheme, null, 2)};\n\ntheme.breakpoints = createBreakpoints(theme.breakpoints || {});\ntheme.transitions = createTransitions(theme.transitions || {});\n\nexport default theme;`;\n}", "map": {"version": 3, "names": ["isPlainObject", "isSerializable", "val", "Array", "isArray", "stringifyTheme", "baseTheme", "serializableTheme", "serializeTheme", "object", "array", "Object", "entries", "index", "length", "key", "value", "startsWith", "JSON", "stringify"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/@mui/material/esm/styles/stringifyTheme.js"], "sourcesContent": ["/* eslint-disable import/prefer-default-export */\nimport { isPlainObject } from '@mui/utils/deepmerge';\nfunction isSerializable(val) {\n  return isPlainObject(val) || typeof val === 'undefined' || typeof val === 'string' || typeof val === 'boolean' || typeof val === 'number' || Array.isArray(val);\n}\n\n/**\n * `baseTheme` usually comes from `createTheme()` or `extendTheme()`.\n *\n * This function is intended to be used with zero-runtime CSS-in-JS like Pigment CSS\n * For example, in a Next.js project:\n *\n * ```js\n * // next.config.js\n * const { extendTheme } = require('@mui/material/styles');\n *\n * const theme = extendTheme();\n * // `.toRuntimeSource` is Pigment CSS specific to create a theme that is available at runtime.\n * theme.toRuntimeSource = stringifyTheme;\n *\n * module.exports = withPigment({\n *  theme,\n * });\n * ```\n */\nexport function stringifyTheme(baseTheme = {}) {\n  const serializableTheme = {\n    ...baseTheme\n  };\n  function serializeTheme(object) {\n    const array = Object.entries(object);\n    // eslint-disable-next-line no-plusplus\n    for (let index = 0; index < array.length; index++) {\n      const [key, value] = array[index];\n      if (!isSerializable(value) || key.startsWith('unstable_')) {\n        delete object[key];\n      } else if (isPlainObject(value)) {\n        object[key] = {\n          ...value\n        };\n        serializeTheme(object[key]);\n      }\n    }\n  }\n  serializeTheme(serializableTheme);\n  return `import { unstable_createBreakpoints as createBreakpoints, createTransitions } from '@mui/material/styles';\n\nconst theme = ${JSON.stringify(serializableTheme, null, 2)};\n\ntheme.breakpoints = createBreakpoints(theme.breakpoints || {});\ntheme.transitions = createTransitions(theme.transitions || {});\n\nexport default theme;`;\n}"], "mappings": "AAAA;AACA,SAASA,aAAa,QAAQ,sBAAsB;AACpD,SAASC,cAAcA,CAACC,GAAG,EAAE;EAC3B,OAAOF,aAAa,CAACE,GAAG,CAAC,IAAI,OAAOA,GAAG,KAAK,WAAW,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,SAAS,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC;AACjK;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASG,cAAcA,CAACC,SAAS,GAAG,CAAC,CAAC,EAAE;EAC7C,MAAMC,iBAAiB,GAAG;IACxB,GAAGD;EACL,CAAC;EACD,SAASE,cAAcA,CAACC,MAAM,EAAE;IAC9B,MAAMC,KAAK,GAAGC,MAAM,CAACC,OAAO,CAACH,MAAM,CAAC;IACpC;IACA,KAAK,IAAII,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGH,KAAK,CAACI,MAAM,EAAED,KAAK,EAAE,EAAE;MACjD,MAAM,CAACE,GAAG,EAAEC,KAAK,CAAC,GAAGN,KAAK,CAACG,KAAK,CAAC;MACjC,IAAI,CAACZ,cAAc,CAACe,KAAK,CAAC,IAAID,GAAG,CAACE,UAAU,CAAC,WAAW,CAAC,EAAE;QACzD,OAAOR,MAAM,CAACM,GAAG,CAAC;MACpB,CAAC,MAAM,IAAIf,aAAa,CAACgB,KAAK,CAAC,EAAE;QAC/BP,MAAM,CAACM,GAAG,CAAC,GAAG;UACZ,GAAGC;QACL,CAAC;QACDR,cAAc,CAACC,MAAM,CAACM,GAAG,CAAC,CAAC;MAC7B;IACF;EACF;EACAP,cAAc,CAACD,iBAAiB,CAAC;EACjC,OAAO;AACT;AACA,gBAAgBW,IAAI,CAACC,SAAS,CAACZ,iBAAiB,EAAE,IAAI,EAAE,CAAC,CAAC;AAC1D;AACA;AACA;AACA;AACA,sBAAsB;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}