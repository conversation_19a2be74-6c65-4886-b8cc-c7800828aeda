{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport Fade from \"../Fade/index.js\";\nimport { getBackdropUtilityClass } from \"./backdropClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    invisible\n  } = ownerState;\n  const slots = {\n    root: ['root', invisible && 'invisible']\n  };\n  return composeClasses(slots, getBackdropUtilityClass, classes);\n};\nconst BackdropRoot = styled('div', {\n  name: 'MuiBackdrop',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.invisible && styles.invisible];\n  }\n})({\n  position: 'fixed',\n  display: 'flex',\n  alignItems: 'center',\n  justifyContent: 'center',\n  right: 0,\n  bottom: 0,\n  top: 0,\n  left: 0,\n  backgroundColor: 'rgba(0, 0, 0, 0.5)',\n  WebkitTapHighlightColor: 'transparent',\n  variants: [{\n    props: {\n      invisible: true\n    },\n    style: {\n      backgroundColor: 'transparent'\n    }\n  }]\n});\nconst Backdrop = /*#__PURE__*/React.forwardRef(function Backdrop(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiBackdrop'\n  });\n  const {\n    children,\n    className,\n    component = 'div',\n    invisible = false,\n    open,\n    components = {},\n    componentsProps = {},\n    slotProps = {},\n    slots = {},\n    TransitionComponent: TransitionComponentProp,\n    transitionDuration,\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    component,\n    invisible\n  };\n  const classes = useUtilityClasses(ownerState);\n  const backwardCompatibleSlots = {\n    transition: TransitionComponentProp,\n    root: components.Root,\n    ...slots\n  };\n  const backwardCompatibleSlotProps = {\n    ...componentsProps,\n    ...slotProps\n  };\n  const externalForwardedProps = {\n    slots: backwardCompatibleSlots,\n    slotProps: backwardCompatibleSlotProps\n  };\n  const [RootSlot, rootProps] = useSlot('root', {\n    elementType: BackdropRoot,\n    externalForwardedProps,\n    className: clsx(classes.root, className),\n    ownerState\n  });\n  const [TransitionSlot, transitionProps] = useSlot('transition', {\n    elementType: Fade,\n    externalForwardedProps,\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(TransitionSlot, {\n    in: open,\n    timeout: transitionDuration,\n    ...other,\n    ...transitionProps,\n    children: /*#__PURE__*/_jsx(RootSlot, {\n      \"aria-hidden\": true,\n      ...rootProps,\n      classes: classes,\n      ref: ref,\n      children: children\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Backdrop.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside.\n   *\n   * @deprecated Use the `slots` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   *\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * @deprecated Use the `slotProps` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    root: PropTypes.object\n  }),\n  /**\n   * If `true`, the backdrop is invisible.\n   * It can be used when rendering a popover or a custom select component.\n   * @default false\n   */\n  invisible: PropTypes.bool,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool.isRequired,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    transition: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType,\n    transition: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The component used for the transition.\n   * [Follow this guide](https://mui.com/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.\n   * @default Fade\n   * @deprecated Use `slots.transition` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  TransitionComponent: PropTypes.elementType,\n  /**\n   * The duration for the transition, in milliseconds.\n   * You may specify a single timeout for all transitions, or individually with an object.\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })])\n} : void 0;\nexport default Backdrop;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "composeClasses", "styled", "useDefaultProps", "useSlot", "Fade", "getBackdropUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "invisible", "slots", "root", "BackdropRoot", "name", "slot", "overridesResolver", "props", "styles", "position", "display", "alignItems", "justifyContent", "right", "bottom", "top", "left", "backgroundColor", "WebkitTapHighlightColor", "variants", "style", "Backdrop", "forwardRef", "inProps", "ref", "children", "className", "component", "open", "components", "componentsProps", "slotProps", "TransitionComponent", "TransitionComponentProp", "transitionDuration", "other", "backwardCompatibleSlots", "transition", "Root", "backwardCompatibleSlotProps", "externalForwardedProps", "RootSlot", "rootProps", "elementType", "TransitionSlot", "transitionProps", "in", "timeout", "process", "env", "NODE_ENV", "propTypes", "node", "object", "string", "shape", "bool", "isRequired", "oneOfType", "func", "sx", "arrayOf", "number", "appear", "enter", "exit"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/@mui/material/esm/Backdrop/Backdrop.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport Fade from \"../Fade/index.js\";\nimport { getBackdropUtilityClass } from \"./backdropClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    invisible\n  } = ownerState;\n  const slots = {\n    root: ['root', invisible && 'invisible']\n  };\n  return composeClasses(slots, getBackdropUtilityClass, classes);\n};\nconst BackdropRoot = styled('div', {\n  name: 'MuiBackdrop',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.invisible && styles.invisible];\n  }\n})({\n  position: 'fixed',\n  display: 'flex',\n  alignItems: 'center',\n  justifyContent: 'center',\n  right: 0,\n  bottom: 0,\n  top: 0,\n  left: 0,\n  backgroundColor: 'rgba(0, 0, 0, 0.5)',\n  WebkitTapHighlightColor: 'transparent',\n  variants: [{\n    props: {\n      invisible: true\n    },\n    style: {\n      backgroundColor: 'transparent'\n    }\n  }]\n});\nconst Backdrop = /*#__PURE__*/React.forwardRef(function Backdrop(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiBackdrop'\n  });\n  const {\n    children,\n    className,\n    component = 'div',\n    invisible = false,\n    open,\n    components = {},\n    componentsProps = {},\n    slotProps = {},\n    slots = {},\n    TransitionComponent: TransitionComponentProp,\n    transitionDuration,\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    component,\n    invisible\n  };\n  const classes = useUtilityClasses(ownerState);\n  const backwardCompatibleSlots = {\n    transition: TransitionComponentProp,\n    root: components.Root,\n    ...slots\n  };\n  const backwardCompatibleSlotProps = {\n    ...componentsProps,\n    ...slotProps\n  };\n  const externalForwardedProps = {\n    slots: backwardCompatibleSlots,\n    slotProps: backwardCompatibleSlotProps\n  };\n  const [RootSlot, rootProps] = useSlot('root', {\n    elementType: BackdropRoot,\n    externalForwardedProps,\n    className: clsx(classes.root, className),\n    ownerState\n  });\n  const [TransitionSlot, transitionProps] = useSlot('transition', {\n    elementType: Fade,\n    externalForwardedProps,\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(TransitionSlot, {\n    in: open,\n    timeout: transitionDuration,\n    ...other,\n    ...transitionProps,\n    children: /*#__PURE__*/_jsx(RootSlot, {\n      \"aria-hidden\": true,\n      ...rootProps,\n      classes: classes,\n      ref: ref,\n      children: children\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Backdrop.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside.\n   *\n   * @deprecated Use the `slots` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   *\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * @deprecated Use the `slotProps` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    root: PropTypes.object\n  }),\n  /**\n   * If `true`, the backdrop is invisible.\n   * It can be used when rendering a popover or a custom select component.\n   * @default false\n   */\n  invisible: PropTypes.bool,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool.isRequired,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    transition: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType,\n    transition: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The component used for the transition.\n   * [Follow this guide](https://mui.com/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.\n   * @default Fade\n   * @deprecated Use `slots.transition` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  TransitionComponent: PropTypes.elementType,\n  /**\n   * The duration for the transition, in milliseconds.\n   * You may specify a single timeout for all transitions, or individually with an object.\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })])\n} : void 0;\nexport default Backdrop;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,eAAe,QAAQ,kCAAkC;AAClE,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAOC,IAAI,MAAM,kBAAkB;AACnC,SAASC,uBAAuB,QAAQ,sBAAsB;AAC9D,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEF,SAAS,IAAI,WAAW;EACzC,CAAC;EACD,OAAOX,cAAc,CAACY,KAAK,EAAEP,uBAAuB,EAAEK,OAAO,CAAC;AAChE,CAAC;AACD,MAAMI,YAAY,GAAGb,MAAM,CAAC,KAAK,EAAE;EACjCc,IAAI,EAAE,aAAa;EACnBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJV;IACF,CAAC,GAAGS,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,IAAI,EAAEJ,UAAU,CAACE,SAAS,IAAIQ,MAAM,CAACR,SAAS,CAAC;EAChE;AACF,CAAC,CAAC,CAAC;EACDS,QAAQ,EAAE,OAAO;EACjBC,OAAO,EAAE,MAAM;EACfC,UAAU,EAAE,QAAQ;EACpBC,cAAc,EAAE,QAAQ;EACxBC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,GAAG,EAAE,CAAC;EACNC,IAAI,EAAE,CAAC;EACPC,eAAe,EAAE,oBAAoB;EACrCC,uBAAuB,EAAE,aAAa;EACtCC,QAAQ,EAAE,CAAC;IACTZ,KAAK,EAAE;MACLP,SAAS,EAAE;IACb,CAAC;IACDoB,KAAK,EAAE;MACLH,eAAe,EAAE;IACnB;EACF,CAAC;AACH,CAAC,CAAC;AACF,MAAMI,QAAQ,GAAG,aAAanC,KAAK,CAACoC,UAAU,CAAC,SAASD,QAAQA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC7E,MAAMjB,KAAK,GAAGhB,eAAe,CAAC;IAC5BgB,KAAK,EAAEgB,OAAO;IACdnB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJqB,QAAQ;IACRC,SAAS;IACTC,SAAS,GAAG,KAAK;IACjB3B,SAAS,GAAG,KAAK;IACjB4B,IAAI;IACJC,UAAU,GAAG,CAAC,CAAC;IACfC,eAAe,GAAG,CAAC,CAAC;IACpBC,SAAS,GAAG,CAAC,CAAC;IACd9B,KAAK,GAAG,CAAC,CAAC;IACV+B,mBAAmB,EAAEC,uBAAuB;IAC5CC,kBAAkB;IAClB,GAAGC;EACL,CAAC,GAAG5B,KAAK;EACT,MAAMT,UAAU,GAAG;IACjB,GAAGS,KAAK;IACRoB,SAAS;IACT3B;EACF,CAAC;EACD,MAAMD,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMsC,uBAAuB,GAAG;IAC9BC,UAAU,EAAEJ,uBAAuB;IACnC/B,IAAI,EAAE2B,UAAU,CAACS,IAAI;IACrB,GAAGrC;EACL,CAAC;EACD,MAAMsC,2BAA2B,GAAG;IAClC,GAAGT,eAAe;IAClB,GAAGC;EACL,CAAC;EACD,MAAMS,sBAAsB,GAAG;IAC7BvC,KAAK,EAAEmC,uBAAuB;IAC9BL,SAAS,EAAEQ;EACb,CAAC;EACD,MAAM,CAACE,QAAQ,EAAEC,SAAS,CAAC,GAAGlD,OAAO,CAAC,MAAM,EAAE;IAC5CmD,WAAW,EAAExC,YAAY;IACzBqC,sBAAsB;IACtBd,SAAS,EAAEtC,IAAI,CAACW,OAAO,CAACG,IAAI,EAAEwB,SAAS,CAAC;IACxC5B;EACF,CAAC,CAAC;EACF,MAAM,CAAC8C,cAAc,EAAEC,eAAe,CAAC,GAAGrD,OAAO,CAAC,YAAY,EAAE;IAC9DmD,WAAW,EAAElD,IAAI;IACjB+C,sBAAsB;IACtB1C;EACF,CAAC,CAAC;EACF,OAAO,aAAaF,IAAI,CAACgD,cAAc,EAAE;IACvCE,EAAE,EAAElB,IAAI;IACRmB,OAAO,EAAEb,kBAAkB;IAC3B,GAAGC,KAAK;IACR,GAAGU,eAAe;IAClBpB,QAAQ,EAAE,aAAa7B,IAAI,CAAC6C,QAAQ,EAAE;MACpC,aAAa,EAAE,IAAI;MACnB,GAAGC,SAAS;MACZ3C,OAAO,EAAEA,OAAO;MAChByB,GAAG,EAAEA,GAAG;MACRC,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFuB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG7B,QAAQ,CAAC8B,SAAS,CAAC,yBAAyB;EAClF;EACA;EACA;EACA;EACA;AACF;AACA;EACE1B,QAAQ,EAAEtC,SAAS,CAACiE,IAAI;EACxB;AACF;AACA;EACErD,OAAO,EAAEZ,SAAS,CAACkE,MAAM;EACzB;AACF;AACA;EACE3B,SAAS,EAAEvC,SAAS,CAACmE,MAAM;EAC3B;AACF;AACA;AACA;EACE3B,SAAS,EAAExC,SAAS,CAACwD,WAAW;EAChC;AACF;AACA;AACA;AACA;AACA;AACA;EACEd,UAAU,EAAE1C,SAAS,CAACoE,KAAK,CAAC;IAC1BjB,IAAI,EAAEnD,SAAS,CAACwD;EAClB,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEb,eAAe,EAAE3C,SAAS,CAACoE,KAAK,CAAC;IAC/BrD,IAAI,EAAEf,SAAS,CAACkE;EAClB,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACErD,SAAS,EAAEb,SAAS,CAACqE,IAAI;EACzB;AACF;AACA;EACE5B,IAAI,EAAEzC,SAAS,CAACqE,IAAI,CAACC,UAAU;EAC/B;AACF;AACA;AACA;EACE1B,SAAS,EAAE5C,SAAS,CAACoE,KAAK,CAAC;IACzBrD,IAAI,EAAEf,SAAS,CAACuE,SAAS,CAAC,CAACvE,SAAS,CAACwE,IAAI,EAAExE,SAAS,CAACkE,MAAM,CAAC,CAAC;IAC7DhB,UAAU,EAAElD,SAAS,CAACuE,SAAS,CAAC,CAACvE,SAAS,CAACwE,IAAI,EAAExE,SAAS,CAACkE,MAAM,CAAC;EACpE,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEpD,KAAK,EAAEd,SAAS,CAACoE,KAAK,CAAC;IACrBrD,IAAI,EAAEf,SAAS,CAACwD,WAAW;IAC3BN,UAAU,EAAElD,SAAS,CAACwD;EACxB,CAAC,CAAC;EACF;AACF;AACA;EACEiB,EAAE,EAAEzE,SAAS,CAACuE,SAAS,CAAC,CAACvE,SAAS,CAAC0E,OAAO,CAAC1E,SAAS,CAACuE,SAAS,CAAC,CAACvE,SAAS,CAACwE,IAAI,EAAExE,SAAS,CAACkE,MAAM,EAAElE,SAAS,CAACqE,IAAI,CAAC,CAAC,CAAC,EAAErE,SAAS,CAACwE,IAAI,EAAExE,SAAS,CAACkE,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;AACA;AACA;EACErB,mBAAmB,EAAE7C,SAAS,CAACwD,WAAW;EAC1C;AACF;AACA;AACA;EACET,kBAAkB,EAAE/C,SAAS,CAACuE,SAAS,CAAC,CAACvE,SAAS,CAAC2E,MAAM,EAAE3E,SAAS,CAACoE,KAAK,CAAC;IACzEQ,MAAM,EAAE5E,SAAS,CAAC2E,MAAM;IACxBE,KAAK,EAAE7E,SAAS,CAAC2E,MAAM;IACvBG,IAAI,EAAE9E,SAAS,CAAC2E;EAClB,CAAC,CAAC,CAAC;AACL,CAAC,GAAG,KAAK,CAAC;AACV,eAAezC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}