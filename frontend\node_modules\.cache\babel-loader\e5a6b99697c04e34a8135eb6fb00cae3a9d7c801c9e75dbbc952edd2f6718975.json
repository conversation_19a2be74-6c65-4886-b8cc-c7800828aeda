{"ast": null, "code": "import { frame, cancelFrame, frameData, time } from 'motion-dom';\nconst frameloopDriver = update => {\n  const passTimestamp = ({\n    timestamp\n  }) => update(timestamp);\n  return {\n    start: () => frame.update(passTimestamp, true),\n    stop: () => cancelFrame(passTimestamp),\n    /**\n     * If we're processing this frame we can use the\n     * framelocked timestamp to keep things in sync.\n     */\n    now: () => frameData.isProcessing ? frameData.timestamp : time.now()\n  };\n};\nexport { frameloopDriver };", "map": {"version": 3, "names": ["frame", "cancelFrame", "frameData", "time", "frameloopDriver", "update", "passTimestamp", "timestamp", "start", "stop", "now", "isProcessing"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/framer-motion/dist/es/animation/animators/drivers/driver-frameloop.mjs"], "sourcesContent": ["import { frame, cancelFrame, frameData, time } from 'motion-dom';\n\nconst frameloopDriver = (update) => {\n    const passTimestamp = ({ timestamp }) => update(timestamp);\n    return {\n        start: () => frame.update(passTimestamp, true),\n        stop: () => cancelFrame(passTimestamp),\n        /**\n         * If we're processing this frame we can use the\n         * framelocked timestamp to keep things in sync.\n         */\n        now: () => (frameData.isProcessing ? frameData.timestamp : time.now()),\n    };\n};\n\nexport { frameloopDriver };\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,WAAW,EAAEC,SAAS,EAAEC,IAAI,QAAQ,YAAY;AAEhE,MAAMC,eAAe,GAAIC,MAAM,IAAK;EAChC,MAAMC,aAAa,GAAGA,CAAC;IAAEC;EAAU,CAAC,KAAKF,MAAM,CAACE,SAAS,CAAC;EAC1D,OAAO;IACHC,KAAK,EAAEA,CAAA,KAAMR,KAAK,CAACK,MAAM,CAACC,aAAa,EAAE,IAAI,CAAC;IAC9CG,IAAI,EAAEA,CAAA,KAAMR,WAAW,CAACK,aAAa,CAAC;IACtC;AACR;AACA;AACA;IACQI,GAAG,EAAEA,CAAA,KAAOR,SAAS,CAACS,YAAY,GAAGT,SAAS,CAACK,SAAS,GAAGJ,IAAI,CAACO,GAAG,CAAC;EACxE,CAAC;AACL,CAAC;AAED,SAASN,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}