{"ast": null, "code": "import { supportsScrollTimeline } from '../utils/supports/scroll-timeline.mjs';\nclass GroupAnimation {\n  constructor(animations) {\n    // Bound to accomodate common `return animation.stop` pattern\n    this.stop = () => this.runAll(\"stop\");\n    this.animations = animations.filter(Boolean);\n  }\n  get finished() {\n    return Promise.all(this.animations.map(animation => animation.finished));\n  }\n  /**\n   * TODO: Filter out cancelled or stopped animations before returning\n   */\n  getAll(propName) {\n    return this.animations[0][propName];\n  }\n  setAll(propName, newValue) {\n    for (let i = 0; i < this.animations.length; i++) {\n      this.animations[i][propName] = newValue;\n    }\n  }\n  attachTimeline(timeline, fallback) {\n    const subscriptions = this.animations.map(animation => {\n      if (supportsScrollTimeline() && animation.attachTimeline) {\n        return animation.attachTimeline(timeline);\n      } else if (typeof fallback === \"function\") {\n        return fallback(animation);\n      }\n    });\n    return () => {\n      subscriptions.forEach((cancel, i) => {\n        cancel && cancel();\n        this.animations[i].stop();\n      });\n    };\n  }\n  get time() {\n    return this.getAll(\"time\");\n  }\n  set time(time) {\n    this.setAll(\"time\", time);\n  }\n  get speed() {\n    return this.getAll(\"speed\");\n  }\n  set speed(speed) {\n    this.setAll(\"speed\", speed);\n  }\n  get startTime() {\n    return this.getAll(\"startTime\");\n  }\n  get duration() {\n    let max = 0;\n    for (let i = 0; i < this.animations.length; i++) {\n      max = Math.max(max, this.animations[i].duration);\n    }\n    return max;\n  }\n  runAll(methodName) {\n    this.animations.forEach(controls => controls[methodName]());\n  }\n  flatten() {\n    this.runAll(\"flatten\");\n  }\n  play() {\n    this.runAll(\"play\");\n  }\n  pause() {\n    this.runAll(\"pause\");\n  }\n  cancel() {\n    this.runAll(\"cancel\");\n  }\n  complete() {\n    this.runAll(\"complete\");\n  }\n}\nexport { GroupAnimation };", "map": {"version": 3, "names": ["supportsScrollTimeline", "GroupAnimation", "constructor", "animations", "stop", "runAll", "filter", "Boolean", "finished", "Promise", "all", "map", "animation", "getAll", "propName", "setAll", "newValue", "i", "length", "attachTimeline", "timeline", "fallback", "subscriptions", "for<PERSON>ach", "cancel", "time", "speed", "startTime", "duration", "max", "Math", "methodName", "controls", "flatten", "play", "pause", "complete"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/motion-dom/dist/es/animation/GroupAnimation.mjs"], "sourcesContent": ["import { supportsScrollTimeline } from '../utils/supports/scroll-timeline.mjs';\n\nclass GroupAnimation {\n    constructor(animations) {\n        // Bound to accomodate common `return animation.stop` pattern\n        this.stop = () => this.runAll(\"stop\");\n        this.animations = animations.filter(Boolean);\n    }\n    get finished() {\n        return Promise.all(this.animations.map((animation) => animation.finished));\n    }\n    /**\n     * TODO: Filter out cancelled or stopped animations before returning\n     */\n    getAll(propName) {\n        return this.animations[0][propName];\n    }\n    setAll(propName, newValue) {\n        for (let i = 0; i < this.animations.length; i++) {\n            this.animations[i][propName] = newValue;\n        }\n    }\n    attachTimeline(timeline, fallback) {\n        const subscriptions = this.animations.map((animation) => {\n            if (supportsScrollTimeline() && animation.attachTimeline) {\n                return animation.attachTimeline(timeline);\n            }\n            else if (typeof fallback === \"function\") {\n                return fallback(animation);\n            }\n        });\n        return () => {\n            subscriptions.forEach((cancel, i) => {\n                cancel && cancel();\n                this.animations[i].stop();\n            });\n        };\n    }\n    get time() {\n        return this.getAll(\"time\");\n    }\n    set time(time) {\n        this.setAll(\"time\", time);\n    }\n    get speed() {\n        return this.getAll(\"speed\");\n    }\n    set speed(speed) {\n        this.setAll(\"speed\", speed);\n    }\n    get startTime() {\n        return this.getAll(\"startTime\");\n    }\n    get duration() {\n        let max = 0;\n        for (let i = 0; i < this.animations.length; i++) {\n            max = Math.max(max, this.animations[i].duration);\n        }\n        return max;\n    }\n    runAll(methodName) {\n        this.animations.forEach((controls) => controls[methodName]());\n    }\n    flatten() {\n        this.runAll(\"flatten\");\n    }\n    play() {\n        this.runAll(\"play\");\n    }\n    pause() {\n        this.runAll(\"pause\");\n    }\n    cancel() {\n        this.runAll(\"cancel\");\n    }\n    complete() {\n        this.runAll(\"complete\");\n    }\n}\n\nexport { GroupAnimation };\n"], "mappings": "AAAA,SAASA,sBAAsB,QAAQ,uCAAuC;AAE9E,MAAMC,cAAc,CAAC;EACjBC,WAAWA,CAACC,UAAU,EAAE;IACpB;IACA,IAAI,CAACC,IAAI,GAAG,MAAM,IAAI,CAACC,MAAM,CAAC,MAAM,CAAC;IACrC,IAAI,CAACF,UAAU,GAAGA,UAAU,CAACG,MAAM,CAACC,OAAO,CAAC;EAChD;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAOC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACP,UAAU,CAACQ,GAAG,CAAEC,SAAS,IAAKA,SAAS,CAACJ,QAAQ,CAAC,CAAC;EAC9E;EACA;AACJ;AACA;EACIK,MAAMA,CAACC,QAAQ,EAAE;IACb,OAAO,IAAI,CAACX,UAAU,CAAC,CAAC,CAAC,CAACW,QAAQ,CAAC;EACvC;EACAC,MAAMA,CAACD,QAAQ,EAAEE,QAAQ,EAAE;IACvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACd,UAAU,CAACe,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7C,IAAI,CAACd,UAAU,CAACc,CAAC,CAAC,CAACH,QAAQ,CAAC,GAAGE,QAAQ;IAC3C;EACJ;EACAG,cAAcA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;IAC/B,MAAMC,aAAa,GAAG,IAAI,CAACnB,UAAU,CAACQ,GAAG,CAAEC,SAAS,IAAK;MACrD,IAAIZ,sBAAsB,CAAC,CAAC,IAAIY,SAAS,CAACO,cAAc,EAAE;QACtD,OAAOP,SAAS,CAACO,cAAc,CAACC,QAAQ,CAAC;MAC7C,CAAC,MACI,IAAI,OAAOC,QAAQ,KAAK,UAAU,EAAE;QACrC,OAAOA,QAAQ,CAACT,SAAS,CAAC;MAC9B;IACJ,CAAC,CAAC;IACF,OAAO,MAAM;MACTU,aAAa,CAACC,OAAO,CAAC,CAACC,MAAM,EAAEP,CAAC,KAAK;QACjCO,MAAM,IAAIA,MAAM,CAAC,CAAC;QAClB,IAAI,CAACrB,UAAU,CAACc,CAAC,CAAC,CAACb,IAAI,CAAC,CAAC;MAC7B,CAAC,CAAC;IACN,CAAC;EACL;EACA,IAAIqB,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACZ,MAAM,CAAC,MAAM,CAAC;EAC9B;EACA,IAAIY,IAAIA,CAACA,IAAI,EAAE;IACX,IAAI,CAACV,MAAM,CAAC,MAAM,EAAEU,IAAI,CAAC;EAC7B;EACA,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACb,MAAM,CAAC,OAAO,CAAC;EAC/B;EACA,IAAIa,KAAKA,CAACA,KAAK,EAAE;IACb,IAAI,CAACX,MAAM,CAAC,OAAO,EAAEW,KAAK,CAAC;EAC/B;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACd,MAAM,CAAC,WAAW,CAAC;EACnC;EACA,IAAIe,QAAQA,CAAA,EAAG;IACX,IAAIC,GAAG,GAAG,CAAC;IACX,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACd,UAAU,CAACe,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7CY,GAAG,GAAGC,IAAI,CAACD,GAAG,CAACA,GAAG,EAAE,IAAI,CAAC1B,UAAU,CAACc,CAAC,CAAC,CAACW,QAAQ,CAAC;IACpD;IACA,OAAOC,GAAG;EACd;EACAxB,MAAMA,CAAC0B,UAAU,EAAE;IACf,IAAI,CAAC5B,UAAU,CAACoB,OAAO,CAAES,QAAQ,IAAKA,QAAQ,CAACD,UAAU,CAAC,CAAC,CAAC,CAAC;EACjE;EACAE,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC5B,MAAM,CAAC,SAAS,CAAC;EAC1B;EACA6B,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC7B,MAAM,CAAC,MAAM,CAAC;EACvB;EACA8B,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC9B,MAAM,CAAC,OAAO,CAAC;EACxB;EACAmB,MAAMA,CAAA,EAAG;IACL,IAAI,CAACnB,MAAM,CAAC,QAAQ,CAAC;EACzB;EACA+B,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC/B,MAAM,CAAC,UAAU,CAAC;EAC3B;AACJ;AAEA,SAASJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}