{"ast": null, "code": "export var top = 'top';\nexport var bottom = 'bottom';\nexport var right = 'right';\nexport var left = 'left';\nexport var auto = 'auto';\nexport var basePlacements = [top, bottom, right, left];\nexport var start = 'start';\nexport var end = 'end';\nexport var clippingParents = 'clippingParents';\nexport var viewport = 'viewport';\nexport var popper = 'popper';\nexport var reference = 'reference';\nexport var variationPlacements = /*#__PURE__*/basePlacements.reduce(function (acc, placement) {\n  return acc.concat([placement + \"-\" + start, placement + \"-\" + end]);\n}, []);\nexport var placements = /*#__PURE__*/[].concat(basePlacements, [auto]).reduce(function (acc, placement) {\n  return acc.concat([placement, placement + \"-\" + start, placement + \"-\" + end]);\n}, []); // modifiers that need to read the DOM\n\nexport var beforeRead = 'beforeRead';\nexport var read = 'read';\nexport var afterRead = 'afterRead'; // pure-logic modifiers\n\nexport var beforeMain = 'beforeMain';\nexport var main = 'main';\nexport var afterMain = 'afterMain'; // modifier with the purpose to write to the DOM (or write into a framework state)\n\nexport var beforeWrite = 'beforeWrite';\nexport var write = 'write';\nexport var afterWrite = 'afterWrite';\nexport var modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];", "map": {"version": 3, "names": ["top", "bottom", "right", "left", "auto", "basePlacements", "start", "end", "clippingParents", "viewport", "popper", "reference", "variationPlacements", "reduce", "acc", "placement", "concat", "placements", "beforeRead", "read", "afterRead", "<PERSON><PERSON><PERSON>", "main", "<PERSON><PERSON><PERSON>", "beforeWrite", "write", "afterWrite", "modifierPhases"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/@popperjs/core/lib/enums.js"], "sourcesContent": ["export var top = 'top';\nexport var bottom = 'bottom';\nexport var right = 'right';\nexport var left = 'left';\nexport var auto = 'auto';\nexport var basePlacements = [top, bottom, right, left];\nexport var start = 'start';\nexport var end = 'end';\nexport var clippingParents = 'clippingParents';\nexport var viewport = 'viewport';\nexport var popper = 'popper';\nexport var reference = 'reference';\nexport var variationPlacements = /*#__PURE__*/basePlacements.reduce(function (acc, placement) {\n  return acc.concat([placement + \"-\" + start, placement + \"-\" + end]);\n}, []);\nexport var placements = /*#__PURE__*/[].concat(basePlacements, [auto]).reduce(function (acc, placement) {\n  return acc.concat([placement, placement + \"-\" + start, placement + \"-\" + end]);\n}, []); // modifiers that need to read the DOM\n\nexport var beforeRead = 'beforeRead';\nexport var read = 'read';\nexport var afterRead = 'afterRead'; // pure-logic modifiers\n\nexport var beforeMain = 'beforeMain';\nexport var main = 'main';\nexport var afterMain = 'afterMain'; // modifier with the purpose to write to the DOM (or write into a framework state)\n\nexport var beforeWrite = 'beforeWrite';\nexport var write = 'write';\nexport var afterWrite = 'afterWrite';\nexport var modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];"], "mappings": "AAAA,OAAO,IAAIA,GAAG,GAAG,KAAK;AACtB,OAAO,IAAIC,MAAM,GAAG,QAAQ;AAC5B,OAAO,IAAIC,KAAK,GAAG,OAAO;AAC1B,OAAO,IAAIC,IAAI,GAAG,MAAM;AACxB,OAAO,IAAIC,IAAI,GAAG,MAAM;AACxB,OAAO,IAAIC,cAAc,GAAG,CAACL,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,CAAC;AACtD,OAAO,IAAIG,KAAK,GAAG,OAAO;AAC1B,OAAO,IAAIC,GAAG,GAAG,KAAK;AACtB,OAAO,IAAIC,eAAe,GAAG,iBAAiB;AAC9C,OAAO,IAAIC,QAAQ,GAAG,UAAU;AAChC,OAAO,IAAIC,MAAM,GAAG,QAAQ;AAC5B,OAAO,IAAIC,SAAS,GAAG,WAAW;AAClC,OAAO,IAAIC,mBAAmB,GAAG,aAAaP,cAAc,CAACQ,MAAM,CAAC,UAAUC,GAAG,EAAEC,SAAS,EAAE;EAC5F,OAAOD,GAAG,CAACE,MAAM,CAAC,CAACD,SAAS,GAAG,GAAG,GAAGT,KAAK,EAAES,SAAS,GAAG,GAAG,GAAGR,GAAG,CAAC,CAAC;AACrE,CAAC,EAAE,EAAE,CAAC;AACN,OAAO,IAAIU,UAAU,GAAG,aAAa,EAAE,CAACD,MAAM,CAACX,cAAc,EAAE,CAACD,IAAI,CAAC,CAAC,CAACS,MAAM,CAAC,UAAUC,GAAG,EAAEC,SAAS,EAAE;EACtG,OAAOD,GAAG,CAACE,MAAM,CAAC,CAACD,SAAS,EAAEA,SAAS,GAAG,GAAG,GAAGT,KAAK,EAAES,SAAS,GAAG,GAAG,GAAGR,GAAG,CAAC,CAAC;AAChF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;AAER,OAAO,IAAIW,UAAU,GAAG,YAAY;AACpC,OAAO,IAAIC,IAAI,GAAG,MAAM;AACxB,OAAO,IAAIC,SAAS,GAAG,WAAW,CAAC,CAAC;;AAEpC,OAAO,IAAIC,UAAU,GAAG,YAAY;AACpC,OAAO,IAAIC,IAAI,GAAG,MAAM;AACxB,OAAO,IAAIC,SAAS,GAAG,WAAW,CAAC,CAAC;;AAEpC,OAAO,IAAIC,WAAW,GAAG,aAAa;AACtC,OAAO,IAAIC,KAAK,GAAG,OAAO;AAC1B,OAAO,IAAIC,UAAU,GAAG,YAAY;AACpC,OAAO,IAAIC,cAAc,GAAG,CAACT,UAAU,EAAEC,IAAI,EAAEC,SAAS,EAAEC,UAAU,EAAEC,IAAI,EAAEC,SAAS,EAAEC,WAAW,EAAEC,KAAK,EAAEC,UAAU,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}