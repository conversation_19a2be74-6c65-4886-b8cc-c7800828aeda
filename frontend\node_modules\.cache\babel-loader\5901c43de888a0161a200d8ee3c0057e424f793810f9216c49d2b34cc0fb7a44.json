{"ast": null, "code": "import { createUnarySpacing } from \"../spacing/index.js\";\n\n// The different signatures imply different meaning for their arguments that can't be expressed structurally.\n// We express the difference with variable names.\n\nexport default function createSpacing(spacingInput = 8,\n// Material Design layouts are visually balanced. Most measurements align to an 8dp grid, which aligns both spacing and the overall layout.\n// Smaller components, such as icons, can align to a 4dp grid.\n// https://m2.material.io/design/layout/understanding-layout.html\ntransform = createUnarySpacing({\n  spacing: spacingInput\n})) {\n  // Already transformed.\n  if (spacingInput.mui) {\n    return spacingInput;\n  }\n  const spacing = (...argsInput) => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (!(argsInput.length <= 4)) {\n        console.error(`MUI: Too many arguments provided, expected between 0 and 4, got ${argsInput.length}`);\n      }\n    }\n    const args = argsInput.length === 0 ? [1] : argsInput;\n    return args.map(argument => {\n      const output = transform(argument);\n      return typeof output === 'number' ? `${output}px` : output;\n    }).join(' ');\n  };\n  spacing.mui = true;\n  return spacing;\n}", "map": {"version": 3, "names": ["createUnarySpacing", "createSpacing", "spacingInput", "transform", "spacing", "mui", "argsInput", "process", "env", "NODE_ENV", "length", "console", "error", "args", "map", "argument", "output", "join"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/@mui/system/esm/createTheme/createSpacing.js"], "sourcesContent": ["import { createUnarySpacing } from \"../spacing/index.js\";\n\n// The different signatures imply different meaning for their arguments that can't be expressed structurally.\n// We express the difference with variable names.\n\nexport default function createSpacing(spacingInput = 8,\n// Material Design layouts are visually balanced. Most measurements align to an 8dp grid, which aligns both spacing and the overall layout.\n// Smaller components, such as icons, can align to a 4dp grid.\n// https://m2.material.io/design/layout/understanding-layout.html\ntransform = createUnarySpacing({\n  spacing: spacingInput\n})) {\n  // Already transformed.\n  if (spacingInput.mui) {\n    return spacingInput;\n  }\n  const spacing = (...argsInput) => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (!(argsInput.length <= 4)) {\n        console.error(`MUI: Too many arguments provided, expected between 0 and 4, got ${argsInput.length}`);\n      }\n    }\n    const args = argsInput.length === 0 ? [1] : argsInput;\n    return args.map(argument => {\n      const output = transform(argument);\n      return typeof output === 'number' ? `${output}px` : output;\n    }).join(' ');\n  };\n  spacing.mui = true;\n  return spacing;\n}"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,qBAAqB;;AAExD;AACA;;AAEA,eAAe,SAASC,aAAaA,CAACC,YAAY,GAAG,CAAC;AACtD;AACA;AACA;AACAC,SAAS,GAAGH,kBAAkB,CAAC;EAC7BI,OAAO,EAAEF;AACX,CAAC,CAAC,EAAE;EACF;EACA,IAAIA,YAAY,CAACG,GAAG,EAAE;IACpB,OAAOH,YAAY;EACrB;EACA,MAAME,OAAO,GAAGA,CAAC,GAAGE,SAAS,KAAK;IAChC,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI,EAAEH,SAAS,CAACI,MAAM,IAAI,CAAC,CAAC,EAAE;QAC5BC,OAAO,CAACC,KAAK,CAAC,mEAAmEN,SAAS,CAACI,MAAM,EAAE,CAAC;MACtG;IACF;IACA,MAAMG,IAAI,GAAGP,SAAS,CAACI,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,GAAGJ,SAAS;IACrD,OAAOO,IAAI,CAACC,GAAG,CAACC,QAAQ,IAAI;MAC1B,MAAMC,MAAM,GAAGb,SAAS,CAACY,QAAQ,CAAC;MAClC,OAAO,OAAOC,MAAM,KAAK,QAAQ,GAAG,GAAGA,MAAM,IAAI,GAAGA,MAAM;IAC5D,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EACd,CAAC;EACDb,OAAO,CAACC,GAAG,GAAG,IAAI;EAClB,OAAOD,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}