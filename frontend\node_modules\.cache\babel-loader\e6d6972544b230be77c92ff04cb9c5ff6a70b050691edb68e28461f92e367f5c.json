{"ast": null, "code": "export { default } from \"./styleFunctionSx.js\";\nexport { unstable_createStyleFunctionSx } from \"./styleFunctionSx.js\";\nexport { default as extendSxProp } from \"./extendSxProp.js\";\nexport { default as unstable_defaultSxConfig } from \"./defaultSxConfig.js\";", "map": {"version": 3, "names": ["default", "unstable_createStyleFunctionSx", "extendSxProp", "unstable_defaultSxConfig"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/@mui/system/esm/styleFunctionSx/index.js"], "sourcesContent": ["export { default } from \"./styleFunctionSx.js\";\nexport { unstable_createStyleFunctionSx } from \"./styleFunctionSx.js\";\nexport { default as extendSxProp } from \"./extendSxProp.js\";\nexport { default as unstable_defaultSxConfig } from \"./defaultSxConfig.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,sBAAsB;AAC9C,SAASC,8BAA8B,QAAQ,sBAAsB;AACrE,SAASD,OAAO,IAAIE,YAAY,QAAQ,mBAAmB;AAC3D,SAASF,OAAO,IAAIG,wBAAwB,QAAQ,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}