{"ast": null, "code": "import { isMotionValue } from '../../value/utils/is-motion-value.mjs';\nimport { VisualElement } from '../VisualElement.mjs';\nimport { DOMKeyframesResolver } from './DOMKeyframesResolver.mjs';\nclass DOMVisualElement extends VisualElement {\n  constructor() {\n    super(...arguments);\n    this.KeyframeResolver = DOMKeyframesResolver;\n  }\n  sortInstanceNodePosition(a, b) {\n    /**\n     * compareDocumentPosition returns a bitmask, by using the bitwise &\n     * we're returning true if 2 in that bitmask is set to true. 2 is set\n     * to true if b preceeds a.\n     */\n    return a.compareDocumentPosition(b) & 2 ? 1 : -1;\n  }\n  getBaseTargetFromProps(props, key) {\n    return props.style ? props.style[key] : undefined;\n  }\n  removeValueFromRenderState(key, {\n    vars,\n    style\n  }) {\n    delete vars[key];\n    delete style[key];\n  }\n  handleChildMotionValue() {\n    if (this.childSubscription) {\n      this.childSubscription();\n      delete this.childSubscription;\n    }\n    const {\n      children\n    } = this.props;\n    if (isMotionValue(children)) {\n      this.childSubscription = children.on(\"change\", latest => {\n        if (this.current) {\n          this.current.textContent = `${latest}`;\n        }\n      });\n    }\n  }\n}\nexport { DOMVisualElement };", "map": {"version": 3, "names": ["isMotionValue", "VisualElement", "DOMKeyframesResolver", "DOMVisualElement", "constructor", "arguments", "KeyframeResolver", "sortInstanceNodePosition", "a", "b", "compareDocumentPosition", "getBaseTargetFromProps", "props", "key", "style", "undefined", "removeValueFromRenderState", "vars", "handleChildMotionValue", "childSubscription", "children", "on", "latest", "current", "textContent"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/framer-motion/dist/es/render/dom/DOMVisualElement.mjs"], "sourcesContent": ["import { isMotionValue } from '../../value/utils/is-motion-value.mjs';\nimport { VisualElement } from '../VisualElement.mjs';\nimport { DOMKeyframesResolver } from './DOMKeyframesResolver.mjs';\n\nclass DOMVisualElement extends VisualElement {\n    constructor() {\n        super(...arguments);\n        this.KeyframeResolver = DOMKeyframesResolver;\n    }\n    sortInstanceNodePosition(a, b) {\n        /**\n         * compareDocumentPosition returns a bitmask, by using the bitwise &\n         * we're returning true if 2 in that bitmask is set to true. 2 is set\n         * to true if b preceeds a.\n         */\n        return a.compareDocumentPosition(b) & 2 ? 1 : -1;\n    }\n    getBaseTargetFromProps(props, key) {\n        return props.style\n            ? props.style[key]\n            : undefined;\n    }\n    removeValueFromRenderState(key, { vars, style }) {\n        delete vars[key];\n        delete style[key];\n    }\n    handleChildMotionValue() {\n        if (this.childSubscription) {\n            this.childSubscription();\n            delete this.childSubscription;\n        }\n        const { children } = this.props;\n        if (isMotionValue(children)) {\n            this.childSubscription = children.on(\"change\", (latest) => {\n                if (this.current) {\n                    this.current.textContent = `${latest}`;\n                }\n            });\n        }\n    }\n}\n\nexport { DOMVisualElement };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,uCAAuC;AACrE,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,oBAAoB,QAAQ,4BAA4B;AAEjE,MAAMC,gBAAgB,SAASF,aAAa,CAAC;EACzCG,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,gBAAgB,GAAGJ,oBAAoB;EAChD;EACAK,wBAAwBA,CAACC,CAAC,EAAEC,CAAC,EAAE;IAC3B;AACR;AACA;AACA;AACA;IACQ,OAAOD,CAAC,CAACE,uBAAuB,CAACD,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACpD;EACAE,sBAAsBA,CAACC,KAAK,EAAEC,GAAG,EAAE;IAC/B,OAAOD,KAAK,CAACE,KAAK,GACZF,KAAK,CAACE,KAAK,CAACD,GAAG,CAAC,GAChBE,SAAS;EACnB;EACAC,0BAA0BA,CAACH,GAAG,EAAE;IAAEI,IAAI;IAAEH;EAAM,CAAC,EAAE;IAC7C,OAAOG,IAAI,CAACJ,GAAG,CAAC;IAChB,OAAOC,KAAK,CAACD,GAAG,CAAC;EACrB;EACAK,sBAAsBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAACC,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAAC,CAAC;MACxB,OAAO,IAAI,CAACA,iBAAiB;IACjC;IACA,MAAM;MAAEC;IAAS,CAAC,GAAG,IAAI,CAACR,KAAK;IAC/B,IAAIZ,aAAa,CAACoB,QAAQ,CAAC,EAAE;MACzB,IAAI,CAACD,iBAAiB,GAAGC,QAAQ,CAACC,EAAE,CAAC,QAAQ,EAAGC,MAAM,IAAK;QACvD,IAAI,IAAI,CAACC,OAAO,EAAE;UACd,IAAI,CAACA,OAAO,CAACC,WAAW,GAAG,GAAGF,MAAM,EAAE;QAC1C;MACJ,CAAC,CAAC;IACN;EACJ;AACJ;AAEA,SAASnB,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}