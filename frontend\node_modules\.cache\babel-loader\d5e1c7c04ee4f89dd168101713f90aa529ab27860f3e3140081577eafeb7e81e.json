{"ast": null, "code": "import createPalette from \"./createPalette.js\";\nimport getOverlayAlpha from \"./getOverlayAlpha.js\";\nconst defaultDarkOverlays = [...Array(25)].map((_, index) => {\n  if (index === 0) {\n    return 'none';\n  }\n  const overlay = getOverlayAlpha(index);\n  return `linear-gradient(rgba(255 255 255 / ${overlay}), rgba(255 255 255 / ${overlay}))`;\n});\nexport function getOpacity(mode) {\n  return {\n    inputPlaceholder: mode === 'dark' ? 0.5 : 0.42,\n    inputUnderline: mode === 'dark' ? 0.7 : 0.42,\n    switchTrackDisabled: mode === 'dark' ? 0.2 : 0.12,\n    switchTrack: mode === 'dark' ? 0.3 : 0.38\n  };\n}\nexport function getOverlays(mode) {\n  return mode === 'dark' ? defaultDarkOverlays : [];\n}\nexport default function createColorScheme(options) {\n  const {\n    palette: paletteInput = {\n      mode: 'light'\n    },\n    // need to cast to avoid module augmentation test\n    opacity,\n    overlays,\n    ...rest\n  } = options;\n  const palette = createPalette(paletteInput);\n  return {\n    palette,\n    opacity: {\n      ...getOpacity(palette.mode),\n      ...opacity\n    },\n    overlays: overlays || getOverlays(palette.mode),\n    ...rest\n  };\n}", "map": {"version": 3, "names": ["createPalette", "getOverlayAlpha", "defaultDarkOverlays", "Array", "map", "_", "index", "overlay", "getOpacity", "mode", "inputPlaceholder", "inputUnderline", "switchTrackDisabled", "switchTrack", "getOverlays", "createColorScheme", "options", "palette", "paletteInput", "opacity", "overlays", "rest"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/@mui/material/esm/styles/createColorScheme.js"], "sourcesContent": ["import createPalette from \"./createPalette.js\";\nimport getOverlayAlpha from \"./getOverlayAlpha.js\";\nconst defaultDarkOverlays = [...Array(25)].map((_, index) => {\n  if (index === 0) {\n    return 'none';\n  }\n  const overlay = getOverlayAlpha(index);\n  return `linear-gradient(rgba(255 255 255 / ${overlay}), rgba(255 255 255 / ${overlay}))`;\n});\nexport function getOpacity(mode) {\n  return {\n    inputPlaceholder: mode === 'dark' ? 0.5 : 0.42,\n    inputUnderline: mode === 'dark' ? 0.7 : 0.42,\n    switchTrackDisabled: mode === 'dark' ? 0.2 : 0.12,\n    switchTrack: mode === 'dark' ? 0.3 : 0.38\n  };\n}\nexport function getOverlays(mode) {\n  return mode === 'dark' ? defaultDarkOverlays : [];\n}\nexport default function createColorScheme(options) {\n  const {\n    palette: paletteInput = {\n      mode: 'light'\n    },\n    // need to cast to avoid module augmentation test\n    opacity,\n    overlays,\n    ...rest\n  } = options;\n  const palette = createPalette(paletteInput);\n  return {\n    palette,\n    opacity: {\n      ...getOpacity(palette.mode),\n      ...opacity\n    },\n    overlays: overlays || getOverlays(palette.mode),\n    ...rest\n  };\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,oBAAoB;AAC9C,OAAOC,eAAe,MAAM,sBAAsB;AAClD,MAAMC,mBAAmB,GAAG,CAAC,GAAGC,KAAK,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,KAAK;EAC3D,IAAIA,KAAK,KAAK,CAAC,EAAE;IACf,OAAO,MAAM;EACf;EACA,MAAMC,OAAO,GAAGN,eAAe,CAACK,KAAK,CAAC;EACtC,OAAO,sCAAsCC,OAAO,yBAAyBA,OAAO,IAAI;AAC1F,CAAC,CAAC;AACF,OAAO,SAASC,UAAUA,CAACC,IAAI,EAAE;EAC/B,OAAO;IACLC,gBAAgB,EAAED,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG,IAAI;IAC9CE,cAAc,EAAEF,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG,IAAI;IAC5CG,mBAAmB,EAAEH,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG,IAAI;IACjDI,WAAW,EAAEJ,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG;EACvC,CAAC;AACH;AACA,OAAO,SAASK,WAAWA,CAACL,IAAI,EAAE;EAChC,OAAOA,IAAI,KAAK,MAAM,GAAGP,mBAAmB,GAAG,EAAE;AACnD;AACA,eAAe,SAASa,iBAAiBA,CAACC,OAAO,EAAE;EACjD,MAAM;IACJC,OAAO,EAAEC,YAAY,GAAG;MACtBT,IAAI,EAAE;IACR,CAAC;IACD;IACAU,OAAO;IACPC,QAAQ;IACR,GAAGC;EACL,CAAC,GAAGL,OAAO;EACX,MAAMC,OAAO,GAAGjB,aAAa,CAACkB,YAAY,CAAC;EAC3C,OAAO;IACLD,OAAO;IACPE,OAAO,EAAE;MACP,GAAGX,UAAU,CAACS,OAAO,CAACR,IAAI,CAAC;MAC3B,GAAGU;IACL,CAAC;IACDC,QAAQ,EAAEA,QAAQ,IAAIN,WAAW,CAACG,OAAO,CAACR,IAAI,CAAC;IAC/C,GAAGY;EACL,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}