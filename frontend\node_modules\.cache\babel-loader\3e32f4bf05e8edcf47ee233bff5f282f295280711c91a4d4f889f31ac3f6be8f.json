{"ast": null, "code": "import { noop } from 'motion-utils';\nimport { addToQueue } from './queue.mjs';\nclass ViewTransitionBuilder {\n  constructor(update, options = {}) {\n    this.currentTarget = \"root\";\n    this.targets = new Map();\n    this.notifyReady = noop;\n    this.readyPromise = new Promise(resolve => {\n      this.notifyReady = resolve;\n    });\n    this.update = update;\n    this.options = {\n      interrupt: \"wait\",\n      ...options\n    };\n    addToQueue(this);\n  }\n  get(selector) {\n    this.currentTarget = selector;\n    return this;\n  }\n  layout(keyframes, options) {\n    this.updateTarget(\"layout\", keyframes, options);\n    return this;\n  }\n  new(keyframes, options) {\n    this.updateTarget(\"new\", keyframes, options);\n    return this;\n  }\n  old(keyframes, options) {\n    this.updateTarget(\"old\", keyframes, options);\n    return this;\n  }\n  enter(keyframes, options) {\n    this.updateTarget(\"enter\", keyframes, options);\n    return this;\n  }\n  exit(keyframes, options) {\n    this.updateTarget(\"exit\", keyframes, options);\n    return this;\n  }\n  crossfade(options) {\n    this.updateTarget(\"enter\", {\n      opacity: 1\n    }, options);\n    this.updateTarget(\"exit\", {\n      opacity: 0\n    }, options);\n    return this;\n  }\n  updateTarget(target, keyframes, options = {}) {\n    const {\n      currentTarget,\n      targets\n    } = this;\n    if (!targets.has(currentTarget)) {\n      targets.set(currentTarget, {});\n    }\n    const targetData = targets.get(currentTarget);\n    targetData[target] = {\n      keyframes,\n      options\n    };\n  }\n  then(resolve, reject) {\n    return this.readyPromise.then(resolve, reject);\n  }\n}\nfunction animateView(update, defaultOptions = {}) {\n  return new ViewTransitionBuilder(update, defaultOptions);\n}\nexport { ViewTransitionBuilder, animateView };", "map": {"version": 3, "names": ["noop", "addToQueue", "ViewTransitionBuilder", "constructor", "update", "options", "currentTarget", "targets", "Map", "notifyReady", "readyPromise", "Promise", "resolve", "interrupt", "get", "selector", "layout", "keyframes", "updateTarget", "new", "old", "enter", "exit", "crossfade", "opacity", "target", "has", "set", "targetData", "then", "reject", "animate<PERSON><PERSON><PERSON>", "defaultOptions"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/motion-dom/dist/es/view/index.mjs"], "sourcesContent": ["import { noop } from 'motion-utils';\nimport { addToQueue } from './queue.mjs';\n\nclass ViewTransitionBuilder {\n    constructor(update, options = {}) {\n        this.currentTarget = \"root\";\n        this.targets = new Map();\n        this.notifyReady = noop;\n        this.readyPromise = new Promise((resolve) => {\n            this.notifyReady = resolve;\n        });\n        this.update = update;\n        this.options = {\n            interrupt: \"wait\",\n            ...options,\n        };\n        addToQueue(this);\n    }\n    get(selector) {\n        this.currentTarget = selector;\n        return this;\n    }\n    layout(keyframes, options) {\n        this.updateTarget(\"layout\", keyframes, options);\n        return this;\n    }\n    new(keyframes, options) {\n        this.updateTarget(\"new\", keyframes, options);\n        return this;\n    }\n    old(keyframes, options) {\n        this.updateTarget(\"old\", keyframes, options);\n        return this;\n    }\n    enter(keyframes, options) {\n        this.updateTarget(\"enter\", keyframes, options);\n        return this;\n    }\n    exit(keyframes, options) {\n        this.updateTarget(\"exit\", keyframes, options);\n        return this;\n    }\n    crossfade(options) {\n        this.updateTarget(\"enter\", { opacity: 1 }, options);\n        this.updateTarget(\"exit\", { opacity: 0 }, options);\n        return this;\n    }\n    updateTarget(target, keyframes, options = {}) {\n        const { currentTarget, targets } = this;\n        if (!targets.has(currentTarget)) {\n            targets.set(currentTarget, {});\n        }\n        const targetData = targets.get(currentTarget);\n        targetData[target] = { keyframes, options };\n    }\n    then(resolve, reject) {\n        return this.readyPromise.then(resolve, reject);\n    }\n}\nfunction animateView(update, defaultOptions = {}) {\n    return new ViewTransitionBuilder(update, defaultOptions);\n}\n\nexport { ViewTransitionBuilder, animateView };\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,cAAc;AACnC,SAASC,UAAU,QAAQ,aAAa;AAExC,MAAMC,qBAAqB,CAAC;EACxBC,WAAWA,CAACC,MAAM,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC9B,IAAI,CAACC,aAAa,GAAG,MAAM;IAC3B,IAAI,CAACC,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;IACxB,IAAI,CAACC,WAAW,GAAGT,IAAI;IACvB,IAAI,CAACU,YAAY,GAAG,IAAIC,OAAO,CAAEC,OAAO,IAAK;MACzC,IAAI,CAACH,WAAW,GAAGG,OAAO;IAC9B,CAAC,CAAC;IACF,IAAI,CAACR,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,OAAO,GAAG;MACXQ,SAAS,EAAE,MAAM;MACjB,GAAGR;IACP,CAAC;IACDJ,UAAU,CAAC,IAAI,CAAC;EACpB;EACAa,GAAGA,CAACC,QAAQ,EAAE;IACV,IAAI,CAACT,aAAa,GAAGS,QAAQ;IAC7B,OAAO,IAAI;EACf;EACAC,MAAMA,CAACC,SAAS,EAAEZ,OAAO,EAAE;IACvB,IAAI,CAACa,YAAY,CAAC,QAAQ,EAAED,SAAS,EAAEZ,OAAO,CAAC;IAC/C,OAAO,IAAI;EACf;EACAc,GAAGA,CAACF,SAAS,EAAEZ,OAAO,EAAE;IACpB,IAAI,CAACa,YAAY,CAAC,KAAK,EAAED,SAAS,EAAEZ,OAAO,CAAC;IAC5C,OAAO,IAAI;EACf;EACAe,GAAGA,CAACH,SAAS,EAAEZ,OAAO,EAAE;IACpB,IAAI,CAACa,YAAY,CAAC,KAAK,EAAED,SAAS,EAAEZ,OAAO,CAAC;IAC5C,OAAO,IAAI;EACf;EACAgB,KAAKA,CAACJ,SAAS,EAAEZ,OAAO,EAAE;IACtB,IAAI,CAACa,YAAY,CAAC,OAAO,EAAED,SAAS,EAAEZ,OAAO,CAAC;IAC9C,OAAO,IAAI;EACf;EACAiB,IAAIA,CAACL,SAAS,EAAEZ,OAAO,EAAE;IACrB,IAAI,CAACa,YAAY,CAAC,MAAM,EAAED,SAAS,EAAEZ,OAAO,CAAC;IAC7C,OAAO,IAAI;EACf;EACAkB,SAASA,CAAClB,OAAO,EAAE;IACf,IAAI,CAACa,YAAY,CAAC,OAAO,EAAE;MAAEM,OAAO,EAAE;IAAE,CAAC,EAAEnB,OAAO,CAAC;IACnD,IAAI,CAACa,YAAY,CAAC,MAAM,EAAE;MAAEM,OAAO,EAAE;IAAE,CAAC,EAAEnB,OAAO,CAAC;IAClD,OAAO,IAAI;EACf;EACAa,YAAYA,CAACO,MAAM,EAAER,SAAS,EAAEZ,OAAO,GAAG,CAAC,CAAC,EAAE;IAC1C,MAAM;MAAEC,aAAa;MAAEC;IAAQ,CAAC,GAAG,IAAI;IACvC,IAAI,CAACA,OAAO,CAACmB,GAAG,CAACpB,aAAa,CAAC,EAAE;MAC7BC,OAAO,CAACoB,GAAG,CAACrB,aAAa,EAAE,CAAC,CAAC,CAAC;IAClC;IACA,MAAMsB,UAAU,GAAGrB,OAAO,CAACO,GAAG,CAACR,aAAa,CAAC;IAC7CsB,UAAU,CAACH,MAAM,CAAC,GAAG;MAAER,SAAS;MAAEZ;IAAQ,CAAC;EAC/C;EACAwB,IAAIA,CAACjB,OAAO,EAAEkB,MAAM,EAAE;IAClB,OAAO,IAAI,CAACpB,YAAY,CAACmB,IAAI,CAACjB,OAAO,EAAEkB,MAAM,CAAC;EAClD;AACJ;AACA,SAASC,WAAWA,CAAC3B,MAAM,EAAE4B,cAAc,GAAG,CAAC,CAAC,EAAE;EAC9C,OAAO,IAAI9B,qBAAqB,CAACE,MAAM,EAAE4B,cAAc,CAAC;AAC5D;AAEA,SAAS9B,qBAAqB,EAAE6B,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}