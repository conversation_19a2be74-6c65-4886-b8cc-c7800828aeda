{"ast": null, "code": "'use client';\n\nimport useId from '@mui/utils/useId';\nexport default useId;", "map": {"version": 3, "names": ["useId"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/@mui/material/esm/utils/useId.js"], "sourcesContent": ["'use client';\n\nimport useId from '@mui/utils/useId';\nexport default useId;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,MAAM,kBAAkB;AACpC,eAAeA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}