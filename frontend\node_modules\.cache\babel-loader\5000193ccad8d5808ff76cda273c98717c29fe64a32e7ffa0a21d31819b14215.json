{"ast": null, "code": "var _jsxFileName = \"D:\\\\Desktop\\\\projects\\\\port1\\\\port\\\\frontend\\\\src\\\\components\\\\Hero.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { TypeAnimation } from \"react-type-animation\";\nimport { Modal, Box, IconButton, Button } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport DownloadIcon from \"@mui/icons-material/Download\";\nimport resumeImage from \"../assets/resume.jpg\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst avatarUrl = process.env.PUBLIC_URL + \"/logo2.png\";\n\n// Responsive breakpoints\nconst useResponsive = () => {\n  _s();\n  const [isMobile, setIsMobile] = React.useState(window.innerWidth <= 768);\n  const [isTablet, setIsTablet] = React.useState(window.innerWidth > 768 && window.innerWidth <= 1024);\n  React.useEffect(() => {\n    const handleResize = () => {\n      setIsMobile(window.innerWidth <= 768);\n      setIsTablet(window.innerWidth > 768 && window.innerWidth <= 1024);\n    };\n    window.addEventListener(\"resize\", handleResize);\n    return () => window.removeEventListener(\"resize\", handleResize);\n  }, []);\n  return {\n    isMobile,\n    isTablet\n  };\n};\n_s(useResponsive, \"F4WheOdlzMN19HQQ5aQkLkav7bY=\");\nexport default function Hero() {\n  _s2();\n  const {\n    isMobile,\n    isTablet\n  } = useResponsive();\n  const [resumeModalOpen, setResumeModalOpen] = useState(false);\n  const handleOpenResumeModal = () => {\n    setResumeModalOpen(true);\n  };\n  const handleCloseResumeModal = () => {\n    setResumeModalOpen(false);\n  };\n  const handleDownload = () => {\n    const link = document.createElement(\"a\");\n    link.href = \"/Abhishek_DS_Resume.pdf\";\n    link.download = \"Abhishek_DS_Resume.pdf\";\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"hero\" // 🔥 This makes your logo link scroll to here\n    ,\n    style: {\n      minHeight: \"100vh\",\n      width: \"100vw\",\n      display: \"flex\",\n      alignItems: \"center\",\n      justifyContent: \"space-between\",\n      backgroundColor: \"#000\",\n      color: \"#fff\",\n      paddingTop: isMobile ? \"60px\" : \"80px\",\n      padding: isMobile ? \"60px 20px 20px 20px\" : \"80px 0 0 0\",\n      boxSizing: \"border-box\",\n      overflow: \"hidden\",\n      flexDirection: isMobile ? \"column\" : \"row\",\n      flexWrap: \"wrap\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: isMobile ? \"none\" : \"1 1 50%\",\n        display: \"flex\",\n        alignItems: isMobile ? \"center\" : \"flex-start\",\n        justifyContent: isMobile ? \"center\" : \"flex-start\",\n        paddingLeft: \"0\",\n        order: isMobile ? 2 : 1,\n        marginBottom: isMobile ? \"2rem\" : \"0\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: avatarUrl,\n        alt: \"Avatar\",\n        style: {\n          height: isMobile ? \"40vh\" : isTablet ? \"70vh\" : \"90vh\",\n          width: \"auto\",\n          maxWidth: isMobile ? \"80%\" : \"100%\",\n          objectFit: \"contain\",\n          filter: \"grayscale(100%) drop-shadow(0 0 35px #e7e6e254)\",\n          borderRadius: \"20px\",\n          transition: \"all 0.3s ease-in-out\",\n          cursor: \"default\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: isMobile ? \"none\" : \"1 1 50%\",\n        display: \"flex\",\n        flexDirection: \"column\",\n        justifyContent: \"center\",\n        alignItems: isMobile ? \"center\" : \"flex-end\",\n        paddingRight: isMobile ? \"0\" : \"5vw\",\n        paddingLeft: isMobile ? \"0\" : \"0\",\n        boxSizing: \"border-box\",\n        order: isMobile ? 1 : 2,\n        width: isMobile ? \"100%\" : \"auto\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: isMobile ? \"center\" : \"right\",\n          maxWidth: \"100%\",\n          width: isMobile ? \"100%\" : \"auto\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            fontSize: isMobile ? \"2.2rem\" : isTablet ? \"2.8rem\" : \"3.2rem\",\n            fontWeight: \"bold\",\n            lineHeight: \"1.2\",\n            marginBottom: \"1rem\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              display: \"block\"\n            },\n            children: \"Hi,\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"I'm \", /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                color: \"#FFD700\"\n              },\n              children: \"Abhishek D S\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TypeAnimation, {\n          sequence: [\"Frontend Developer\", 2000, \"React Enthusiast\", 2000, \"UI/UX Explorer\", 2000],\n          speed: 50,\n          wrapper: \"span\",\n          repeat: Infinity,\n          style: {\n            fontSize: isMobile ? \"1.2rem\" : isTablet ? \"1.4rem\" : \"1.6rem\",\n            color: \"#fff\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: \"2rem\"\n          },\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleOpenResumeModal,\n            style: {\n              fontFamily: \"'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif\",\n              padding: isMobile ? \"12px 24px\" : \"14px 28px\",\n              fontSize: isMobile ? \"1rem\" : \"1.1rem\",\n              fontWeight: 600,\n              letterSpacing: \"-0.01em\",\n              color: \"#000\",\n              backgroundColor: \"#FFD700\",\n              border: \"2px solid #FFD700\",\n              borderRadius: \"12px\",\n              cursor: \"pointer\",\n              transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n              textDecoration: \"none\",\n              display: \"inline-block\",\n              boxShadow: \"0 4px 14px 0 rgba(255, 215, 0, 0.3)\"\n            },\n            onMouseEnter: e => {\n              e.target.style.backgroundColor = \"transparent\";\n              e.target.style.color = \"#FFD700\";\n              e.target.style.transform = \"translateY(-2px) scale(1.02)\";\n              e.target.style.boxShadow = \"0 8px 25px 0 rgba(255, 215, 0, 0.4)\";\n            },\n            onMouseLeave: e => {\n              e.target.style.backgroundColor = \"#FFD700\";\n              e.target.style.color = \"#000\";\n              e.target.style.transform = \"translateY(0px) scale(1)\";\n              e.target.style.boxShadow = \"0 4px 14px 0 rgba(255, 215, 0, 0.3)\";\n            },\n            children: \"View Resume\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      open: resumeModalOpen,\n      onClose: handleCloseResumeModal,\n      sx: {\n        display: \"flex\",\n        alignItems: \"center\",\n        justifyContent: \"center\",\n        padding: isMobile ? \"20px\" : \"40px\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        onClick: handleCloseResumeModal,\n        sx: {\n          position: \"relative\",\n          width: \"100%\",\n          height: \"100%\",\n          display: \"flex\",\n          flexDirection: \"column\",\n          alignItems: \"center\",\n          justifyContent: \"flex-start\",\n          outline: \"none\",\n          overflow: \"hidden\",\n          cursor: \"pointer\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: \"100%\",\n            height: \"calc(100vh - 100px)\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            alignItems: \"center\",\n            justifyContent: \"flex-start\",\n            overflow: \"auto\",\n            paddingTop: isMobile ? \"70px\" : \"90px\",\n            paddingBottom: \"20px\",\n            paddingX: \"20px\",\n            \"&::-webkit-scrollbar\": {\n              width: \"8px\"\n            },\n            \"&::-webkit-scrollbar-track\": {\n              background: \"rgba(255, 255, 255, 0.1)\",\n              borderRadius: \"4px\"\n            },\n            \"&::-webkit-scrollbar-thumb\": {\n              background: \"rgba(255, 215, 0, 0.6)\",\n              borderRadius: \"4px\",\n              \"&:hover\": {\n                background: \"rgba(255, 215, 0, 0.8)\"\n              }\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: resumeImage,\n            alt: \"Abhishek DS Resume\",\n            style: {\n              width: isMobile ? \"100%\" : \"60%\",\n              minWidth: isMobile ? \"100%\" : \"60vw\",\n              height: \"auto\",\n              objectFit: \"contain\",\n              boxShadow: \"0 12px 40px rgba(0, 0, 0, 0.4)\",\n              borderRadius: \"12px\",\n              marginBottom: \"30px\"\n            },\n            onLoad: () => console.log(\"Resume image loaded successfully\"),\n            onError: e => {\n              console.log(\"Resume image failed to load\");\n              e.target.style.display = \"none\";\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleDownload,\n          startIcon: /*#__PURE__*/_jsxDEV(DownloadIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 24\n          }, this),\n          sx: {\n            position: \"fixed\",\n            bottom: isMobile ? 20 : 30,\n            left: \"50%\",\n            transform: \"translateX(-50%)\",\n            zIndex: 1001,\n            fontFamily: \"'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif\",\n            fontSize: isMobile ? \"0.9rem\" : \"1rem\",\n            fontWeight: 600,\n            letterSpacing: \"-0.01em\",\n            px: isMobile ? 3 : 4,\n            py: isMobile ? 1.2 : 1.5,\n            bgcolor: \"#FFD700\",\n            color: \"#000\",\n            borderRadius: \"8px\",\n            textTransform: \"none\",\n            boxShadow: \"0 4px 16px 0 rgba(255, 215, 0, 0.4)\",\n            \"&:hover\": {\n              bgcolor: \"#FFC700\",\n              transform: \"translateX(-50%) translateY(-2px)\",\n              boxShadow: \"0 6px 20px 0 rgba(255, 215, 0, 0.5)\"\n            },\n            transition: \"all 0.2s ease\"\n          },\n          children: \"Download Resume\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 5\n  }, this);\n}\n_s2(Hero, \"+TWjhlsYPAsXcWhiWKWVImmriDQ=\", false, function () {\n  return [useResponsive];\n});\n_c = Hero;\nvar _c;\n$RefreshReg$(_c, \"Hero\");", "map": {"version": 3, "names": ["React", "useState", "TypeAnimation", "Modal", "Box", "IconButton", "<PERSON><PERSON>", "CloseIcon", "DownloadIcon", "resumeImage", "jsxDEV", "_jsxDEV", "avatarUrl", "process", "env", "PUBLIC_URL", "useResponsive", "_s", "isMobile", "setIsMobile", "window", "innerWidth", "isTablet", "setIsTablet", "useEffect", "handleResize", "addEventListener", "removeEventListener", "Hero", "_s2", "resumeModalOpen", "setResumeModalOpen", "handleOpenResumeModal", "handleCloseResumeModal", "handleDownload", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "id", "style", "minHeight", "width", "display", "alignItems", "justifyContent", "backgroundColor", "color", "paddingTop", "padding", "boxSizing", "overflow", "flexDirection", "flexWrap", "children", "flex", "paddingLeft", "order", "marginBottom", "src", "alt", "height", "max<PERSON><PERSON><PERSON>", "objectFit", "filter", "borderRadius", "transition", "cursor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "paddingRight", "textAlign", "fontSize", "fontWeight", "lineHeight", "sequence", "speed", "wrapper", "repeat", "Infinity", "marginTop", "onClick", "fontFamily", "letterSpacing", "border", "textDecoration", "boxShadow", "onMouseEnter", "e", "target", "transform", "onMouseLeave", "open", "onClose", "sx", "position", "outline", "paddingBottom", "paddingX", "background", "min<PERSON><PERSON><PERSON>", "onLoad", "console", "log", "onError", "startIcon", "bottom", "left", "zIndex", "px", "py", "bgcolor", "textTransform", "_c", "$RefreshReg$"], "sources": ["D:/Desktop/projects/port1/port/frontend/src/components/Hero.jsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { TypeAnimation } from \"react-type-animation\";\r\nimport { Modal, Box, IconButton, Button } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport DownloadIcon from \"@mui/icons-material/Download\";\r\nimport resumeImage from \"../assets/resume.jpg\";\r\n\r\nconst avatarUrl = process.env.PUBLIC_URL + \"/logo2.png\";\r\n\r\n// Responsive breakpoints\r\nconst useResponsive = () => {\r\n  const [isMobile, setIsMobile] = React.useState(window.innerWidth <= 768);\r\n  const [isTablet, setIsTablet] = React.useState(\r\n    window.innerWidth > 768 && window.innerWidth <= 1024\r\n  );\r\n\r\n  React.useEffect(() => {\r\n    const handleResize = () => {\r\n      setIsMobile(window.innerWidth <= 768);\r\n      setIsTablet(window.innerWidth > 768 && window.innerWidth <= 1024);\r\n    };\r\n\r\n    window.addEventListener(\"resize\", handleResize);\r\n    return () => window.removeEventListener(\"resize\", handleResize);\r\n  }, []);\r\n\r\n  return { isMobile, isTablet };\r\n};\r\n\r\nexport default function Hero() {\r\n  const { isMobile, isTablet } = useResponsive();\r\n  const [resumeModalOpen, setResumeModalOpen] = useState(false);\r\n\r\n  const handleOpenResumeModal = () => {\r\n    setResumeModalOpen(true);\r\n  };\r\n\r\n  const handleCloseResumeModal = () => {\r\n    setResumeModalOpen(false);\r\n  };\r\n\r\n  const handleDownload = () => {\r\n    const link = document.createElement(\"a\");\r\n    link.href = \"/Abhishek_DS_Resume.pdf\";\r\n    link.download = \"Abhishek_DS_Resume.pdf\";\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n  };\r\n\r\n  return (\r\n    <section\r\n      id=\"hero\" // 🔥 This makes your logo link scroll to here\r\n      style={{\r\n        minHeight: \"100vh\",\r\n        width: \"100vw\",\r\n        display: \"flex\",\r\n        alignItems: \"center\",\r\n        justifyContent: \"space-between\",\r\n        backgroundColor: \"#000\",\r\n        color: \"#fff\",\r\n        paddingTop: isMobile ? \"60px\" : \"80px\",\r\n        padding: isMobile ? \"60px 20px 20px 20px\" : \"80px 0 0 0\",\r\n        boxSizing: \"border-box\",\r\n        overflow: \"hidden\",\r\n        flexDirection: isMobile ? \"column\" : \"row\",\r\n        flexWrap: \"wrap\",\r\n      }}\r\n    >\r\n      {/* Avatar Section */}\r\n      <div\r\n        style={{\r\n          flex: isMobile ? \"none\" : \"1 1 50%\",\r\n          display: \"flex\",\r\n          alignItems: isMobile ? \"center\" : \"flex-start\",\r\n          justifyContent: isMobile ? \"center\" : \"flex-start\",\r\n          paddingLeft: \"0\",\r\n          order: isMobile ? 2 : 1,\r\n          marginBottom: isMobile ? \"2rem\" : \"0\",\r\n        }}\r\n      >\r\n        <img\r\n          src={avatarUrl}\r\n          alt=\"Avatar\"\r\n          style={{\r\n            height: isMobile ? \"40vh\" : isTablet ? \"70vh\" : \"90vh\",\r\n            width: \"auto\",\r\n            maxWidth: isMobile ? \"80%\" : \"100%\",\r\n            objectFit: \"contain\",\r\n            filter: \"grayscale(100%) drop-shadow(0 0 35px #e7e6e254)\",\r\n            borderRadius: \"20px\",\r\n            transition: \"all 0.3s ease-in-out\",\r\n            cursor: \"default\",\r\n          }}\r\n        />\r\n      </div>\r\n\r\n      {/* Text Section */}\r\n      <div\r\n        style={{\r\n          flex: isMobile ? \"none\" : \"1 1 50%\",\r\n          display: \"flex\",\r\n          flexDirection: \"column\",\r\n          justifyContent: \"center\",\r\n          alignItems: isMobile ? \"center\" : \"flex-end\",\r\n          paddingRight: isMobile ? \"0\" : \"5vw\",\r\n          paddingLeft: isMobile ? \"0\" : \"0\",\r\n          boxSizing: \"border-box\",\r\n          order: isMobile ? 1 : 2,\r\n          width: isMobile ? \"100%\" : \"auto\",\r\n        }}\r\n      >\r\n        <div\r\n          style={{\r\n            textAlign: isMobile ? \"center\" : \"right\",\r\n            maxWidth: \"100%\",\r\n            width: isMobile ? \"100%\" : \"auto\",\r\n          }}\r\n        >\r\n          <h1\r\n            style={{\r\n              fontSize: isMobile ? \"2.2rem\" : isTablet ? \"2.8rem\" : \"3.2rem\",\r\n              fontWeight: \"bold\",\r\n              lineHeight: \"1.2\",\r\n              marginBottom: \"1rem\",\r\n            }}\r\n          >\r\n            <span style={{ display: \"block\" }}>Hi,</span>\r\n            <span>\r\n              I'm <span style={{ color: \"#FFD700\" }}>Abhishek D S</span>\r\n            </span>\r\n          </h1>\r\n\r\n          <TypeAnimation\r\n            sequence={[\r\n              \"Frontend Developer\",\r\n              2000,\r\n              \"React Enthusiast\",\r\n              2000,\r\n              \"UI/UX Explorer\",\r\n              2000,\r\n            ]}\r\n            speed={50}\r\n            wrapper=\"span\"\r\n            repeat={Infinity}\r\n            style={{\r\n              fontSize: isMobile ? \"1.2rem\" : isTablet ? \"1.4rem\" : \"1.6rem\",\r\n              color: \"#fff\",\r\n            }}\r\n          />\r\n\r\n          {/* View Resume Button */}\r\n          <div style={{ marginTop: \"2rem\" }}>\r\n            <button\r\n              onClick={handleOpenResumeModal}\r\n              style={{\r\n                fontFamily:\r\n                  \"'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif\",\r\n                padding: isMobile ? \"12px 24px\" : \"14px 28px\",\r\n                fontSize: isMobile ? \"1rem\" : \"1.1rem\",\r\n                fontWeight: 600,\r\n                letterSpacing: \"-0.01em\",\r\n                color: \"#000\",\r\n                backgroundColor: \"#FFD700\",\r\n                border: \"2px solid #FFD700\",\r\n                borderRadius: \"12px\",\r\n                cursor: \"pointer\",\r\n                transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\r\n                textDecoration: \"none\",\r\n                display: \"inline-block\",\r\n                boxShadow: \"0 4px 14px 0 rgba(255, 215, 0, 0.3)\",\r\n              }}\r\n              onMouseEnter={(e) => {\r\n                e.target.style.backgroundColor = \"transparent\";\r\n                e.target.style.color = \"#FFD700\";\r\n                e.target.style.transform = \"translateY(-2px) scale(1.02)\";\r\n                e.target.style.boxShadow =\r\n                  \"0 8px 25px 0 rgba(255, 215, 0, 0.4)\";\r\n              }}\r\n              onMouseLeave={(e) => {\r\n                e.target.style.backgroundColor = \"#FFD700\";\r\n                e.target.style.color = \"#000\";\r\n                e.target.style.transform = \"translateY(0px) scale(1)\";\r\n                e.target.style.boxShadow =\r\n                  \"0 4px 14px 0 rgba(255, 215, 0, 0.3)\";\r\n              }}\r\n            >\r\n              View Resume\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Resume Modal */}\r\n      <Modal\r\n        open={resumeModalOpen}\r\n        onClose={handleCloseResumeModal}\r\n        sx={{\r\n          display: \"flex\",\r\n          alignItems: \"center\",\r\n          justifyContent: \"center\",\r\n          padding: isMobile ? \"20px\" : \"40px\",\r\n        }}\r\n      >\r\n        <Box\r\n          onClick={handleCloseResumeModal}\r\n          sx={{\r\n            position: \"relative\",\r\n            width: \"100%\",\r\n            height: \"100%\",\r\n            display: \"flex\",\r\n            flexDirection: \"column\",\r\n            alignItems: \"center\",\r\n            justifyContent: \"flex-start\",\r\n            outline: \"none\",\r\n            overflow: \"hidden\",\r\n            cursor: \"pointer\",\r\n          }}\r\n        >\r\n          {/* Scrollable Resume Container */}\r\n          <Box\r\n            sx={{\r\n              width: \"100%\",\r\n              height: \"calc(100vh - 100px)\",\r\n              display: \"flex\",\r\n              flexDirection: \"column\",\r\n              alignItems: \"center\",\r\n              justifyContent: \"flex-start\",\r\n              overflow: \"auto\",\r\n              paddingTop: isMobile ? \"70px\" : \"90px\",\r\n              paddingBottom: \"20px\",\r\n              paddingX: \"20px\",\r\n              \"&::-webkit-scrollbar\": {\r\n                width: \"8px\",\r\n              },\r\n              \"&::-webkit-scrollbar-track\": {\r\n                background: \"rgba(255, 255, 255, 0.1)\",\r\n                borderRadius: \"4px\",\r\n              },\r\n              \"&::-webkit-scrollbar-thumb\": {\r\n                background: \"rgba(255, 215, 0, 0.6)\",\r\n                borderRadius: \"4px\",\r\n                \"&:hover\": {\r\n                  background: \"rgba(255, 215, 0, 0.8)\",\r\n                },\r\n              },\r\n            }}\r\n          >\r\n            {/* Resume Image */}\r\n            <img\r\n              src={resumeImage}\r\n              alt=\"Abhishek DS Resume\"\r\n              style={{\r\n                width: isMobile ? \"100%\" : \"60%\",\r\n                minWidth: isMobile ? \"100%\" : \"60vw\",\r\n                height: \"auto\",\r\n                objectFit: \"contain\",\r\n                boxShadow: \"0 12px 40px rgba(0, 0, 0, 0.4)\",\r\n                borderRadius: \"12px\",\r\n                marginBottom: \"30px\",\r\n              }}\r\n              onLoad={() => console.log(\"Resume image loaded successfully\")}\r\n              onError={(e) => {\r\n                console.log(\"Resume image failed to load\");\r\n                e.target.style.display = \"none\";\r\n              }}\r\n            />\r\n          </Box>\r\n\r\n          {/* Fixed Download Button */}\r\n          <Button\r\n            onClick={handleDownload}\r\n            startIcon={<DownloadIcon />}\r\n            sx={{\r\n              position: \"fixed\",\r\n              bottom: isMobile ? 20 : 30,\r\n              left: \"50%\",\r\n              transform: \"translateX(-50%)\",\r\n              zIndex: 1001,\r\n              fontFamily:\r\n                \"'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif\",\r\n              fontSize: isMobile ? \"0.9rem\" : \"1rem\",\r\n              fontWeight: 600,\r\n              letterSpacing: \"-0.01em\",\r\n              px: isMobile ? 3 : 4,\r\n              py: isMobile ? 1.2 : 1.5,\r\n              bgcolor: \"#FFD700\",\r\n              color: \"#000\",\r\n              borderRadius: \"8px\",\r\n              textTransform: \"none\",\r\n              boxShadow: \"0 4px 16px 0 rgba(255, 215, 0, 0.4)\",\r\n              \"&:hover\": {\r\n                bgcolor: \"#FFC700\",\r\n                transform: \"translateX(-50%) translateY(-2px)\",\r\n                boxShadow: \"0 6px 20px 0 rgba(255, 215, 0, 0.5)\",\r\n              },\r\n              transition: \"all 0.2s ease\",\r\n            }}\r\n          >\r\n            Download Resume\r\n          </Button>\r\n        </Box>\r\n      </Modal>\r\n    </section>\r\n  );\r\n}\r\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,KAAK,EAAEC,GAAG,EAAEC,UAAU,EAAEC,MAAM,QAAQ,eAAe;AAC9D,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,WAAW,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,GAAG,YAAY;;AAEvD;AACA,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,KAAK,CAACC,QAAQ,CAACmB,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;EACxE,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,KAAK,CAACC,QAAQ,CAC5CmB,MAAM,CAACC,UAAU,GAAG,GAAG,IAAID,MAAM,CAACC,UAAU,IAAI,IAClD,CAAC;EAEDrB,KAAK,CAACwB,SAAS,CAAC,MAAM;IACpB,MAAMC,YAAY,GAAGA,CAAA,KAAM;MACzBN,WAAW,CAACC,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;MACrCE,WAAW,CAACH,MAAM,CAACC,UAAU,GAAG,GAAG,IAAID,MAAM,CAACC,UAAU,IAAI,IAAI,CAAC;IACnE,CAAC;IAEDD,MAAM,CAACM,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAML,MAAM,CAACO,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IAAEP,QAAQ;IAAEI;EAAS,CAAC;AAC/B,CAAC;AAACL,EAAA,CAjBID,aAAa;AAmBnB,eAAe,SAASY,IAAIA,CAAA,EAAG;EAAAC,GAAA;EAC7B,MAAM;IAAEX,QAAQ;IAAEI;EAAS,CAAC,GAAGN,aAAa,CAAC,CAAC;EAC9C,MAAM,CAACc,eAAe,EAAEC,kBAAkB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAE7D,MAAM+B,qBAAqB,GAAGA,CAAA,KAAM;IAClCD,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAME,sBAAsB,GAAGA,CAAA,KAAM;IACnCF,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;EAED,MAAMG,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAG,yBAAyB;IACrCH,IAAI,CAACI,QAAQ,GAAG,wBAAwB;IACxCH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;IAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;IACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;EACjC,CAAC;EAED,oBACExB,OAAA;IACEiC,EAAE,EAAC,MAAM,CAAC;IAAA;IACVC,KAAK,EAAE;MACLC,SAAS,EAAE,OAAO;MAClBC,KAAK,EAAE,OAAO;MACdC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,eAAe;MAC/BC,eAAe,EAAE,MAAM;MACvBC,KAAK,EAAE,MAAM;MACbC,UAAU,EAAEnC,QAAQ,GAAG,MAAM,GAAG,MAAM;MACtCoC,OAAO,EAAEpC,QAAQ,GAAG,qBAAqB,GAAG,YAAY;MACxDqC,SAAS,EAAE,YAAY;MACvBC,QAAQ,EAAE,QAAQ;MAClBC,aAAa,EAAEvC,QAAQ,GAAG,QAAQ,GAAG,KAAK;MAC1CwC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,gBAGFhD,OAAA;MACEkC,KAAK,EAAE;QACLe,IAAI,EAAE1C,QAAQ,GAAG,MAAM,GAAG,SAAS;QACnC8B,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE/B,QAAQ,GAAG,QAAQ,GAAG,YAAY;QAC9CgC,cAAc,EAAEhC,QAAQ,GAAG,QAAQ,GAAG,YAAY;QAClD2C,WAAW,EAAE,GAAG;QAChBC,KAAK,EAAE5C,QAAQ,GAAG,CAAC,GAAG,CAAC;QACvB6C,YAAY,EAAE7C,QAAQ,GAAG,MAAM,GAAG;MACpC,CAAE;MAAAyC,QAAA,eAEFhD,OAAA;QACEqD,GAAG,EAAEpD,SAAU;QACfqD,GAAG,EAAC,QAAQ;QACZpB,KAAK,EAAE;UACLqB,MAAM,EAAEhD,QAAQ,GAAG,MAAM,GAAGI,QAAQ,GAAG,MAAM,GAAG,MAAM;UACtDyB,KAAK,EAAE,MAAM;UACboB,QAAQ,EAAEjD,QAAQ,GAAG,KAAK,GAAG,MAAM;UACnCkD,SAAS,EAAE,SAAS;UACpBC,MAAM,EAAE,iDAAiD;UACzDC,YAAY,EAAE,MAAM;UACpBC,UAAU,EAAE,sBAAsB;UAClCC,MAAM,EAAE;QACV;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNjE,OAAA;MACEkC,KAAK,EAAE;QACLe,IAAI,EAAE1C,QAAQ,GAAG,MAAM,GAAG,SAAS;QACnC8B,OAAO,EAAE,MAAM;QACfS,aAAa,EAAE,QAAQ;QACvBP,cAAc,EAAE,QAAQ;QACxBD,UAAU,EAAE/B,QAAQ,GAAG,QAAQ,GAAG,UAAU;QAC5C2D,YAAY,EAAE3D,QAAQ,GAAG,GAAG,GAAG,KAAK;QACpC2C,WAAW,EAAE3C,QAAQ,GAAG,GAAG,GAAG,GAAG;QACjCqC,SAAS,EAAE,YAAY;QACvBO,KAAK,EAAE5C,QAAQ,GAAG,CAAC,GAAG,CAAC;QACvB6B,KAAK,EAAE7B,QAAQ,GAAG,MAAM,GAAG;MAC7B,CAAE;MAAAyC,QAAA,eAEFhD,OAAA;QACEkC,KAAK,EAAE;UACLiC,SAAS,EAAE5D,QAAQ,GAAG,QAAQ,GAAG,OAAO;UACxCiD,QAAQ,EAAE,MAAM;UAChBpB,KAAK,EAAE7B,QAAQ,GAAG,MAAM,GAAG;QAC7B,CAAE;QAAAyC,QAAA,gBAEFhD,OAAA;UACEkC,KAAK,EAAE;YACLkC,QAAQ,EAAE7D,QAAQ,GAAG,QAAQ,GAAGI,QAAQ,GAAG,QAAQ,GAAG,QAAQ;YAC9D0D,UAAU,EAAE,MAAM;YAClBC,UAAU,EAAE,KAAK;YACjBlB,YAAY,EAAE;UAChB,CAAE;UAAAJ,QAAA,gBAEFhD,OAAA;YAAMkC,KAAK,EAAE;cAAEG,OAAO,EAAE;YAAQ,CAAE;YAAAW,QAAA,EAAC;UAAG;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7CjE,OAAA;YAAAgD,QAAA,GAAM,MACA,eAAAhD,OAAA;cAAMkC,KAAK,EAAE;gBAAEO,KAAK,EAAE;cAAU,CAAE;cAAAO,QAAA,EAAC;YAAY;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAELjE,OAAA,CAACT,aAAa;UACZgF,QAAQ,EAAE,CACR,oBAAoB,EACpB,IAAI,EACJ,kBAAkB,EAClB,IAAI,EACJ,gBAAgB,EAChB,IAAI,CACJ;UACFC,KAAK,EAAE,EAAG;UACVC,OAAO,EAAC,MAAM;UACdC,MAAM,EAAEC,QAAS;UACjBzC,KAAK,EAAE;YACLkC,QAAQ,EAAE7D,QAAQ,GAAG,QAAQ,GAAGI,QAAQ,GAAG,QAAQ,GAAG,QAAQ;YAC9D8B,KAAK,EAAE;UACT;QAAE;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGFjE,OAAA;UAAKkC,KAAK,EAAE;YAAE0C,SAAS,EAAE;UAAO,CAAE;UAAA5B,QAAA,eAChChD,OAAA;YACE6E,OAAO,EAAExD,qBAAsB;YAC/Ba,KAAK,EAAE;cACL4C,UAAU,EACR,sFAAsF;cACxFnC,OAAO,EAAEpC,QAAQ,GAAG,WAAW,GAAG,WAAW;cAC7C6D,QAAQ,EAAE7D,QAAQ,GAAG,MAAM,GAAG,QAAQ;cACtC8D,UAAU,EAAE,GAAG;cACfU,aAAa,EAAE,SAAS;cACxBtC,KAAK,EAAE,MAAM;cACbD,eAAe,EAAE,SAAS;cAC1BwC,MAAM,EAAE,mBAAmB;cAC3BrB,YAAY,EAAE,MAAM;cACpBE,MAAM,EAAE,SAAS;cACjBD,UAAU,EAAE,uCAAuC;cACnDqB,cAAc,EAAE,MAAM;cACtB5C,OAAO,EAAE,cAAc;cACvB6C,SAAS,EAAE;YACb,CAAE;YACFC,YAAY,EAAGC,CAAC,IAAK;cACnBA,CAAC,CAACC,MAAM,CAACnD,KAAK,CAACM,eAAe,GAAG,aAAa;cAC9C4C,CAAC,CAACC,MAAM,CAACnD,KAAK,CAACO,KAAK,GAAG,SAAS;cAChC2C,CAAC,CAACC,MAAM,CAACnD,KAAK,CAACoD,SAAS,GAAG,8BAA8B;cACzDF,CAAC,CAACC,MAAM,CAACnD,KAAK,CAACgD,SAAS,GACtB,qCAAqC;YACzC,CAAE;YACFK,YAAY,EAAGH,CAAC,IAAK;cACnBA,CAAC,CAACC,MAAM,CAACnD,KAAK,CAACM,eAAe,GAAG,SAAS;cAC1C4C,CAAC,CAACC,MAAM,CAACnD,KAAK,CAACO,KAAK,GAAG,MAAM;cAC7B2C,CAAC,CAACC,MAAM,CAACnD,KAAK,CAACoD,SAAS,GAAG,0BAA0B;cACrDF,CAAC,CAACC,MAAM,CAACnD,KAAK,CAACgD,SAAS,GACtB,qCAAqC;YACzC,CAAE;YAAAlC,QAAA,EACH;UAED;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNjE,OAAA,CAACR,KAAK;MACJgG,IAAI,EAAErE,eAAgB;MACtBsE,OAAO,EAAEnE,sBAAuB;MAChCoE,EAAE,EAAE;QACFrD,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBI,OAAO,EAAEpC,QAAQ,GAAG,MAAM,GAAG;MAC/B,CAAE;MAAAyC,QAAA,eAEFhD,OAAA,CAACP,GAAG;QACFoF,OAAO,EAAEvD,sBAAuB;QAChCoE,EAAE,EAAE;UACFC,QAAQ,EAAE,UAAU;UACpBvD,KAAK,EAAE,MAAM;UACbmB,MAAM,EAAE,MAAM;UACdlB,OAAO,EAAE,MAAM;UACfS,aAAa,EAAE,QAAQ;UACvBR,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,YAAY;UAC5BqD,OAAO,EAAE,MAAM;UACf/C,QAAQ,EAAE,QAAQ;UAClBgB,MAAM,EAAE;QACV,CAAE;QAAAb,QAAA,gBAGFhD,OAAA,CAACP,GAAG;UACFiG,EAAE,EAAE;YACFtD,KAAK,EAAE,MAAM;YACbmB,MAAM,EAAE,qBAAqB;YAC7BlB,OAAO,EAAE,MAAM;YACfS,aAAa,EAAE,QAAQ;YACvBR,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,YAAY;YAC5BM,QAAQ,EAAE,MAAM;YAChBH,UAAU,EAAEnC,QAAQ,GAAG,MAAM,GAAG,MAAM;YACtCsF,aAAa,EAAE,MAAM;YACrBC,QAAQ,EAAE,MAAM;YAChB,sBAAsB,EAAE;cACtB1D,KAAK,EAAE;YACT,CAAC;YACD,4BAA4B,EAAE;cAC5B2D,UAAU,EAAE,0BAA0B;cACtCpC,YAAY,EAAE;YAChB,CAAC;YACD,4BAA4B,EAAE;cAC5BoC,UAAU,EAAE,wBAAwB;cACpCpC,YAAY,EAAE,KAAK;cACnB,SAAS,EAAE;gBACToC,UAAU,EAAE;cACd;YACF;UACF,CAAE;UAAA/C,QAAA,eAGFhD,OAAA;YACEqD,GAAG,EAAEvD,WAAY;YACjBwD,GAAG,EAAC,oBAAoB;YACxBpB,KAAK,EAAE;cACLE,KAAK,EAAE7B,QAAQ,GAAG,MAAM,GAAG,KAAK;cAChCyF,QAAQ,EAAEzF,QAAQ,GAAG,MAAM,GAAG,MAAM;cACpCgD,MAAM,EAAE,MAAM;cACdE,SAAS,EAAE,SAAS;cACpByB,SAAS,EAAE,gCAAgC;cAC3CvB,YAAY,EAAE,MAAM;cACpBP,YAAY,EAAE;YAChB,CAAE;YACF6C,MAAM,EAAEA,CAAA,KAAMC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAE;YAC9DC,OAAO,EAAGhB,CAAC,IAAK;cACdc,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;cAC1Cf,CAAC,CAACC,MAAM,CAACnD,KAAK,CAACG,OAAO,GAAG,MAAM;YACjC;UAAE;YAAAyB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNjE,OAAA,CAACL,MAAM;UACLkF,OAAO,EAAEtD,cAAe;UACxB8E,SAAS,eAAErG,OAAA,CAACH,YAAY;YAAAiE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC5ByB,EAAE,EAAE;YACFC,QAAQ,EAAE,OAAO;YACjBW,MAAM,EAAE/F,QAAQ,GAAG,EAAE,GAAG,EAAE;YAC1BgG,IAAI,EAAE,KAAK;YACXjB,SAAS,EAAE,kBAAkB;YAC7BkB,MAAM,EAAE,IAAI;YACZ1B,UAAU,EACR,0EAA0E;YAC5EV,QAAQ,EAAE7D,QAAQ,GAAG,QAAQ,GAAG,MAAM;YACtC8D,UAAU,EAAE,GAAG;YACfU,aAAa,EAAE,SAAS;YACxB0B,EAAE,EAAElG,QAAQ,GAAG,CAAC,GAAG,CAAC;YACpBmG,EAAE,EAAEnG,QAAQ,GAAG,GAAG,GAAG,GAAG;YACxBoG,OAAO,EAAE,SAAS;YAClBlE,KAAK,EAAE,MAAM;YACbkB,YAAY,EAAE,KAAK;YACnBiD,aAAa,EAAE,MAAM;YACrB1B,SAAS,EAAE,qCAAqC;YAChD,SAAS,EAAE;cACTyB,OAAO,EAAE,SAAS;cAClBrB,SAAS,EAAE,mCAAmC;cAC9CJ,SAAS,EAAE;YACb,CAAC;YACDtB,UAAU,EAAE;UACd,CAAE;UAAAZ,QAAA,EACH;QAED;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd;AAAC/C,GAAA,CApRuBD,IAAI;EAAA,QACKZ,aAAa;AAAA;AAAAwG,EAAA,GADtB5F,IAAI;AAAA,IAAA4F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}