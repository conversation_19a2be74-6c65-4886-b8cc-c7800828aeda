{"ast": null, "code": "import { number } from '../../../value/types/numbers/index.mjs';\nimport { px } from '../../../value/types/numbers/units.mjs';\nimport { transformPropOrder } from '../../html/utils/keys-transform.mjs';\nimport { parseValueFromTransform } from '../../html/utils/parse-transform.mjs';\nconst isNumOrPxType = v => v === number || v === px;\nconst transformKeys = new Set([\"x\", \"y\", \"z\"]);\nconst nonTranslationalTransformKeys = transformPropOrder.filter(key => !transformKeys.has(key));\nfunction removeNonTranslationalTransform(visualElement) {\n  const removedTransforms = [];\n  nonTranslationalTransformKeys.forEach(key => {\n    const value = visualElement.getValue(key);\n    if (value !== undefined) {\n      removedTransforms.push([key, value.get()]);\n      value.set(key.startsWith(\"scale\") ? 1 : 0);\n    }\n  });\n  return removedTransforms;\n}\nconst positionalValues = {\n  // Dimensions\n  width: ({\n    x\n  }, {\n    paddingLeft = \"0\",\n    paddingRight = \"0\"\n  }) => x.max - x.min - parseFloat(paddingLeft) - parseFloat(paddingRight),\n  height: ({\n    y\n  }, {\n    paddingTop = \"0\",\n    paddingBottom = \"0\"\n  }) => y.max - y.min - parseFloat(paddingTop) - parseFloat(paddingBottom),\n  top: (_bbox, {\n    top\n  }) => parseFloat(top),\n  left: (_bbox, {\n    left\n  }) => parseFloat(left),\n  bottom: ({\n    y\n  }, {\n    top\n  }) => parseFloat(top) + (y.max - y.min),\n  right: ({\n    x\n  }, {\n    left\n  }) => parseFloat(left) + (x.max - x.min),\n  // Transform\n  x: (_bbox, {\n    transform\n  }) => parseValueFromTransform(transform, \"x\"),\n  y: (_bbox, {\n    transform\n  }) => parseValueFromTransform(transform, \"y\")\n};\n// Alias translate longform names\npositionalValues.translateX = positionalValues.x;\npositionalValues.translateY = positionalValues.y;\nexport { isNumOrPxType, positionalValues, removeNonTranslationalTransform };", "map": {"version": 3, "names": ["number", "px", "transformPropOrder", "parseValueFromTransform", "isNumOrPxType", "v", "transformKeys", "Set", "nonTranslationalTransformKeys", "filter", "key", "has", "removeNonTranslationalTransform", "visualElement", "removedTransforms", "for<PERSON>ach", "value", "getValue", "undefined", "push", "get", "set", "startsWith", "positionalV<PERSON>ues", "width", "x", "paddingLeft", "paddingRight", "max", "min", "parseFloat", "height", "y", "paddingTop", "paddingBottom", "top", "_bbox", "left", "bottom", "right", "transform", "translateX", "translateY"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/framer-motion/dist/es/render/dom/utils/unit-conversion.mjs"], "sourcesContent": ["import { number } from '../../../value/types/numbers/index.mjs';\nimport { px } from '../../../value/types/numbers/units.mjs';\nimport { transformPropOrder } from '../../html/utils/keys-transform.mjs';\nimport { parseValueFromTransform } from '../../html/utils/parse-transform.mjs';\n\nconst isNumOrPxType = (v) => v === number || v === px;\nconst transformKeys = new Set([\"x\", \"y\", \"z\"]);\nconst nonTranslationalTransformKeys = transformPropOrder.filter((key) => !transformKeys.has(key));\nfunction removeNonTranslationalTransform(visualElement) {\n    const removedTransforms = [];\n    nonTranslationalTransformKeys.forEach((key) => {\n        const value = visualElement.getValue(key);\n        if (value !== undefined) {\n            removedTransforms.push([key, value.get()]);\n            value.set(key.startsWith(\"scale\") ? 1 : 0);\n        }\n    });\n    return removedTransforms;\n}\nconst positionalValues = {\n    // Dimensions\n    width: ({ x }, { paddingLeft = \"0\", paddingRight = \"0\" }) => x.max - x.min - parseFloat(paddingLeft) - parseFloat(paddingRight),\n    height: ({ y }, { paddingTop = \"0\", paddingBottom = \"0\" }) => y.max - y.min - parseFloat(paddingTop) - parseFloat(paddingBottom),\n    top: (_bbox, { top }) => parseFloat(top),\n    left: (_bbox, { left }) => parseFloat(left),\n    bottom: ({ y }, { top }) => parseFloat(top) + (y.max - y.min),\n    right: ({ x }, { left }) => parseFloat(left) + (x.max - x.min),\n    // Transform\n    x: (_bbox, { transform }) => parseValueFromTransform(transform, \"x\"),\n    y: (_bbox, { transform }) => parseValueFromTransform(transform, \"y\"),\n};\n// Alias translate longform names\npositionalValues.translateX = positionalValues.x;\npositionalValues.translateY = positionalValues.y;\n\nexport { isNumOrPxType, positionalValues, removeNonTranslationalTransform };\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,wCAAwC;AAC/D,SAASC,EAAE,QAAQ,wCAAwC;AAC3D,SAASC,kBAAkB,QAAQ,qCAAqC;AACxE,SAASC,uBAAuB,QAAQ,sCAAsC;AAE9E,MAAMC,aAAa,GAAIC,CAAC,IAAKA,CAAC,KAAKL,MAAM,IAAIK,CAAC,KAAKJ,EAAE;AACrD,MAAMK,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AAC9C,MAAMC,6BAA6B,GAAGN,kBAAkB,CAACO,MAAM,CAAEC,GAAG,IAAK,CAACJ,aAAa,CAACK,GAAG,CAACD,GAAG,CAAC,CAAC;AACjG,SAASE,+BAA+BA,CAACC,aAAa,EAAE;EACpD,MAAMC,iBAAiB,GAAG,EAAE;EAC5BN,6BAA6B,CAACO,OAAO,CAAEL,GAAG,IAAK;IAC3C,MAAMM,KAAK,GAAGH,aAAa,CAACI,QAAQ,CAACP,GAAG,CAAC;IACzC,IAAIM,KAAK,KAAKE,SAAS,EAAE;MACrBJ,iBAAiB,CAACK,IAAI,CAAC,CAACT,GAAG,EAAEM,KAAK,CAACI,GAAG,CAAC,CAAC,CAAC,CAAC;MAC1CJ,KAAK,CAACK,GAAG,CAACX,GAAG,CAACY,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC9C;EACJ,CAAC,CAAC;EACF,OAAOR,iBAAiB;AAC5B;AACA,MAAMS,gBAAgB,GAAG;EACrB;EACAC,KAAK,EAAEA,CAAC;IAAEC;EAAE,CAAC,EAAE;IAAEC,WAAW,GAAG,GAAG;IAAEC,YAAY,GAAG;EAAI,CAAC,KAAKF,CAAC,CAACG,GAAG,GAAGH,CAAC,CAACI,GAAG,GAAGC,UAAU,CAACJ,WAAW,CAAC,GAAGI,UAAU,CAACH,YAAY,CAAC;EAC/HI,MAAM,EAAEA,CAAC;IAAEC;EAAE,CAAC,EAAE;IAAEC,UAAU,GAAG,GAAG;IAAEC,aAAa,GAAG;EAAI,CAAC,KAAKF,CAAC,CAACJ,GAAG,GAAGI,CAAC,CAACH,GAAG,GAAGC,UAAU,CAACG,UAAU,CAAC,GAAGH,UAAU,CAACI,aAAa,CAAC;EAChIC,GAAG,EAAEA,CAACC,KAAK,EAAE;IAAED;EAAI,CAAC,KAAKL,UAAU,CAACK,GAAG,CAAC;EACxCE,IAAI,EAAEA,CAACD,KAAK,EAAE;IAAEC;EAAK,CAAC,KAAKP,UAAU,CAACO,IAAI,CAAC;EAC3CC,MAAM,EAAEA,CAAC;IAAEN;EAAE,CAAC,EAAE;IAAEG;EAAI,CAAC,KAAKL,UAAU,CAACK,GAAG,CAAC,IAAIH,CAAC,CAACJ,GAAG,GAAGI,CAAC,CAACH,GAAG,CAAC;EAC7DU,KAAK,EAAEA,CAAC;IAAEd;EAAE,CAAC,EAAE;IAAEY;EAAK,CAAC,KAAKP,UAAU,CAACO,IAAI,CAAC,IAAIZ,CAAC,CAACG,GAAG,GAAGH,CAAC,CAACI,GAAG,CAAC;EAC9D;EACAJ,CAAC,EAAEA,CAACW,KAAK,EAAE;IAAEI;EAAU,CAAC,KAAKrC,uBAAuB,CAACqC,SAAS,EAAE,GAAG,CAAC;EACpER,CAAC,EAAEA,CAACI,KAAK,EAAE;IAAEI;EAAU,CAAC,KAAKrC,uBAAuB,CAACqC,SAAS,EAAE,GAAG;AACvE,CAAC;AACD;AACAjB,gBAAgB,CAACkB,UAAU,GAAGlB,gBAAgB,CAACE,CAAC;AAChDF,gBAAgB,CAACmB,UAAU,GAAGnB,gBAAgB,CAACS,CAAC;AAEhD,SAAS5B,aAAa,EAAEmB,gBAAgB,EAAEX,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}