{"ast": null, "code": "export { default } from \"./composeClasses.js\";", "map": {"version": 3, "names": ["default"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/@mui/utils/esm/composeClasses/index.js"], "sourcesContent": ["export { default } from \"./composeClasses.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}