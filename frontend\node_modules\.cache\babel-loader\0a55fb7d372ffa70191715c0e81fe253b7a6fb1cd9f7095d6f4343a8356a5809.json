{"ast": null, "code": "import { millisecondsToSeconds } from 'motion-utils';\nimport { calcGeneratorDuration, maxGeneratorDuration } from './calc-duration.mjs';\n\n/**\n * Create a progress => progress easing function from a generator.\n */\nfunction createGeneratorEasing(options, scale = 100, createGenerator) {\n  const generator = createGenerator({\n    ...options,\n    keyframes: [0, scale]\n  });\n  const duration = Math.min(calcGeneratorDuration(generator), maxGeneratorDuration);\n  return {\n    type: \"keyframes\",\n    ease: progress => {\n      return generator.next(duration * progress).value / scale;\n    },\n    duration: millisecondsToSeconds(duration)\n  };\n}\nexport { createGeneratorEasing };", "map": {"version": 3, "names": ["millisecondsToSeconds", "calcGeneratorDuration", "maxGeneratorDuration", "createGeneratorEasing", "options", "scale", "createGenerator", "generator", "keyframes", "duration", "Math", "min", "type", "ease", "progress", "next", "value"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/motion-dom/dist/es/animation/generators/utils/create-generator-easing.mjs"], "sourcesContent": ["import { millisecondsToSeconds } from 'motion-utils';\nimport { calcGeneratorDuration, maxGeneratorDuration } from './calc-duration.mjs';\n\n/**\n * Create a progress => progress easing function from a generator.\n */\nfunction createGeneratorEasing(options, scale = 100, createGenerator) {\n    const generator = createGenerator({ ...options, keyframes: [0, scale] });\n    const duration = Math.min(calcGeneratorDuration(generator), maxGeneratorDuration);\n    return {\n        type: \"keyframes\",\n        ease: (progress) => {\n            return generator.next(duration * progress).value / scale;\n        },\n        duration: millisecondsToSeconds(duration),\n    };\n}\n\nexport { createGeneratorEasing };\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,cAAc;AACpD,SAASC,qBAAqB,EAAEC,oBAAoB,QAAQ,qBAAqB;;AAEjF;AACA;AACA;AACA,SAASC,qBAAqBA,CAACC,OAAO,EAAEC,KAAK,GAAG,GAAG,EAAEC,eAAe,EAAE;EAClE,MAAMC,SAAS,GAAGD,eAAe,CAAC;IAAE,GAAGF,OAAO;IAAEI,SAAS,EAAE,CAAC,CAAC,EAAEH,KAAK;EAAE,CAAC,CAAC;EACxE,MAAMI,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACV,qBAAqB,CAACM,SAAS,CAAC,EAAEL,oBAAoB,CAAC;EACjF,OAAO;IACHU,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAGC,QAAQ,IAAK;MAChB,OAAOP,SAAS,CAACQ,IAAI,CAACN,QAAQ,GAAGK,QAAQ,CAAC,CAACE,KAAK,GAAGX,KAAK;IAC5D,CAAC;IACDI,QAAQ,EAAET,qBAAqB,CAACS,QAAQ;EAC5C,CAAC;AACL;AAEA,SAASN,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}