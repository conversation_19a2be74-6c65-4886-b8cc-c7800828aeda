{"ast": null, "code": "'use client';\n\nimport createTheme from \"../createTheme/index.js\";\nimport useThemeWithoutDefault from \"../useThemeWithoutDefault/index.js\";\nexport const systemDefaultTheme = createTheme();\nfunction useTheme(defaultTheme = systemDefaultTheme) {\n  return useThemeWithoutDefault(defaultTheme);\n}\nexport default useTheme;", "map": {"version": 3, "names": ["createTheme", "useThemeWithoutDefault", "systemDefaultTheme", "useTheme", "defaultTheme"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/@mui/system/esm/useTheme/useTheme.js"], "sourcesContent": ["'use client';\n\nimport createTheme from \"../createTheme/index.js\";\nimport useThemeWithoutDefault from \"../useThemeWithoutDefault/index.js\";\nexport const systemDefaultTheme = createTheme();\nfunction useTheme(defaultTheme = systemDefaultTheme) {\n  return useThemeWithoutDefault(defaultTheme);\n}\nexport default useTheme;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,WAAW,MAAM,yBAAyB;AACjD,OAAOC,sBAAsB,MAAM,oCAAoC;AACvE,OAAO,MAAMC,kBAAkB,GAAGF,WAAW,CAAC,CAAC;AAC/C,SAASG,QAAQA,CAACC,YAAY,GAAGF,kBAAkB,EAAE;EACnD,OAAOD,sBAAsB,CAACG,YAAY,CAAC;AAC7C;AACA,eAAeD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}