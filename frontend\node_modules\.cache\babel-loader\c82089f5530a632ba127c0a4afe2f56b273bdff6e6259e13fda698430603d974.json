{"ast": null, "code": "import createStyled from \"../createStyled/index.js\";\nconst styled = createStyled();\nexport default styled;", "map": {"version": 3, "names": ["createStyled", "styled"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/@mui/system/esm/styled/styled.js"], "sourcesContent": ["import createStyled from \"../createStyled/index.js\";\nconst styled = createStyled();\nexport default styled;"], "mappings": "AAAA,OAAOA,YAAY,MAAM,0BAA0B;AACnD,MAAMC,MAAM,GAAGD,YAAY,CAAC,CAAC;AAC7B,eAAeC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}