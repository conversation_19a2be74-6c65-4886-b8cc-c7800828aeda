{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getStepUtilityClass(slot) {\n  return generateUtilityClass('MuiStep', slot);\n}\nconst stepClasses = generateUtilityClasses('MuiStep', ['root', 'horizontal', 'vertical', 'alternativeLabel', 'completed']);\nexport default stepClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getStepUtilityClass", "slot", "stepClasses"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/@mui/material/esm/Step/stepClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getStepUtilityClass(slot) {\n  return generateUtilityClass('MuiStep', slot);\n}\nconst stepClasses = generateUtilityClasses('MuiStep', ['root', 'horizontal', 'vertical', 'alternativeLabel', 'completed']);\nexport default stepClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,mBAAmBA,CAACC,IAAI,EAAE;EACxC,OAAOF,oBAAoB,CAAC,SAAS,EAAEE,IAAI,CAAC;AAC9C;AACA,MAAMC,WAAW,GAAGJ,sBAAsB,CAAC,SAAS,EAAE,CAAC,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,kBAAkB,EAAE,WAAW,CAAC,CAAC;AAC1H,eAAeI,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}