{"ast": null, "code": "import * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { exactProp } from '@mui/utils';\nimport ThemeContext from \"../useTheme/ThemeContext.js\";\nimport useTheme from \"../useTheme/index.js\";\nimport nested from \"./nested.js\";\n\n// To support composition of theme.\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction mergeOuterLocalTheme(outerTheme, localTheme) {\n  if (typeof localTheme === 'function') {\n    const mergedTheme = localTheme(outerTheme);\n    if (process.env.NODE_ENV !== 'production') {\n      if (!mergedTheme) {\n        console.error(['MUI: You should return an object from your theme function, i.e.', '<ThemeProvider theme={() => ({})} />'].join('\\n'));\n      }\n    }\n    return mergedTheme;\n  }\n  return {\n    ...outerTheme,\n    ...localTheme\n  };\n}\n\n/**\n * This component takes a `theme` prop.\n * It makes the `theme` available down the React tree thanks to React context.\n * This component should preferably be used at **the root of your component tree**.\n */\nfunction ThemeProvider(props) {\n  const {\n    children,\n    theme: localTheme\n  } = props;\n  const outerTheme = useTheme();\n  if (process.env.NODE_ENV !== 'production') {\n    if (outerTheme === null && typeof localTheme === 'function') {\n      console.error(['MUI: You are providing a theme function prop to the ThemeProvider component:', '<ThemeProvider theme={outerTheme => outerTheme} />', '', 'However, no outer theme is present.', 'Make sure a theme is already injected higher in the React tree ' + 'or provide a theme object.'].join('\\n'));\n    }\n  }\n  const theme = React.useMemo(() => {\n    const output = outerTheme === null ? {\n      ...localTheme\n    } : mergeOuterLocalTheme(outerTheme, localTheme);\n    if (output != null) {\n      output[nested] = outerTheme !== null;\n    }\n    return output;\n  }, [localTheme, outerTheme]);\n  return /*#__PURE__*/_jsx(ThemeContext.Provider, {\n    value: theme,\n    children: children\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? ThemeProvider.propTypes = {\n  /**\n   * Your component tree.\n   */\n  children: PropTypes.node,\n  /**\n   * A theme object. You can provide a function to extend the outer theme.\n   */\n  theme: PropTypes.oneOfType([PropTypes.object, PropTypes.func]).isRequired\n} : void 0;\nif (process.env.NODE_ENV !== 'production') {\n  process.env.NODE_ENV !== \"production\" ? ThemeProvider.propTypes = exactProp(ThemeProvider.propTypes) : void 0;\n}\nexport default ThemeProvider;", "map": {"version": 3, "names": ["React", "PropTypes", "exactProp", "ThemeContext", "useTheme", "nested", "jsx", "_jsx", "mergeOuterLocalTheme", "outerTheme", "localTheme", "mergedTheme", "process", "env", "NODE_ENV", "console", "error", "join", "ThemeProvider", "props", "children", "theme", "useMemo", "output", "Provider", "value", "propTypes", "node", "oneOfType", "object", "func", "isRequired"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/@mui/private-theming/esm/ThemeProvider/ThemeProvider.js"], "sourcesContent": ["import * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { exactProp } from '@mui/utils';\nimport ThemeContext from \"../useTheme/ThemeContext.js\";\nimport useTheme from \"../useTheme/index.js\";\nimport nested from \"./nested.js\";\n\n// To support composition of theme.\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction mergeOuterLocalTheme(outerTheme, localTheme) {\n  if (typeof localTheme === 'function') {\n    const mergedTheme = localTheme(outerTheme);\n    if (process.env.NODE_ENV !== 'production') {\n      if (!mergedTheme) {\n        console.error(['MUI: You should return an object from your theme function, i.e.', '<ThemeProvider theme={() => ({})} />'].join('\\n'));\n      }\n    }\n    return mergedTheme;\n  }\n  return {\n    ...outerTheme,\n    ...localTheme\n  };\n}\n\n/**\n * This component takes a `theme` prop.\n * It makes the `theme` available down the React tree thanks to React context.\n * This component should preferably be used at **the root of your component tree**.\n */\nfunction ThemeProvider(props) {\n  const {\n    children,\n    theme: localTheme\n  } = props;\n  const outerTheme = useTheme();\n  if (process.env.NODE_ENV !== 'production') {\n    if (outerTheme === null && typeof localTheme === 'function') {\n      console.error(['MUI: You are providing a theme function prop to the ThemeProvider component:', '<ThemeProvider theme={outerTheme => outerTheme} />', '', 'However, no outer theme is present.', 'Make sure a theme is already injected higher in the React tree ' + 'or provide a theme object.'].join('\\n'));\n    }\n  }\n  const theme = React.useMemo(() => {\n    const output = outerTheme === null ? {\n      ...localTheme\n    } : mergeOuterLocalTheme(outerTheme, localTheme);\n    if (output != null) {\n      output[nested] = outerTheme !== null;\n    }\n    return output;\n  }, [localTheme, outerTheme]);\n  return /*#__PURE__*/_jsx(ThemeContext.Provider, {\n    value: theme,\n    children: children\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? ThemeProvider.propTypes = {\n  /**\n   * Your component tree.\n   */\n  children: PropTypes.node,\n  /**\n   * A theme object. You can provide a function to extend the outer theme.\n   */\n  theme: PropTypes.oneOfType([PropTypes.object, PropTypes.func]).isRequired\n} : void 0;\nif (process.env.NODE_ENV !== 'production') {\n  process.env.NODE_ENV !== \"production\" ? ThemeProvider.propTypes = exactProp(ThemeProvider.propTypes) : void 0;\n}\nexport default ThemeProvider;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,SAAS,QAAQ,YAAY;AACtC,OAAOC,YAAY,MAAM,6BAA6B;AACtD,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,MAAM,MAAM,aAAa;;AAEhC;AACA,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,oBAAoBA,CAACC,UAAU,EAAEC,UAAU,EAAE;EACpD,IAAI,OAAOA,UAAU,KAAK,UAAU,EAAE;IACpC,MAAMC,WAAW,GAAGD,UAAU,CAACD,UAAU,CAAC;IAC1C,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI,CAACH,WAAW,EAAE;QAChBI,OAAO,CAACC,KAAK,CAAC,CAAC,iEAAiE,EAAE,sCAAsC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MACvI;IACF;IACA,OAAON,WAAW;EACpB;EACA,OAAO;IACL,GAAGF,UAAU;IACb,GAAGC;EACL,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASQ,aAAaA,CAACC,KAAK,EAAE;EAC5B,MAAM;IACJC,QAAQ;IACRC,KAAK,EAAEX;EACT,CAAC,GAAGS,KAAK;EACT,MAAMV,UAAU,GAAGL,QAAQ,CAAC,CAAC;EAC7B,IAAIQ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIL,UAAU,KAAK,IAAI,IAAI,OAAOC,UAAU,KAAK,UAAU,EAAE;MAC3DK,OAAO,CAACC,KAAK,CAAC,CAAC,8EAA8E,EAAE,oDAAoD,EAAE,EAAE,EAAE,qCAAqC,EAAE,iEAAiE,GAAG,4BAA4B,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/S;EACF;EACA,MAAMI,KAAK,GAAGrB,KAAK,CAACsB,OAAO,CAAC,MAAM;IAChC,MAAMC,MAAM,GAAGd,UAAU,KAAK,IAAI,GAAG;MACnC,GAAGC;IACL,CAAC,GAAGF,oBAAoB,CAACC,UAAU,EAAEC,UAAU,CAAC;IAChD,IAAIa,MAAM,IAAI,IAAI,EAAE;MAClBA,MAAM,CAAClB,MAAM,CAAC,GAAGI,UAAU,KAAK,IAAI;IACtC;IACA,OAAOc,MAAM;EACf,CAAC,EAAE,CAACb,UAAU,EAAED,UAAU,CAAC,CAAC;EAC5B,OAAO,aAAaF,IAAI,CAACJ,YAAY,CAACqB,QAAQ,EAAE;IAC9CC,KAAK,EAAEJ,KAAK;IACZD,QAAQ,EAAEA;EACZ,CAAC,CAAC;AACJ;AACAR,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGI,aAAa,CAACQ,SAAS,GAAG;EAChE;AACF;AACA;EACEN,QAAQ,EAAEnB,SAAS,CAAC0B,IAAI;EACxB;AACF;AACA;EACEN,KAAK,EAAEpB,SAAS,CAAC2B,SAAS,CAAC,CAAC3B,SAAS,CAAC4B,MAAM,EAAE5B,SAAS,CAAC6B,IAAI,CAAC,CAAC,CAACC;AACjE,CAAC,GAAG,KAAK,CAAC;AACV,IAAInB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGI,aAAa,CAACQ,SAAS,GAAGxB,SAAS,CAACgB,aAAa,CAACQ,SAAS,CAAC,GAAG,KAAK,CAAC;AAC/G;AACA,eAAeR,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}