{"ast": null, "code": "const windowCallbacks = new Set();\nlet windowResizeHandler;\nfunction createWindowResizeHandler() {\n  windowResizeHandler = () => {\n    const size = {\n      width: window.innerWidth,\n      height: window.innerHeight\n    };\n    const info = {\n      target: window,\n      size,\n      contentSize: size\n    };\n    windowCallbacks.forEach(callback => callback(info));\n  };\n  window.addEventListener(\"resize\", windowResizeHandler);\n}\nfunction resizeWindow(callback) {\n  windowCallbacks.add(callback);\n  if (!windowResizeHandler) createWindowResizeHandler();\n  return () => {\n    windowCallbacks.delete(callback);\n    if (!windowCallbacks.size && windowResizeHandler) {\n      windowResizeHandler = undefined;\n    }\n  };\n}\nexport { resizeWindow };", "map": {"version": 3, "names": ["windowCallbacks", "Set", "windowResizeHandler", "createWindowResizeHandler", "size", "width", "window", "innerWidth", "height", "innerHeight", "info", "target", "contentSize", "for<PERSON>ach", "callback", "addEventListener", "resizeWindow", "add", "delete", "undefined"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/framer-motion/dist/es/render/dom/resize/handle-window.mjs"], "sourcesContent": ["const windowCallbacks = new Set();\nlet windowResizeHandler;\nfunction createWindowResizeHandler() {\n    windowResizeHandler = () => {\n        const size = {\n            width: window.innerWidth,\n            height: window.innerHeight,\n        };\n        const info = {\n            target: window,\n            size,\n            contentSize: size,\n        };\n        windowCallbacks.forEach((callback) => callback(info));\n    };\n    window.addEventListener(\"resize\", windowResizeHandler);\n}\nfunction resizeWindow(callback) {\n    windowCallbacks.add(callback);\n    if (!windowResizeHandler)\n        createWindowResizeHandler();\n    return () => {\n        windowCallbacks.delete(callback);\n        if (!windowCallbacks.size && windowResizeHandler) {\n            windowResizeHandler = undefined;\n        }\n    };\n}\n\nexport { resizeWindow };\n"], "mappings": "AAAA,MAAMA,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;AACjC,IAAIC,mBAAmB;AACvB,SAASC,yBAAyBA,CAAA,EAAG;EACjCD,mBAAmB,GAAGA,CAAA,KAAM;IACxB,MAAME,IAAI,GAAG;MACTC,KAAK,EAAEC,MAAM,CAACC,UAAU;MACxBC,MAAM,EAAEF,MAAM,CAACG;IACnB,CAAC;IACD,MAAMC,IAAI,GAAG;MACTC,MAAM,EAAEL,MAAM;MACdF,IAAI;MACJQ,WAAW,EAAER;IACjB,CAAC;IACDJ,eAAe,CAACa,OAAO,CAAEC,QAAQ,IAAKA,QAAQ,CAACJ,IAAI,CAAC,CAAC;EACzD,CAAC;EACDJ,MAAM,CAACS,gBAAgB,CAAC,QAAQ,EAAEb,mBAAmB,CAAC;AAC1D;AACA,SAASc,YAAYA,CAACF,QAAQ,EAAE;EAC5Bd,eAAe,CAACiB,GAAG,CAACH,QAAQ,CAAC;EAC7B,IAAI,CAACZ,mBAAmB,EACpBC,yBAAyB,CAAC,CAAC;EAC/B,OAAO,MAAM;IACTH,eAAe,CAACkB,MAAM,CAACJ,QAAQ,CAAC;IAChC,IAAI,CAACd,eAAe,CAACI,IAAI,IAAIF,mBAAmB,EAAE;MAC9CA,mBAAmB,GAAGiB,SAAS;IACnC;EACJ,CAAC;AACL;AAEA,SAASH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}