import React, { useState } from "react";
import { TypeAnimation } from "react-type-animation";
import { Modal, Box, IconButton, Button } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import DownloadIcon from "@mui/icons-material/Download";

const avatarUrl = process.env.PUBLIC_URL + "/logo2.png";

export default function Hero() {
  const [resumeModalOpen, setResumeModalOpen] = useState(false);

  const handleOpenResumeModal = () => {
    setResumeModalOpen(true);
  };

  const handleCloseResumeModal = () => {
    setResumeModalOpen(false);
  };

  const handleDownload = () => {
    const link = document.createElement("a");
    link.href = "/resume.jpg";
    link.download = "Abhishek_DS_Resume.jpg";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <section
      id="hero" // 🔥 This makes your logo link scroll to here
      style={{
        minHeight: "100vh",
        width: "100vw",
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        backgroundColor: "#000",
        color: "#fff",
        paddingTop: "80px", // adjust to navbar height
        boxSizing: "border-box",
        overflow: "hidden",
        flexWrap: "wrap",
      }}
    >
      {/* Avatar Section */}
      <div
        style={{
          flex: "1 1 50%",
          display: "flex",
          alignItems: "flex-start",
          justifyContent: "flex-start",
          paddingLeft: "0",
        }}
      >
        <img
          src={avatarUrl}
          alt="Avatar"
          style={{
            height: "90vh",
            width: "auto",
            objectFit: "contain",
            filter: "grayscale(100%) drop-shadow(0 0 35px #e7e6e254)",
            borderRadius: "20px",
            transition: "all 0.3s ease-in-out",
            cursor: "default",
          }}
        />
      </div>

      {/* Text Section */}
      <div
        style={{
          flex: "1 1 50%",
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          alignItems: "flex-end",
          paddingRight: "5vw", // aligns text cleanly to the right
          boxSizing: "border-box",
        }}
      >
        <div style={{ textAlign: "right", maxWidth: "100%" }}>
          <h1
            style={{
              fontSize: "3.2rem",
              fontWeight: "bold",
              lineHeight: "1.2",
              marginBottom: "1rem",
            }}
          >
            <span style={{ display: "block" }}>Hi,</span>
            <span>
              I'm <span style={{ color: "#FFD700" }}>Abhishek D S</span>
            </span>
          </h1>

          <TypeAnimation
            sequence={[
              "Frontend Developer",
              2000,
              "React Enthusiast",
              2000,
              "UI/UX Explorer",
              2000,
            ]}
            speed={50}
            wrapper="span"
            repeat={Infinity}
            style={{
              fontSize: "1.6rem",
              color: "#fff",
            }}
          />

          {/* View Resume Button */}
          <div style={{ marginTop: "2rem" }}>
            <button
              onClick={handleOpenResumeModal}
              style={{
                fontFamily:
                  "'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",
                padding: "14px 28px",
                fontSize: "1.1rem",
                fontWeight: 600,
                letterSpacing: "-0.01em",
                color: "#000",
                backgroundColor: "#FFD700",
                border: "2px solid #FFD700",
                borderRadius: "12px",
                cursor: "pointer",
                transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                textDecoration: "none",
                display: "inline-block",
                boxShadow: "0 4px 14px 0 rgba(255, 215, 0, 0.3)",
              }}
              onMouseEnter={(e) => {
                e.target.style.backgroundColor = "transparent";
                e.target.style.color = "#FFD700";
                e.target.style.transform = "translateY(-2px) scale(1.02)";
                e.target.style.boxShadow =
                  "0 8px 25px 0 rgba(255, 215, 0, 0.4)";
              }}
              onMouseLeave={(e) => {
                e.target.style.backgroundColor = "#FFD700";
                e.target.style.color = "#000";
                e.target.style.transform = "translateY(0px) scale(1)";
                e.target.style.boxShadow =
                  "0 4px 14px 0 rgba(255, 215, 0, 0.3)";
              }}
            >
              View Resume
            </button>
          </div>
        </div>
      </div>

      {/* Resume Modal */}
      <Modal
        open={resumeModalOpen}
        onClose={handleCloseResumeModal}
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Box
          sx={{
            position: "relative",
            width: "95vw",
            height: "95vh",
            maxWidth: "1000px",
            maxHeight: "900px",
            bgcolor: "#fff",
            borderRadius: "12px",
            boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.8)",
            overflow: "hidden",
            display: "flex",
            flexDirection: "column",
          }}
        >
          {/* Close Button */}
          <IconButton
            onClick={handleCloseResumeModal}
            sx={{
              position: "absolute",
              top: 8,
              left: 8,
              zIndex: 1000,
              color: "#000",
              bgcolor: "rgba(255, 255, 255, 0.9)",
              "&:hover": {
                bgcolor: "rgba(255, 255, 255, 1)",
                transform: "scale(1.1)",
              },
              transition: "all 0.2s ease",
            }}
          >
            <CloseIcon />
          </IconButton>

          {/* Resume Image Viewer */}
          <div
            style={{
              width: "100%",
              height: "calc(100% - 80px)",
              backgroundColor: "#fff",
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              overflow: "auto",
              padding: "10px",
            }}
          >
            <img
              src="/resume.jpg"
              alt="Abhishek DS Resume"
              style={{
                maxWidth: "100%",
                height: "auto",
                boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
                borderRadius: "4px",
              }}
              onLoad={() => console.log("Resume image loaded successfully")}
              onError={(e) => {
                console.log("Image failed to load:", e);
                e.target.style.display = "none";
                // Show fallback message
                const fallback = document.createElement("div");
                fallback.innerHTML = `
                  <p style="text-align: center; color: #666; font-size: 16px;">
                    Resume image could not be loaded.<br/>
                    <a href="/resume.jpg" target="_blank" style="color: #f1c40f; text-decoration: underline;">
                      Click here to view the resume
                    </a>
                  </p>
                `;
                e.target.parentNode.appendChild(fallback);
              }}
            />
          </div>

          {/* Download Button */}
          <Box
            sx={{
              height: "80px",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              bgcolor: "#f5f5f5",
              borderTop: "1px solid #e0e0e0",
            }}
          >
            <Button
              onClick={handleDownload}
              startIcon={<DownloadIcon />}
              sx={{
                fontFamily:
                  "'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif",
                fontSize: "1rem",
                fontWeight: 600,
                letterSpacing: "-0.01em",
                px: 4,
                py: 1.5,
                bgcolor: "#FFD700",
                color: "#000",
                borderRadius: "8px",
                textTransform: "none",
                boxShadow: "0 2px 8px 0 rgba(255, 215, 0, 0.3)",
                "&:hover": {
                  bgcolor: "#FFC700",
                  transform: "translateY(-1px)",
                  boxShadow: "0 4px 12px 0 rgba(255, 215, 0, 0.4)",
                },
                transition: "all 0.2s ease",
              }}
            >
              Download Resume
            </Button>
          </Box>
        </Box>
      </Modal>
    </section>
  );
}
