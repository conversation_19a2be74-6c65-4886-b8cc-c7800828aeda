{"ast": null, "code": "/**\n * Converts seconds to milliseconds\n *\n * @param seconds - Time in seconds.\n * @return milliseconds - Converted time in milliseconds.\n */\n/*#__NO_SIDE_EFFECTS__*/\nconst secondsToMilliseconds = seconds => seconds * 1000;\n/*#__NO_SIDE_EFFECTS__*/\nconst millisecondsToSeconds = milliseconds => milliseconds / 1000;\nexport { millisecondsToSeconds, secondsToMilliseconds };", "map": {"version": 3, "names": ["secondsToMilliseconds", "seconds", "millisecondsToSeconds", "milliseconds"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/motion-utils/dist/es/time-conversion.mjs"], "sourcesContent": ["/**\n * Converts seconds to milliseconds\n *\n * @param seconds - Time in seconds.\n * @return milliseconds - Converted time in milliseconds.\n */\n/*#__NO_SIDE_EFFECTS__*/\nconst secondsToMilliseconds = (seconds) => seconds * 1000;\n/*#__NO_SIDE_EFFECTS__*/\nconst millisecondsToSeconds = (milliseconds) => milliseconds / 1000;\n\nexport { millisecondsToSeconds, secondsToMilliseconds };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,qBAAqB,GAAIC,OAAO,IAAKA,OAAO,GAAG,IAAI;AACzD;AACA,MAAMC,qBAAqB,GAAIC,YAAY,IAAKA,YAAY,GAAG,IAAI;AAEnE,SAASD,qBAAqB,EAAEF,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}