{"homepage": "https://abhi120508.github.io/portfolio", "name": "port", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.0.1", "@mui/material": "^7.0.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "canvas-confetti": "^1.9.3", "framer-motion": "^12.6.5", "lottie-react": "^2.4.1", "lucide-react": "^0.487.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-multi-carousel": "^2.8.6", "react-scripts": "5.0.1", "react-slick": "^0.30.3", "react-swipeable": "^7.0.2", "react-swipeable-views": "^0.14.0", "react-swipeable-views-utils": "^0.14.0", "react-tsparticles": "^2.12.2", "react-type-animation": "^3.2.0", "slick-carousel": "^1.8.1", "styled-components": "^6.1.17", "tsparticles": "^3.8.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "predeploy": "npm run build", "deploy": "gh-pages -d build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"gh-pages": "^6.3.0"}}