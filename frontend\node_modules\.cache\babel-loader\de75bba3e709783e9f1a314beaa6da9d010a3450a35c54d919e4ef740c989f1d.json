{"ast": null, "code": "const isCSSVar = name => name.startsWith(\"--\");\nconst style = {\n  set: (element, name, value) => {\n    isCSSVar(name) ? element.style.setProperty(name, value) : element.style[name] = value;\n  },\n  get: (element, name) => {\n    return isCSSVar(name) ? element.style.getPropertyValue(name) : element.style[name];\n  }\n};\nexport { style };", "map": {"version": 3, "names": ["isCSSVar", "name", "startsWith", "style", "set", "element", "value", "setProperty", "get", "getPropertyValue"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/motion-dom/dist/es/render/dom/style.mjs"], "sourcesContent": ["const isCSSVar = (name) => name.startsWith(\"--\");\nconst style = {\n    set: (element, name, value) => {\n        isCSSVar(name)\n            ? element.style.setProperty(name, value)\n            : (element.style[name] = value);\n    },\n    get: (element, name) => {\n        return isCSSVar(name)\n            ? element.style.getPropertyValue(name)\n            : element.style[name];\n    },\n};\n\nexport { style };\n"], "mappings": "AAAA,MAAMA,QAAQ,GAAIC,IAAI,IAAKA,IAAI,CAACC,UAAU,CAAC,IAAI,CAAC;AAChD,MAAMC,KAAK,GAAG;EACVC,GAAG,EAAEA,CAACC,OAAO,EAAEJ,IAAI,EAAEK,KAAK,KAAK;IAC3BN,QAAQ,CAACC,IAAI,CAAC,GACRI,OAAO,CAACF,KAAK,CAACI,WAAW,CAACN,IAAI,EAAEK,KAAK,CAAC,GACrCD,OAAO,CAACF,KAAK,CAACF,IAAI,CAAC,GAAGK,KAAM;EACvC,CAAC;EACDE,GAAG,EAAEA,CAACH,OAAO,EAAEJ,IAAI,KAAK;IACpB,OAAOD,QAAQ,CAACC,IAAI,CAAC,GACfI,OAAO,CAACF,KAAK,CAACM,gBAAgB,CAACR,IAAI,CAAC,GACpCI,OAAO,CAACF,KAAK,CAACF,IAAI,CAAC;EAC7B;AACJ,CAAC;AAED,SAASE,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}