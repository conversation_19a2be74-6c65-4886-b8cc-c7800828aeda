{"ast": null, "code": "import { hover, frame } from 'motion-dom';\nimport { extractEventInfo } from '../events/event-info.mjs';\nimport { Feature } from '../motion/features/Feature.mjs';\nfunction handleHoverEvent(node, event, lifecycle) {\n  const {\n    props\n  } = node;\n  if (node.animationState && props.whileHover) {\n    node.animationState.setActive(\"whileHover\", lifecycle === \"Start\");\n  }\n  const eventName = \"onHover\" + lifecycle;\n  const callback = props[eventName];\n  if (callback) {\n    frame.postRender(() => callback(event, extractEventInfo(event)));\n  }\n}\nclass HoverGesture extends Feature {\n  mount() {\n    const {\n      current\n    } = this.node;\n    if (!current) return;\n    this.unmount = hover(current, (_element, startEvent) => {\n      handleHoverEvent(this.node, startEvent, \"Start\");\n      return endEvent => handleHoverEvent(this.node, endEvent, \"End\");\n    });\n  }\n  unmount() {}\n}\nexport { HoverGesture };", "map": {"version": 3, "names": ["hover", "frame", "extractEventInfo", "Feature", "handleHoverEvent", "node", "event", "lifecycle", "props", "animationState", "whileHover", "setActive", "eventName", "callback", "postRender", "HoverGesture", "mount", "current", "unmount", "_element", "startEvent", "endEvent"], "sources": ["D:/Desktop/projects/port1/port/frontend/node_modules/framer-motion/dist/es/gestures/hover.mjs"], "sourcesContent": ["import { hover, frame } from 'motion-dom';\nimport { extractEventInfo } from '../events/event-info.mjs';\nimport { Feature } from '../motion/features/Feature.mjs';\n\nfunction handleHoverEvent(node, event, lifecycle) {\n    const { props } = node;\n    if (node.animationState && props.whileHover) {\n        node.animationState.setActive(\"whileHover\", lifecycle === \"Start\");\n    }\n    const eventName = (\"onHover\" + lifecycle);\n    const callback = props[eventName];\n    if (callback) {\n        frame.postRender(() => callback(event, extractEventInfo(event)));\n    }\n}\nclass HoverGesture extends Feature {\n    mount() {\n        const { current } = this.node;\n        if (!current)\n            return;\n        this.unmount = hover(current, (_element, startEvent) => {\n            handleHoverEvent(this.node, startEvent, \"Start\");\n            return (endEvent) => handleHoverEvent(this.node, endEvent, \"End\");\n        });\n    }\n    unmount() { }\n}\n\nexport { HoverGesture };\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,KAAK,QAAQ,YAAY;AACzC,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,SAASC,OAAO,QAAQ,gCAAgC;AAExD,SAASC,gBAAgBA,CAACC,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAE;EAC9C,MAAM;IAAEC;EAAM,CAAC,GAAGH,IAAI;EACtB,IAAIA,IAAI,CAACI,cAAc,IAAID,KAAK,CAACE,UAAU,EAAE;IACzCL,IAAI,CAACI,cAAc,CAACE,SAAS,CAAC,YAAY,EAAEJ,SAAS,KAAK,OAAO,CAAC;EACtE;EACA,MAAMK,SAAS,GAAI,SAAS,GAAGL,SAAU;EACzC,MAAMM,QAAQ,GAAGL,KAAK,CAACI,SAAS,CAAC;EACjC,IAAIC,QAAQ,EAAE;IACVZ,KAAK,CAACa,UAAU,CAAC,MAAMD,QAAQ,CAACP,KAAK,EAAEJ,gBAAgB,CAACI,KAAK,CAAC,CAAC,CAAC;EACpE;AACJ;AACA,MAAMS,YAAY,SAASZ,OAAO,CAAC;EAC/Ba,KAAKA,CAAA,EAAG;IACJ,MAAM;MAAEC;IAAQ,CAAC,GAAG,IAAI,CAACZ,IAAI;IAC7B,IAAI,CAACY,OAAO,EACR;IACJ,IAAI,CAACC,OAAO,GAAGlB,KAAK,CAACiB,OAAO,EAAE,CAACE,QAAQ,EAAEC,UAAU,KAAK;MACpDhB,gBAAgB,CAAC,IAAI,CAACC,IAAI,EAAEe,UAAU,EAAE,OAAO,CAAC;MAChD,OAAQC,QAAQ,IAAKjB,gBAAgB,CAAC,IAAI,CAACC,IAAI,EAAEgB,QAAQ,EAAE,KAAK,CAAC;IACrE,CAAC,CAAC;EACN;EACAH,OAAOA,CAAA,EAAG,CAAE;AAChB;AAEA,SAASH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}